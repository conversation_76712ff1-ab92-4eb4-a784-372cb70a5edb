package com.example.word.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 用户进度实体类
 */
@Entity(tableName = "user_progress")
data class UserProgress(
    @PrimaryKey
    val id: Long = 1L, // 单例，只有一条记录
    
    // 学习统计
    val totalWordsLearned: Int = 0,
    val totalPhrasesLearned: Int = 0,
    val totalStudyTime: Long = 0L, // 总学习时间（毫秒）
    val studyDays: Int = 0, // 总学习天数
    val consecutiveDays: Int = 0, // 连续学习天数
    val lastStudyDate: Long = 0L, // 最后学习日期
    
    // 等级系统
    val currentLevel: Int = 1,
    val experiencePoints: Int = 0,
    val totalExperience: Int = 0,
    
    // 测验统计
    val totalQuizzes: Int = 0,
    val correctAnswers: Int = 0,
    val totalAnswers: Int = 0,
    val averageAccuracy: Float = 0f,
    
    // 成就系统
    val unlockedAchievements: String = "", // JSON字符串存储已解锁成就ID
    val totalAchievements: Int = 0,
    
    // 学习偏好
    val dailyGoal: Int = 20, // 每日学习目标
    val reminderEnabled: Boolean = true,
    val reminderTime: String = "20:00", // 提醒时间
    val autoPlayPronunciation: Boolean = true,
    val slowPronunciation: Boolean = false,
    
    // 应用设置
    val themeMode: String = "system", // light, dark, system
    val showDifficulty: Boolean = true,
    val showFrequency: Boolean = true,
    val showStreak: Boolean = true,
    val autoBackup: Boolean = false,
    
    // 时间戳
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
