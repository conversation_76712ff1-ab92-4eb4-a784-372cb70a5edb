package com.example.word.data.database;

/**
 * 词汇JSON数据加载器
 * 从assets文件夹中加载CET-4词汇数据
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0007\u001a\u00020\bJ\u0018\u0010\f\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\u0004H\u0002J\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\u0007\u001a\u00020\bJ\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\n2\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/word/data/database/VocabularyJsonLoader;", "", "()V", "TAG", "", "getVocabularyMetadata", "Lcom/example/word/data/database/VocabularyMetadata;", "context", "Landroid/content/Context;", "loadEssayTemplatesFromJson", "", "Lcom/example/word/data/entities/EssayTemplate;", "loadJsonFromAssets", "fileName", "loadPhrasesFromJson", "Lcom/example/word/data/entities/Phrase;", "loadWordsFromJson", "Lcom/example/word/data/entities/Word;", "app_debug"})
public final class VocabularyJsonLoader {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "VocabularyJsonLoader";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.data.database.VocabularyJsonLoader INSTANCE = null;
    
    private VocabularyJsonLoader() {
        super();
    }
    
    /**
     * 从JSON文件加载词汇数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.data.entities.Word> loadWordsFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 从JSON文件加载短语数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.data.entities.Phrase> loadPhrasesFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 从JSON文件加载作文模板数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.data.entities.EssayTemplate> loadEssayTemplatesFromJson(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 从assets文件夹加载JSON文件
     */
    private final java.lang.String loadJsonFromAssets(android.content.Context context, java.lang.String fileName) {
        return null;
    }
    
    /**
     * 获取词汇数据元信息
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.word.data.database.VocabularyMetadata getVocabularyMetadata(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
}