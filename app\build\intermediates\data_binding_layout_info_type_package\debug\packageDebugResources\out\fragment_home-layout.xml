<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="com.example.word" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_home_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="312" endOffset="12"/></Target><Target id="@+id/textViewWelcome" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="28" endOffset="58"/></Target><Target id="@+id/textViewDate" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="37" endOffset="48"/></Target><Target id="@+id/textViewLevel" view="TextView"><Expressions/><location startLine="61" startOffset="20" endLine="68" endOffset="64"/></Target><Target id="@+id/textViewLevelName" view="TextView"><Expressions/><location startLine="70" startOffset="20" endLine="78" endOffset="58"/></Target><Target id="@+id/textViewExperience" view="TextView"><Expressions/><location startLine="80" startOffset="20" endLine="86" endOffset="73"/></Target><Target id="@+id/progressBarExperience" view="ProgressBar"><Expressions/><location startLine="90" startOffset="16" endLine="97" endOffset="42"/></Target><Target id="@+id/textViewDailyGoal" view="TextView"><Expressions/><location startLine="132" startOffset="20" endLine="139" endOffset="73"/></Target><Target id="@+id/progressBarDaily" view="ProgressBar"><Expressions/><location startLine="143" startOffset="16" endLine="150" endOffset="42"/></Target><Target id="@+id/textViewTotalWords" view="TextView"><Expressions/><location startLine="178" startOffset="20" endLine="185" endOffset="64"/></Target><Target id="@+id/textViewReviewCount" view="TextView"><Expressions/><location startLine="213" startOffset="20" endLine="220" endOffset="66"/></Target><Target id="@+id/buttonStartStudy" view="Button"><Expressions/><location startLine="250" startOffset="12" endLine="256" endOffset="51"/></Target><Target id="@+id/buttonStartReview" view="Button"><Expressions/><location startLine="258" startOffset="12" endLine="264" endOffset="51"/></Target><Target id="@+id/buttonStartQuiz" view="Button"><Expressions/><location startLine="266" startOffset="12" endLine="272" endOffset="52"/></Target><Target id="@+id/buttonPhrases" view="Button"><Expressions/><location startLine="282" startOffset="12" endLine="289" endOffset="48"/></Target><Target id="@+id/buttonEssays" view="Button"><Expressions/><location startLine="291" startOffset="12" endLine="298" endOffset="50"/></Target><Target id="@+id/buttonViewProgress" view="Button"><Expressions/><location startLine="302" startOffset="8" endLine="308" endOffset="44"/></Target></Targets></Layout>