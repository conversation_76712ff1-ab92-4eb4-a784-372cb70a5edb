package com.example.word.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import java.io.IOException

/**
 * 在线TTS助手
 * 专门用于测试和调用在线TTS API
 */
object OnlineTTSHelper {
    
    private const val TAG = "OnlineTTSHelper"
    private const val TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech"
    private const val API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7"
    
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(10, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    /**
     * 测试API连接
     */
    fun testAPIConnection(context: Context, callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = TTS_API_URL.toHttpUrl().newBuilder()
                    .addQueryParameter("key", API_KEY)
                    .addQueryParameter("text", "hello")
                    .build()
                

                
                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "WordLearningApp/1.0")
                    .get()
                    .build()
                
                Log.d(TAG, "Testing API connection to: $url")
                
                httpClient.newCall(request).execute().use { response ->
                    val responseCode = response.code
                    val responseBody = response.body?.string() ?: ""
                    
                    Log.d(TAG, "API Response Code: $responseCode")
                    Log.d(TAG, "API Response Body: $responseBody")
                    
                    withContext(Dispatchers.Main) {
                        if (response.isSuccessful) {
                            callback(true, "API连接成功 (${responseCode})")
                        } else {
                            callback(false, "API连接失败: $responseCode - ${response.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "API connection test failed", e)
                withContext(Dispatchers.Main) {
                    callback(false, "API连接异常: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 获取TTS音频
     */
    fun getTTSAudio(
        text: String, 
        language: String = "en",
        speed: String = "normal",
        callback: (Boolean, String, ByteArray?) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = TTS_API_URL.toHttpUrl().newBuilder()
                    .addQueryParameter("key", API_KEY)
                    .addQueryParameter("text", text)
                    .addQueryParameter("lang", language)
                    .addQueryParameter("speed", speed)
                    .build()
                
                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "WordLearningApp/1.0")
                    .get()
                    .build()
                
                Log.d(TAG, "Requesting TTS for: $text")
                Log.d(TAG, "Request URL: $url")
                
                httpClient.newCall(request).execute().use { response ->
                    val responseCode = response.code

                    if (response.isSuccessful) {
                        val contentType = response.header("Content-Type") ?: ""
                        Log.d(TAG, "Response Content-Type: $contentType")

                        if (contentType.contains("audio") || contentType.contains("octet-stream")) {
                            // 直接返回音频数据
                            val audioData = response.body?.bytes()
                            withContext(Dispatchers.Main) {
                                if (audioData != null && audioData.isNotEmpty()) {
                                    callback(true, "获取音频成功", audioData)
                                } else {
                                    callback(false, "音频数据为空", null)
                                }
                            }
                        } else {
                            // 可能是JSON响应
                            val responseBody = response.body?.string() ?: ""
                            Log.d(TAG, "JSON Response: $responseBody")

                            withContext(Dispatchers.Main) {
                                // 这里可以解析JSON获取音频URL
                                callback(false, "需要解析JSON响应: $responseBody", null)
                            }
                        }
                    } else {
                        val errorBody = response.body?.string() ?: ""
                        Log.e(TAG, "TTS request failed: $responseCode - $errorBody")
                        
                        withContext(Dispatchers.Main) {
                            callback(false, "TTS请求失败: $responseCode - $errorBody", null)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "TTS request exception", e)
                withContext(Dispatchers.Main) {
                    callback(false, "TTS请求异常: ${e.message}", null)
                }
            }
        }
    }
    
    /**
     * 简单的API测试方法
     */
    fun simpleAPITest(callback: (String) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 构建最简单的请求
                val url = "$TTS_API_URL?key=$API_KEY"
                
                val request = Request.Builder()
                    .url(url)
                    .get()
                    .build()
                
                Log.d(TAG, "Simple API test URL: $url")
                
                httpClient.newCall(request).execute().use { response ->
                    val result = buildString {
                        appendLine("=== API测试结果 ===")
                        appendLine("URL: $url")
                        appendLine("响应码: ${response.code}")
                        appendLine("响应消息: ${response.message}")
                        appendLine("Content-Type: ${response.header("Content-Type")}")
                        appendLine("Content-Length: ${response.header("Content-Length")}")

                        val body = response.body?.string()
                        if (body != null) {
                            appendLine("响应体长度: ${body.length}")
                            appendLine("响应体前100字符: ${body.take(100)}")
                        }
                    }
                    
                    Log.d(TAG, result)
                    
                    withContext(Dispatchers.Main) {
                        callback(result)
                    }
                }
            } catch (e: Exception) {
                val errorResult = "API测试异常: ${e.message}"
                Log.e(TAG, errorResult, e)
                withContext(Dispatchers.Main) {
                    callback(errorResult)
                }
            }
        }
    }
}
