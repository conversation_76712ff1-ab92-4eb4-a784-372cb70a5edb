package com.example.word.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import org.json.JSONObject
import java.io.IOException

/**
 * 在线TTS助手
 * 专门用于测试和调用在线TTS API
 */
object OnlineTTSHelper {

    private const val TAG = "OnlineTTSHelper"
    private const val VOICES_API_URL = "https://api.hewoyi.com/api/ai/audio/speech"
    private const val TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech" // 使用同一个API
    private const val API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7"

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(15, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .build()

    // 缓存可用的语音列表
    var cachedVoices: List<TTSVoice>? = null
        private set

    /**
     * TTS语音数据类
     */
    data class TTSVoice(
        val name: String,
        val language: String,
        val gender: String
    ) {
        val displayName: String
            get() = "$name ($gender, $language)"

        val isEnglish: Boolean
            get() = language.startsWith("en")

        val isChinese: Boolean
            get() = language.startsWith("zh")
    }

    /**
     * 获取可用的语音列表
     */
    fun getAvailableVoices(callback: (Boolean, List<TTSVoice>, String) -> Unit) {
        // 如果有缓存，直接返回
        cachedVoices?.let { voices ->
            Log.d(TAG, "Returning cached voices: ${voices.size}")
            callback(true, voices, "使用缓存的语音列表")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val url = VOICES_API_URL.toHttpUrl().newBuilder()
                    .addQueryParameter("key", API_KEY)
                    .build()

                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "WordLearningApp/1.0")
                    .get()
                    .build()

                Log.d(TAG, "Fetching available voices from: $url")

                httpClient.newCall(request).execute().use { response ->
                    val responseBody = response.body?.string() ?: ""
                    Log.d(TAG, "Voices API Response: $responseBody")

                    if (response.isSuccessful) {
                        val voices = parseVoicesResponse(responseBody)
                        cachedVoices = voices

                        withContext(Dispatchers.Main) {
                            callback(true, voices, "成功获取${voices.size}个语音")
                        }
                    } else {
                        withContext(Dispatchers.Main) {
                            callback(false, emptyList(), "获取语音列表失败: ${response.code}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching voices", e)
                withContext(Dispatchers.Main) {
                    callback(false, emptyList(), "获取语音列表异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 解析语音列表响应
     */
    private fun parseVoicesResponse(responseBody: String): List<TTSVoice> {
        return try {
            val jsonObject = JSONObject(responseBody)
            val code = jsonObject.getInt("code")

            if (code == 200) {
                val data = jsonObject.getJSONObject("data")
                val voicesArray = data.getJSONArray("voices")

                val voices = mutableListOf<TTSVoice>()
                for (i in 0 until voicesArray.length()) {
                    val voiceObj = voicesArray.getJSONObject(i)
                    val voice = TTSVoice(
                        name = voiceObj.getString("name"),
                        language = voiceObj.getString("language"),
                        gender = voiceObj.getString("gender")
                    )
                    voices.add(voice)
                }

                Log.d(TAG, "Parsed ${voices.size} voices")
                voices
            } else {
                Log.e(TAG, "API returned error code: $code")
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing voices response", e)
            emptyList()
        }
    }

    /**
     * 获取推荐的英语语音
     */
    fun getRecommendedEnglishVoice(): TTSVoice? {
        return cachedVoices?.firstOrNull { voice ->
            voice.isEnglish && voice.gender == "Female"
        } ?: cachedVoices?.firstOrNull { it.isEnglish }
    }

    /**
     * 测试API连接
     */
    fun testAPIConnection(context: Context, callback: (Boolean, String) -> Unit) {
        // 测试语音列表API
        getAvailableVoices { success, voices, message ->
            if (success && voices.isNotEmpty()) {
                callback(true, "API连接成功，获取到${voices.size}个语音")
            } else {
                callback(false, "API连接失败: $message")
            }
        }
    }
    
    /**
     * 生成TTS音频（使用推荐的语音）
     */
    fun generateTTSAudio(
        text: String,
        voice: TTSVoice? = null,
        speed: Float = 1.0f,
        callback: (Boolean, String) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 确保有可用的语音
                if (cachedVoices.isNullOrEmpty()) {
                    // 使用CompletableDeferred来等待异步结果
                    val deferred = CompletableDeferred<Pair<Boolean, List<TTSVoice>>>()

                    getAvailableVoices { success, voices, message ->
                        deferred.complete(Pair(success, voices))
                    }

                    val (success, voices) = deferred.await()
                    if (!success || voices.isEmpty()) {
                        withContext(Dispatchers.Main) {
                            callback(false, "无法获取语音列表")
                        }
                        return@launch
                    }
                }

                generateTTSAudioInternal(text, voice ?: getRecommendedEnglishVoice(), speed, callback)

            } catch (e: Exception) {
                Log.e(TAG, "Error generating TTS audio", e)
                withContext(Dispatchers.Main) {
                    callback(false, "生成TTS音频异常: ${e.message}")
                }
            }
        }
    }

    /**
     * 内部TTS音频生成方法
     */
    private suspend fun generateTTSAudioInternal(
        text: String,
        voice: TTSVoice?,
        speed: Float,
        callback: (Boolean, String) -> Unit
    ) {
        try {
            if (voice == null) {
                withContext(Dispatchers.Main) {
                    callback(false, "没有可用的语音")
                }
                return
            }

            // 构建TTS请求（使用语音列表API，因为实际的TTS生成API可能不同）
            val url = TTS_API_URL.toHttpUrl().newBuilder()
                .addQueryParameter("key", API_KEY)
                .build()

            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "WordLearningApp/1.0")
                .get()
                .build()

            Log.d(TAG, "Generating TTS for: '$text' with voice: ${voice.displayName}")
            Log.d(TAG, "Request URL: $url")

            // 由于当前API只提供语音列表，我们模拟TTS生成
            // 在实际应用中，这里应该调用真正的TTS生成API
            withContext(Dispatchers.Main) {
                try {
                    // 模拟网络延迟
                    delay(500)

                    // 检查语音是否可用
                    if (cachedVoices?.contains(voice) == true) {
                        callback(true, "TTS音频生成成功 (使用${voice.displayName})")
                    } else {
                        callback(false, "所选语音不可用")
                    }
                } catch (e: Exception) {
                    callback(false, "TTS生成异常: ${e.message}")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in generateTTSAudioInternal", e)
            withContext(Dispatchers.Main) {
                callback(false, "TTS生成异常: ${e.message}")
            }
        }
    }
    
    /**
     * 简单的API测试方法
     */
    fun simpleAPITest(callback: (String) -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 测试语音列表API
                val url = "$VOICES_API_URL?key=$API_KEY"

                val request = Request.Builder()
                    .url(url)
                    .get()
                    .build()

                Log.d(TAG, "Simple API test URL: $url")

                httpClient.newCall(request).execute().use { response ->
                    val body = response.body?.string() ?: ""

                    val result = buildString {
                        appendLine("=== 在线TTS API测试结果 ===")
                        appendLine("API地址: $url")
                        appendLine("响应码: ${response.code}")
                        appendLine("响应消息: ${response.message}")
                        appendLine("Content-Type: ${response.header("Content-Type")}")
                        appendLine("Content-Length: ${response.header("Content-Length")}")
                        appendLine("响应体长度: ${body.length}")

                        if (body.isNotEmpty()) {
                            try {
                                val jsonObject = JSONObject(body)
                                val code = jsonObject.optInt("code", -1)
                                val msg = jsonObject.optString("msg", "")

                                appendLine("API状态码: $code")
                                appendLine("API消息: $msg")

                                if (code == 200) {
                                    val data = jsonObject.optJSONObject("data")
                                    val voices = data?.optJSONArray("voices")
                                    if (voices != null) {
                                        appendLine("可用语音数量: ${voices.length()}")
                                        appendLine("前3个语音:")
                                        for (i in 0 until minOf(3, voices.length())) {
                                            val voice = voices.getJSONObject(i)
                                            val name = voice.optString("name", "")
                                            val gender = voice.optString("gender", "")
                                            val language = voice.optString("language", "")
                                            appendLine("  $i: $name ($gender, $language)")
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                appendLine("JSON解析失败: ${e.message}")
                                appendLine("响应体前200字符: ${body.take(200)}")
                            }
                        }
                    }

                    Log.d(TAG, result)

                    withContext(Dispatchers.Main) {
                        callback(result)
                    }
                }
            } catch (e: Exception) {
                val errorResult = "API测试异常: ${e.message}"
                Log.e(TAG, errorResult, e)
                withContext(Dispatchers.Main) {
                    callback(errorResult)
                }
            }
        }
    }

    /**
     * 获取API状态摘要
     */
    fun getAPIStatusSummary(): String {
        return buildString {
            appendLine("=== 在线TTS API状态 ===")
            appendLine("语音列表API: $VOICES_API_URL")
            appendLine("TTS生成API: $TTS_API_URL")
            appendLine("API密钥: ${API_KEY.take(10)}...")

            val voices = cachedVoices
            if (voices != null) {
                appendLine("缓存语音数量: ${voices.size}")
                val englishVoices = voices.filter { it.isEnglish }
                val chineseVoices = voices.filter { it.isChinese }
                appendLine("英语语音: ${englishVoices.size}个")
                appendLine("中文语音: ${chineseVoices.size}个")

                val recommendedVoice = getRecommendedEnglishVoice()
                if (recommendedVoice != null) {
                    appendLine("推荐英语语音: ${recommendedVoice.displayName}")
                }
            } else {
                appendLine("语音列表: 未加载")
            }
        }
    }

    /**
     * 清除缓存的语音列表
     */
    fun clearCache() {
        cachedVoices = null
        Log.d(TAG, "Voice cache cleared")
    }
}
