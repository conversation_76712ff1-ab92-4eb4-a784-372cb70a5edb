package com.example.word.utils;

/**
 * 增强的在线TTS管理器
 * 集成OnlineTTSHelper，提供完整的在线TTS解决方案
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 \u001e2\u00020\u0001:\u0001\u001eB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ \u0010\u0010\u001a\u00020\u00112\u0018\u0010\u0012\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00110\u0013J\u0006\u0010\u0014\u001a\u00020\tJ \u0010\u0015\u001a\u00020\u00112\u0018\u0010\u0012\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00110\u0013J\u0006\u0010\u0016\u001a\u00020\u0011J\u000e\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0007J6\u0010\u0019\u001a\u00020\u00112\u0006\u0010\u001a\u001a\u00020\u000f2\b\b\u0002\u0010\u001b\u001a\u00020\t2\u001c\b\u0002\u0010\u0012\u001a\u0016\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0013J\u001a\u0010\u001c\u001a\u00020\u00112\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00110\u001dR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/example/word/utils/EnhancedOnlineTTSManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "availableVoices", "", "Lcom/example/word/utils/OnlineTTSHelper$TTSVoice;", "isInitialized", "", "preferredVoice", "getAvailableVoices", "getChineseVoices", "getEnglishVoices", "getStatus", "", "initialize", "", "callback", "Lkotlin/Function2;", "isReady", "reinitialize", "release", "setPreferredVoice", "voice", "speak", "text", "slowSpeed", "testOnlineTTS", "Lkotlin/Function1;", "Companion", "app_debug"})
public final class EnhancedOnlineTTSManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "EnhancedOnlineTTSManager";
    private boolean isInitialized = false;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> availableVoices;
    @org.jetbrains.annotations.Nullable()
    private com.example.word.utils.OnlineTTSHelper.TTSVoice preferredVoice;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.EnhancedOnlineTTSManager.Companion Companion = null;
    
    public EnhancedOnlineTTSManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 初始化在线TTS
     */
    public final void initialize(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 播放文本
     */
    public final void speak(@org.jetbrains.annotations.NotNull()
    java.lang.String text, boolean slowSpeed, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 测试在线TTS功能
     */
    public final void testOnlineTTS(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 获取状态信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getStatus() {
        return null;
    }
    
    /**
     * 设置首选语音
     */
    public final void setPreferredVoice(@org.jetbrains.annotations.NotNull()
    com.example.word.utils.OnlineTTSHelper.TTSVoice voice) {
    }
    
    /**
     * 获取可用语音列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> getAvailableVoices() {
        return null;
    }
    
    /**
     * 获取英语语音列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> getEnglishVoices() {
        return null;
    }
    
    /**
     * 获取中文语音列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> getChineseVoices() {
        return null;
    }
    
    /**
     * 检查是否已初始化
     */
    public final boolean isReady() {
        return false;
    }
    
    /**
     * 重新初始化
     */
    public final void reinitialize(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 释放资源
     */
    public final void release() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/word/utils/EnhancedOnlineTTSManager$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}