<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 欢迎信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <TextView
                android:id="@+id/textViewWelcome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="早上好！"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface" />

            <TextView
                android:id="@+id/textViewDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2024年1月1日 周一"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- 用户等级信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/textViewLevel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Lv.1"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorPrimary" />

                    <TextView
                        android:id="@+id/textViewLevelName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="初学者"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurface"
                        android:layout_marginStart="8dp" />

                    <TextView
                        android:id="@+id/textViewExperience"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0/100"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

                <ProgressBar
                    android:id="@+id/progressBarExperience"
                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="8dp"
                    android:max="100"
                    android:progress="0" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 今日目标 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="今日目标"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:id="@+id/textViewDailyGoal"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="0 / 20"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

                <ProgressBar
                    android:id="@+id/progressBarDaily"
                    style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="8dp"
                    android:layout_marginTop="8dp"
                    android:max="20"
                    android:progress="0" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 学习统计 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/textViewTotalWords"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="总词汇"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/textViewReviewCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorSecondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="待复习"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- 快速开始 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快速开始"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/buttonStartStudy"
                style="@style/Widget.Material3.Button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="开始学习"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/buttonStartReview"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="开始复习"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/buttonStartQuiz"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="开始测验"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

        <!-- 其他功能 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/buttonPhrases"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="短语"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/buttonEssays"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="作文"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <Button
            android:id="@+id/buttonViewProgress"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="查看详细进度"
            android:layout_marginTop="8dp" />

    </LinearLayout>

</ScrollView>
