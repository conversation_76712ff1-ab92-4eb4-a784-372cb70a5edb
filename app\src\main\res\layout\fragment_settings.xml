<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 学习设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="学习设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <!-- 每日目标 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="每日学习目标"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <Spinner
                        android:id="@+id/spinner_daily_goal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="100dp" />

                </LinearLayout>

                <!-- 复习模式 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="复习模式"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <Spinner
                        android:id="@+id/spinner_review_mode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="120dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 提醒设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="提醒设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <!-- 学习提醒开关 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="学习提醒"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_reminder"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 提醒时间 -->
                <LinearLayout
                    android:id="@+id/layout_reminder_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="提醒时间"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <TextView
                        android:id="@+id/text_view_reminder_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="20:00"
                        android:textSize="16sp"
                        android:textColor="?attr/colorPrimary"
                        tools:text="20:00" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 发音设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="发音设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <!-- 自动播放发音 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="自动播放发音"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_auto_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 慢速发音 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="慢速发音"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_slow_speed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 主题设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="主题设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <RadioGroup
                    android:id="@+id/radio_group_theme"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/radio_button_system"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="跟随系统"
                        android:textSize="16sp"
                        android:checked="true" />

                    <RadioButton
                        android:id="@+id/radio_button_light"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="浅色主题"
                        android:textSize="16sp" />

                    <RadioButton
                        android:id="@+id/radio_button_dark"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="深色主题"
                        android:textSize="16sp" />

                </RadioGroup>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 统计设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="统计设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <!-- 显示学习进度 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="显示学习进度"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_show_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 显示连续学习天数 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="显示连续学习天数"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_show_streak"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 数据管理 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="数据管理"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <!-- 自动备份 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="自动备份"
                        android:textSize="16sp"
                        android:textColor="?android:attr/textColorPrimary" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_auto_backup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 数据操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/button_export_data"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="导出数据"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/button_import_data"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="导入数据"
                        android:textSize="14sp" />

                </LinearLayout>

                <Button
                    android:id="@+id/button_clear_data"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="清除所有数据"
                    android:textColor="@android:color/holo_red_dark"
                    android:textSize="14sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 关于 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="关于"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:attr/textColorPrimary" />

                <Button
                    android:id="@+id/button_about"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="关于应用"
                    android:textSize="14sp" />

                <Button
                    android:id="@+id/buttonTestTTS"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="测试TTS API"
                    android:textSize="14sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
