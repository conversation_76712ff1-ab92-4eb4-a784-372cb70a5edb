--------- beginning of crash
--------- beginning of system
2025-06-01 21:24:48.845 32736-306   ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 21:24:48.845 32736-306   ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
2025-06-01 21:24:48.875 32736-32736 Looper                  com.example.word                     W  PerfMonitor looperActivity : package=com.example.word/.MainActivity time=0ms latency=609ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=1 wall=62ms seq=1 late=6ms h=android.app.ActivityThread$H w=162) (msgIndex=3 wall=189ms seq=3 late=65ms h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=355ms seq=4 late=254ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:24:49.159 32736-32736 Loop<PERSON>                  com.example.word                     W  PerfMonitor doFrame : time=283ms vsyncFrame=0 latency=358ms procState=-1 historyMsgCount=4 (msgIndex=1 wall=189ms seq=3 late=65ms h=android.app.ActivityThread$H w=110) (msgIndex=2 wall=355ms seq=4 late=254ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:29:57.409  3189-3232  ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 21:29:57.411  3189-3232  ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
2025-06-01 21:29:57.442  3189-3189  Looper                  com.example.word                     W  PerfMonitor looperActivity : package=com.example.word/.MainActivity time=0ms latency=644ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=1 wall=70ms seq=1 late=18ms h=android.app.ActivityThread$H w=162) (msgIndex=3 wall=200ms seq=3 late=85ms h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=368ms seq=4 late=277ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:29:57.586  3189-3189  Looper                  com.example.word                     W  PerfMonitor doFrame : time=143ms vsyncFrame=0 latency=379ms procState=-1 historyMsgCount=4 (msgIndex=1 wall=200ms seq=3 late=85ms h=android.app.ActivityThread$H w=110) (msgIndex=2 wall=368ms seq=4 late=277ms h=android.app.ActivityThread$H w=159)
--------- beginning of main
2025-06-01 21:48:02.551  7911-7911  om.example.word         com.example.word                     I  Late-enabling -Xcheck:jni
2025-06-01 21:48:02.622  7911-7911  MessageMonitor          com.example.word                     I  Load libmiui_runtime
2025-06-01 21:48:02.663  7911-7928  AppScoutStateMachine    com.example.word                     D  7911-ScoutStateMachinecreated
2025-06-01 21:48:02.672  7911-7911  re-initialized>         com.example.word                     W  type=1400 audit(0.0:235826): avc: granted { execute } for path="/data/data/com.example.word/code_cache/startup_agents/4aa0b38c-agent.so" dev="dm-46" ino=4503449 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c255,c256,c512,c768 tclass=file app=com.example.word
2025-06-01 21:48:02.682  7911-7911  om.example.word         com.example.word                     W  DexFile /data/data/com.example.word/code_cache/.studio/instruments-dd2db83e.jar is in boot class path but is not in a known location
2025-06-01 21:48:02.694  7911-7911  om.example.word         com.example.word                     W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-06-01 21:48:02.694  7911-7911  om.example.word         com.example.word                     W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-06-01 21:48:02.696  7911-7911  Compatibil...geReporter com.example.word                     D  Compat change id reported: 171979766; UID 10255; state: ENABLED
2025-06-01 21:48:02.712  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes10.dm': No such file or directory
2025-06-01 21:48:02.712  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-06-01 21:48:02.713  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes7.dm': No such file or directory
2025-06-01 21:48:02.713  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes5.dm': No such file or directory
2025-06-01 21:48:02.714  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
2025-06-01 21:48:02.714  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/app/~~LwWHvx7Bd9q2I6lV4dyHkg==/com.example.word-MroSVOuWnubLntNeKYp4Ug==/base.dm': No such file or directory
2025-06-01 21:48:02.714  7911-7911  ziparchive              com.example.word                     W  Unable to open '/data/app/~~LwWHvx7Bd9q2I6lV4dyHkg==/com.example.word-MroSVOuWnubLntNeKYp4Ug==/base.dm': No such file or directory
2025-06-01 21:48:02.852  7911-7911  GraphicsEnvironment     com.example.word                     V  ANGLE Developer option for 'com.example.word' set to: 'default'
2025-06-01 21:48:02.852  7911-7911  GraphicsEnvironment     com.example.word                     V  ANGLE GameManagerService for com.example.word: false
2025-06-01 21:48:02.852  7911-7911  GraphicsEnvironment     com.example.word                     V  App is not on the allowlist for updatable production driver.
2025-06-01 21:48:02.855  7911-7911  ForceDarkHelperStubImpl com.example.word                     I  initialize for com.example.word , ForceDarkOrigin
2025-06-01 21:48:02.855  7911-7911  om.example.word         com.example.word                     D  JNI_OnLoad success
2025-06-01 21:48:02.856  7911-7911  MiuiForceDarkConfig     com.example.word                     I  setConfig density:2.750000, mainRule:0, secondaryRule:0, tertiaryRule:0
2025-06-01 21:48:02.857  7911-7911  NetworkSecurityConfig   com.example.word                     D  No Network Security Config specified, using platform default
2025-06-01 21:48:02.858  7911-7911  NetworkSecurityConfig   com.example.word                     D  No Network Security Config specified, using platform default
2025-06-01 21:48:02.859  7911-7911  Compatibil...geReporter com.example.word                     D  Compat change id reported: 183155436; UID 10255; state: ENABLED
2025-06-01 21:48:02.878  7911-7911  WordApplication         com.example.word                     D  Application starting
2025-06-01 21:48:02.879  7911-7911  ErrorHandler            com.example.word                     D  Initializing error handler
2025-06-01 21:48:02.882  7911-7911  WordApplication         com.example.word                     D  Application initialized successfully
2025-06-01 21:48:02.884  7911-7911  MSYNC3-Var...efreshRate com.example.word                     I  Variable refreshrate is disabled
2025-06-01 21:48:02.890  7911-7944  PowerHalWrapper         com.example.word                     I  PowerHalWrapper.getInstance 
2025-06-01 21:48:02.891  7911-7911  MiuiMultiWindowAdapter  com.example.word                     D  MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
2025-06-01 21:48:02.896  7911-7949  libMEOW                 com.example.word                     D  meow new tls: 0xb400007483b4a200
2025-06-01 21:48:02.898  7911-7949  libMEOW                 com.example.word                     D  meow reload base cfg path: na
2025-06-01 21:48:02.898  7911-7949  libMEOW                 com.example.word                     D  meow reload overlay cfg path: na
2025-06-01 21:48:02.898  7911-7949  QT                      com.example.word                     W  qt_process_init() called
2025-06-01 21:48:02.898  7911-7949  QT                      com.example.word                     E  [QT]file does not exist
2025-06-01 21:48:02.898  7911-7949  QT                      com.example.word                     W  Support!!
2025-06-01 21:48:02.899  7911-7949  QT                      com.example.word                     E  [QT]file does not exist
2025-06-01 21:48:02.899  7911-7949  libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:48:02.899  7911-7949  libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:48:02.899  7911-7949  libMEOW                 com.example.word                     D  meow delete tls: 0xb400007483b4a200
2025-06-01 21:48:02.910  7911-7911  AppCompatDelegate       com.example.word                     D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-06-01 21:48:02.972  7911-7911  libc                    com.example.word                     W  Access denied finding property "ro.vendor.df.effect.conflict"
2025-06-01 21:48:02.968  7911-7911  om.example.word         com.example.word                     W  type=1400 audit(0.0:235827): avc: denied { read } for name="u:object_r:vendor_displayfeature_prop:s0" dev="tmpfs" ino=442 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:vendor_displayfeature_prop:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:03.003  7911-7950  ViewContentFactory      com.example.word                     D  initViewContentFetcherClass
2025-06-01 21:48:03.003  7911-7950  ViewContentFactory      com.example.word                     D  getInterceptorPackageInfo
2025-06-01 21:48:03.003  7911-7950  ViewContentFactory      com.example.word                     D  getInitialApplication took 1ms
2025-06-01 21:48:03.003  7911-7950  ViewContentFactory      com.example.word                     D  packageInfo.packageName: com.miui.catcherpatch
2025-06-01 21:48:03.010  7911-7950  ViewContentFactory      com.example.word                     D  initViewContentFetcherClass took 7ms
2025-06-01 21:48:03.010  7911-7950  ContentCatcher          com.example.word                     I  ViewContentFetcher : ViewContentFetcher
2025-06-01 21:48:03.010  7911-7950  ViewContentFactory      com.example.word                     D  createInterceptor took 7ms
2025-06-01 21:48:03.011  7911-7950  ContentCatcher          com.example.word                     I  Interceptor : Catcher list <NAME_EMAIL>@264623960
2025-06-01 21:48:03.012  7911-7950  ContentCatcher          com.example.word                     I  Interceptor : Get featureInfo from config image_pick_mode
2025-06-01 21:48:03.012  7911-7950  ContentCatcher          com.example.word                     I  Interceptor : Get featureInfo from config pick_mode
2025-06-01 21:48:03.065  7911-7911  Compatibil...geReporter com.example.word                     D  Compat change id reported: 210923482; UID 10255; state: ENABLED
2025-06-01 21:48:03.109  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:03.109  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:03.114  7911-7911  DecorView[]             com.example.word                     D  getWindowModeFromSystem  windowmode is 1
2025-06-01 21:48:03.121  7911-7911  om.example.word         com.example.word                     W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
2025-06-01 21:48:03.123  7911-7911  PerformanceMonitor      com.example.word                     D  Starting performance monitoring...
2025-06-01 21:48:03.132  7911-7911  MainActivity            com.example.word                     D  Performance monitoring started
2025-06-01 21:48:03.133  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 2% (6MB)
2025-06-01 21:48:03.135  7911-7911  AppOptimizer            com.example.word                     D  Performing startup optimization...
2025-06-01 21:48:03.137  7911-7911  AppOptimizer            com.example.word                     D  Health check needed: true (last: 0, current: 1748785683137)
2025-06-01 21:48:03.137  7911-7911  AppOptimizer            com.example.word                     D  TTS initialization needed: false
2025-06-01 21:48:03.137  7911-7911  AppOptimizer            com.example.word                     D  Database check needed: false
2025-06-01 21:48:03.138  7911-7911  AppOptimizer            com.example.word                     D  Startup optimization result: StartupOptimizationResult(needsHealthCheck=true, needsTTSInit=false, needsDatabaseCheck=false, optimizations=[跳过TTS初始化（已完成）, 跳过数据库检查（已完成）])
2025-06-01 21:48:03.138  7911-7911  Compatibil...geReporter com.example.word                     D  Compat change id reported: 147798919; UID 10255; state: ENABLED
2025-06-01 21:48:03.143  7911-7951  AppHealthChecker        com.example.word                     D  === Starting App Health Check ===
2025-06-01 21:48:03.143  7911-7951  AppHealthChecker        com.example.word                     D  ✓ 1.txt文件正常
2025-06-01 21:48:03.144  7911-7951  DatabaseDebugHelper     com.example.word                     D  Testing data organization...
2025-06-01 21:48:03.145  7911-7951  DataOrganizer           com.example.word                     D  Starting to organize data from 1.txt
2025-06-01 21:48:03.146  7911-7951  DataOrganizer           com.example.word                     D  Found high frequency words section
2025-06-01 21:48:03.149  7911-7911  Compatibil...geReporter com.example.word                     D  Compat change id reported: 171228096; UID 10255; state: ENABLED
2025-06-01 21:48:03.168  7911-7951  DataOrganizer           com.example.word                     D  Found phrases section
2025-06-01 21:48:03.209  7911-7911  WordDatabase            com.example.word                     D  Creating database instance
2025-06-01 21:48:03.215  7911-7911  WordDatabase            com.example.word                     D  Database instance created successfully
2025-06-01 21:48:03.222  7911-7951  DataOrganizer           com.example.word                     D  Found essay sentences section
2025-06-01 21:48:03.225  7911-7950  ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 21:48:03.225  7911-7950  ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
2025-06-01 21:48:03.230  7911-7911  SurfaceFactory          com.example.word                     I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@1fa5f46
2025-06-01 21:48:03.237  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:03.237  7911-7951  DataOrganizer           com.example.word                     D  Found essay templates section
2025-06-01 21:48:03.241  7911-7956  WordDatabase            com.example.word                     D  Database opened, version: 2
2025-06-01 21:48:03.243  7911-7911  libMEOW                 com.example.word                     D  meow new tls: 0xb4000074241eb9c0
2025-06-01 21:48:03.243  7911-7911  libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:48:03.243  7911-7911  libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:48:03.246  7911-7911  Looper                  com.example.word                     W  PerfMonitor looperActivity : package=com.example.word/.MainActivity time=1ms latency=605ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=1 wall=53ms seq=1 late=10ms h=android.app.ActivityThread$H w=162) (msgIndex=3 wall=187ms seq=3 late=60ms h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=361ms seq=4 late=244ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:48:03.283  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000000,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:03.285  7911-7911  FBI                     com.example.word                     E  Can't load library: dlopen failed: library "libmagtsync.so" not found
2025-06-01 21:48:03.287  7911-7946  libMEOW                 com.example.word                     D  meow new tls: 0xb40000740dd42880
2025-06-01 21:48:03.287  7911-7946  libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:48:03.287  7911-7946  libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:48:03.293  7911-7951  DataOrganizer           com.example.word                     D  Found CET-4 words section
2025-06-01 21:48:03.318  7911-7946  libc                    com.example.word                     W  Access denied finding property "vendor.migl.debug"
2025-06-01 21:48:03.318  7911-7946  libMiGL                 com.example.word                     E  libmigl:This GPU version is note support Variable Shading Rate
2025-06-01 21:48:03.320  7911-7946  libEGL                  com.example.word                     E  pre_cache appList: ,,
2025-06-01 21:48:03.357  7911-7946  om.example.word         com.example.word                     D  MiuiProcessManagerServiceStub setSchedFifo
2025-06-01 21:48:03.357  7911-7946  MiuiProcessManagerImpl  com.example.word                     I  setSchedFifo pid:7911, mode:3
2025-06-01 21:48:03.377  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.378  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1080x2460 mFrameNumber=1 applyTransaction=true mTimestamp=263681013424562(auto) mPendingTransactions.size=0 graphicBufferId=33977486278659 transform=0
2025-06-01 21:48:03.379  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:03.380  7911-7911  Looper                  com.example.word                     W  PerfMonitor doFrame : time=132ms vsyncFrame=0 latency=369ms procState=-1 historyMsgCount=4 (msgIndex=1 wall=187ms seq=3 late=60ms h=android.app.ActivityThread$H w=110) (msgIndex=2 wall=361ms seq=4 late=244ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:48:03.380  7911-7911  PerformanceMonitor      com.example.word                     W  Main thread blocked for 247ms
2025-06-01 21:48:03.385  7911-7951  DataOrganizer           com.example.word                     D  Data organization completed:
2025-06-01 21:48:03.385  7911-7951  DataOrganizer           com.example.word                     D    Words: 1175
2025-06-01 21:48:03.385  7911-7951  DataOrganizer           com.example.word                     D    Phrases: 263
2025-06-01 21:48:03.385  7911-7951  DataOrganizer           com.example.word                     D    Essay Templates: 0
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D  Data organization results:
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D    Words organized: 1175
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D    Phrases organized: 263
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D    Templates organized: 0
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D    First word: available - 可利用的，可得到
2025-06-01 21:48:03.385  7911-7951  DatabaseDebugHelper     com.example.word                     D    First phrase: a series of - 一系列,一连串
2025-06-01 21:48:03.386  7911-7951  AppHealthChecker        com.example.word                     D  ✓ 数据解析正常 (1175词汇, 263短语)
2025-06-01 21:48:03.390  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.398  7911-7951  DatabaseDebugHelper     com.example.word                     D  Database Status:
2025-06-01 21:48:03.398  7911-7951  DatabaseDebugHelper     com.example.word                     D    Words: 1175
2025-06-01 21:48:03.398  7911-7951  DatabaseDebugHelper     com.example.word                     D    Phrases: 263
2025-06-01 21:48:03.399  7911-7951  DatabaseDebugHelper     com.example.word                     D    Templates: 3
2025-06-01 21:48:03.399  7911-7951  DatabaseDebugHelper     com.example.word                     D    Initialized: true
2025-06-01 21:48:03.399  7911-7951  DatabaseDebugHelper     com.example.word                     D    Version: 2.0
2025-06-01 21:48:03.399  7911-7951  AppHealthChecker        com.example.word                     D  ✓ 数据库状态正常
2025-06-01 21:48:03.400  7911-7951  SimpleTTSManager        com.example.word                     D  Initializing TTS...
2025-06-01 21:48:03.405  7911-7951  SimpleTTSManager        com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:48:03.416  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.417  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:03.419  7911-7911  HandWritingStubImpl     com.example.word                     I  refreshLastKeyboardType: 1
2025-06-01 21:48:03.419  7911-7911  HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:48:03.427  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.430  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.439  7911-7911  HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:48:03.448  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.464  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.502  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.504  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:03.509  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.406  7911-7951  TextToSpeech            com.example.word                     W  stop failed: not bound to TTS engine
2025-06-01 21:48:04.406  7911-7951  TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:48:04.406  7911-7951  SimpleTTSManager        com.example.word                     D  TTS resources released
2025-06-01 21:48:04.406  7911-7951  AppHealthChecker        com.example.word                     D  ⚠ TTS不可用，将使用备用方案
2025-06-01 21:48:04.406  7911-7951  AppHealthChecker        com.example.word                     D  ✓ 导航配置正常
2025-06-01 21:48:04.406  7911-7951  AppHealthChecker        com.example.word                     D  === Health Check Completed ===
2025-06-01 21:48:04.407  7911-7951  AppHealthChecker        com.example.word                     D  Issues found: 1
2025-06-01 21:48:04.407  7911-7951  AppHealthChecker        com.example.word                     D  Fixes applied: 0
2025-06-01 21:48:04.412  7911-7951  DatabaseInitializer     com.example.word                     D  Starting database initialization
2025-06-01 21:48:04.413  7911-7951  VocabularyJsonLoader    com.example.word                     D  Loading JSON file: cet4_vocabulary.json
2025-06-01 21:48:04.414  7911-7951  VocabularyJsonLoader    com.example.word                     D  Successfully loaded JSON file: cet4_vocabulary.json (8763 characters)
2025-06-01 21:48:04.415  7911-7951  DatabaseInitializer     com.example.word                     D  Current version: 2.0, Latest version: 2.0
2025-06-01 21:48:04.415  7911-7951  DatabaseInitializer     com.example.word                     D  Database already initialized with latest version
2025-06-01 21:48:04.857  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.864  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.872  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.879  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.888  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.896  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.904  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.913  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.926  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.929  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.937  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.947  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.956  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.962  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.970  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.980  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.990  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:04.996  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.005  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.014  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.023  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.031  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.037  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.048  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.055  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.063  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.071  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.081  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.087  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.097  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.103  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:05.113  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:07.430  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263685053, downTime=263685053, phoneEventTime=21:48:07.418 } moveCount:0
2025-06-01 21:48:07.457  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263685090, downTime=263685053, phoneEventTime=21:48:07.455 } moveCount:0
2025-06-01 21:48:08.137  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 5% (14MB)
2025-06-01 21:48:08.137  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (0ms)
2025-06-01 21:48:08.422  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263686047, downTime=263686047, phoneEventTime=21:48:08.412 } moveCount:0
2025-06-01 21:48:08.424  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263686055, downTime=263686047, phoneEventTime=21:48:08.419 } moveCount:0
2025-06-01 21:48:08.457  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.461  7911-7911  OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:48:08.463  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.471  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.479  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.488  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.488  7911-7923  om.example.word         com.example.word                     I  Compiler allocated 5099KB to compile void android.view.ViewRootImpl.performTraversals()
2025-06-01 21:48:08.493  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.505  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.512  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.520  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.527  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.536  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.544  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.552  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.561  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.569  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.577  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.585  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.594  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.603  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.610  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.619  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.626  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.635  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.643  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.652  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.660  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.668  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.685  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.694  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.702  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.718  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.720  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.727  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.736  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.744  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.753  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.769  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.777  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.786  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.797  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.800  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.814  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.818  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.830  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.836  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.843  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.852  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.861  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.870  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.878  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.887  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.895  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.898  7911-7989  ProfileInstaller        com.example.word                     D  Installing profile for com.example.word
2025-06-01 21:48:08.900  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.910  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.918  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.928  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.937  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.944  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.951  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.961  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.968  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.976  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.985  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:08.994  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.002  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.009  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.019  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.026  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.034  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.043  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.051  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.060  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.067  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.075  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.084  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.093  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.101  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.108  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.117  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.125  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.135  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.143  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.150  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.160  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.166  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.175  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.183  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.192  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.199  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.208  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.217  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.224  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.233  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.241  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.248  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.258  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.266  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.275  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.282  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.291  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.298  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.298  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263686924, downTime=263686924, phoneEventTime=21:48:09.289 } moveCount:0
2025-06-01 21:48:09.304  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.356  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.366  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.372  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.396  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.398  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.399  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263687031, downTime=263686924, phoneEventTime=21:48:09.396 } moveCount:7
2025-06-01 21:48:09.403  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.404  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235828): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:09.406  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:09.412  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.420  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.430  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.436  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.446  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.452  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.460  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.469  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.475  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.489  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.492  7911-7923  om.example.word         com.example.word                     I  Compiler allocated 5099KB to compile void android.view.ViewRootImpl.performTraversals()
2025-06-01 21:48:09.494  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.502  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.509  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.520  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.525  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.538  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.544  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.554  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.560  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.568  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.574  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.586  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.591  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.600  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.611  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.619  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.626  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.634  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.640  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.648  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.658  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.665  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.673  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.682  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.689  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.698  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.706  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.715  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.724  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.731  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.741  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.747  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.756  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.764  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.773  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.781  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.790  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.797  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.808  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.814  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.822  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.830  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.840  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.850  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.855  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.864  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.872  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.879  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.890  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.897  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.907  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.913  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.923  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.929  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.940  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.946  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:09.954  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.474  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263688103, downTime=263688103, phoneEventTime=21:48:10.468 } moveCount:0
2025-06-01 21:48:10.517  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263688149, downTime=263688103, phoneEventTime=21:48:10.513 } moveCount:0
2025-06-01 21:48:10.535  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.540  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:10.540  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:10.542  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.551  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.558  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.567  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.568  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:10.575  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.583  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.592  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.599  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.608  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.616  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.623  7911-7951  OnlineTTSHelper         com.example.word                     D  Simple API test URL: https://api.hewoyi.com/api/ai/audio/speech?key=QrK0eMRKAkaq7XIrovZSR3A5i7
2025-06-01 21:48:10.625  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.633  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.635  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000001,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:10.641  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.647  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.647  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#1](f:0,a:1) acquireNextBufferLocked size=1090x432 mFrameNumber=1 applyTransaction=true mTimestamp=263688282842024(auto) mPendingTransactions.size=0 graphicBufferId=33977486278663 transform=0
2025-06-01 21:48:10.648  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:10.649  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.657  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:10.658  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.672  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.674  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.675  7911-7951  TrafficStats            com.example.word                     D  tagSocket(154) with statsTag=0xffffffff, statsUid=-1
2025-06-01 21:48:10.675  7911-7951  System.out              com.example.word                     I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-06-01 21:48:10.675  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:10.675  7911-7951  System                  com.example.word                     W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-06-01 21:48:10.676  7911-7951  System.out              com.example.word                     I  [com.mediatek.cta.CtaAdapter] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-06-01 21:48:10.686  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.693  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.701  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.709  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.718  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.727  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.736  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.742  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.751  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.760  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.766  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.775  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.783  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.790  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.798  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.807  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.815  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.823  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.832  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.840  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.849  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.857  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.865  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.865  7911-7939  om.example.word         com.example.word                     I  ProcessProfilingInfo new_methods=743 is saved saved_to_disk=1 resolve_classes_delay=8000
2025-06-01 21:48:10.873  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.881  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.890  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.897  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.906  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.914  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.923  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.931  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.939  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.948  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.956  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.964  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.972  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.981  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.989  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:10.997  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.006  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.014  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.022  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.030  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.039  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.047  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.056  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.064  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.072  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.081  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.089  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.098  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.106  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.114  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.122  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.131  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.138  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.147  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.155  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.163  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.172  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.181  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.189  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.197  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.205  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.214  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.222  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.230  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.238  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.246  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.255  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.263  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.271  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.279  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.287  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.296  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.304  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.313  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.321  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.328  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.334  7911-7951  OnlineTTSHelper         com.example.word                     D  === 在线TTS API测试结果 ===
                                                                                                    API地址: https://api.hewoyi.com/api/ai/audio/speech?key=QrK0eMRKAkaq7XIrovZSR3A5i7
                                                                                                    响应码: 200
                                                                                                    响应消息: 
                                                                                                    Content-Type: text/html; charset=UTF-8
                                                                                                    Content-Length: null
                                                                                                    响应体长度: 84
                                                                                                    API状态码: 400
                                                                                                    API消息: type参数错误
2025-06-01 21:48:11.337  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.345  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.349  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@ab3e405[MainActivity]
2025-06-01 21:48:11.350  7911-7911  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#1](f:0,a:1) destructor()
2025-06-01 21:48:11.350  7911-7911  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#1(BLAST Consumer)1](id:1ee700000001,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:11.354  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.362  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.370  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:11.370  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:11.370  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.379  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.387  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.395  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.404  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.412  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.420  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.424  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:11.428  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.436  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.444  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.453  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.462  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.470  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.478  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.486  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.494  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.503  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.509  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000002,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:11.512  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.519  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.527  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.536  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.536  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#2](f:0,a:1) acquireNextBufferLocked size=1090x1209 mFrameNumber=1 applyTransaction=true mTimestamp=263689171544178(auto) mPendingTransactions.size=0 graphicBufferId=33977486278667 transform=0
2025-06-01 21:48:11.537  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:11.547  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:11.553  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.559  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.560  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.562  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.569  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.570  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.571  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:11.576  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.579  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.583  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.586  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.593  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.594  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.602  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.603  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.610  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.612  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.620  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.622  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.628  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.630  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.636  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.637  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.644  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.645  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.651  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.659  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:11.667  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:12.858  7911-7924  om.example.word         com.example.word                     I  This is non sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:48:12.905  7911-7926  System                  com.example.word                     W  A resource failed to call close. 
2025-06-01 21:48:12.923  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263690552, downTime=263690552, phoneEventTime=21:48:12.917 } moveCount:0
2025-06-01 21:48:13.011  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263690643, downTime=263690552, phoneEventTime=21:48:13.008 } moveCount:0
2025-06-01 21:48:13.018  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.024  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.026  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:13.026  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:13.032  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.041  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.044  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:13.049  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.057  7911-7946  OpenGLRenderer          com.example.word                     D  endAllActiveAnimators on 0xb400007411492f00 (RippleDrawable) with handle 0xb400007493a09220
2025-06-01 21:48:13.058  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@2caa0e9[MainActivity]
2025-06-01 21:48:13.060  7911-7928  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#2](f:0,a:1) destructor()
2025-06-01 21:48:13.060  7911-7928  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#2(BLAST Consumer)2](id:1ee700000002,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:13.061  7911-7952  OnlineTTSHelper         com.example.word                     D  Fetching available voices from: https://api.hewoyi.com/api/ai/audio/speech?key=QrK0eMRKAkaq7XIrovZSR3A5i7
2025-06-01 21:48:13.080  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000003,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:13.088  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:13.088  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#3](f:0,a:1) acquireNextBufferLocked size=1090x432 mFrameNumber=1 applyTransaction=true mTimestamp=263690723782332(auto) mPendingTransactions.size=0 graphicBufferId=33977486278671 transform=0
2025-06-01 21:48:13.089  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:13.119  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:13.138  7911-8003  PerformanceMonitor      com.example.word                     D  Memory usage: 3% (8MB)
2025-06-01 21:48:13.139  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (1ms)
2025-06-01 21:48:14.450  7911-7952  OnlineTTSHelper         com.example.word                     D  Voices API Response: {"code":400,"msg":"type参数错误","data":null,"exec_time":0.019704,"ip":"***************"}
2025-06-01 21:48:14.450  7911-7952  OnlineTTSHelper         com.example.word                     E  API returned error code: 400
2025-06-01 21:48:14.467  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@dffdea9[MainActivity]
2025-06-01 21:48:14.468  7911-7911  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#3](f:0,a:1) destructor()
2025-06-01 21:48:14.468  7911-7911  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#3(BLAST Consumer)3](id:1ee700000003,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:14.498  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:14.498  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:14.530  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:14.572  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000004,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:14.596  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.596  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#4](f:0,a:1) acquireNextBufferLocked size=1090x586 mFrameNumber=1 applyTransaction=true mTimestamp=263692231058024(auto) mPendingTransactions.size=0 graphicBufferId=33977486278675 transform=0
2025-06-01 21:48:14.598  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:14.612  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:14.620  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.625  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.630  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.631  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.632  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:14.639  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.640  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.647  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.648  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.657  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.658  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.664  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.665  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.672  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.673  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.681  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.682  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.691  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.693  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.697  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.698  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.708  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.710  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.712  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.723  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:14.732  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.165  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.173  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.181  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.191  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.199  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.208  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.217  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.224  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.233  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.241  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.250  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.257  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.266  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.274  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.282  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.289  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.300  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.307  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.316  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.325  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.332  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.342  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.350  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.357  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.366  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.373  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.381  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.391  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.397  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.408  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.416  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:16.425  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:18.141  7911-7952  PerformanceMonitor      com.example.word                     D  Memory usage: 3% (8MB)
2025-06-01 21:48:18.142  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (1ms)
2025-06-01 21:48:23.033  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263700656, downTime=263700656, phoneEventTime=21:48:23.020 } moveCount:0
2025-06-01 21:48:23.106  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263700739, downTime=263700656, phoneEventTime=21:48:23.103 } moveCount:0
2025-06-01 21:48:23.126  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.130  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.137  7911-7946  OpenGLRenderer          com.example.word                     D  endAllActiveAnimators on 0xb40000741129f200 (RippleDrawable) with handle 0xb400007493a093e0
2025-06-01 21:48:23.138  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@6efbcc1[MainActivity]
2025-06-01 21:48:23.142  7911-7930  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#4](f:0,a:1) destructor()
2025-06-01 21:48:23.143  7911-7930  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#4(BLAST Consumer)4](id:1ee700000004,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:23.144  7911-7952  PerformanceMonitor      com.example.word                     D  Memory usage: 3% (8MB)
2025-06-01 21:48:23.157  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (13ms)
2025-06-01 21:48:23.178  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:23.182  7911-7911  HandWritingStubImpl     com.example.word                     I  refreshLastKeyboardType: 1
2025-06-01 21:48:23.182  7911-7911  HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:48:23.202  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.206  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.215  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.226  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.230  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.244  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.248  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.257  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.261  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.270  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:23.281  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.390  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263705011, downTime=263705011, phoneEventTime=21:48:27.376 } moveCount:0
2025-06-01 21:48:27.469  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263705102, downTime=263705011, phoneEventTime=21:48:27.466 } moveCount:0
2025-06-01 21:48:27.491  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:27.491  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:27.494  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.508  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.514  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.517  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:27.521  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.527  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.538  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.546  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.548  7911-7952  OnlineTTSHelper         com.example.word                     D  Simple API test URL: https://api.hewoyi.com/api/ai/audio/speech?key=QrK0eMRKAkaq7XIrovZSR3A5i7
2025-06-01 21:48:27.553  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.563  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.567  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000005,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:27.571  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.579  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.579  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#5](f:0,a:1) acquireNextBufferLocked size=1090x432 mFrameNumber=1 applyTransaction=true mTimestamp=263705214786948(auto) mPendingTransactions.size=0 graphicBufferId=33977486278676 transform=0
2025-06-01 21:48:27.581  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:27.583  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.597  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:27.599  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.614  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.622  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:27.623  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.630  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.639  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.647  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.657  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.671  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.682  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.689  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.702  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.713  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.722  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.729  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.739  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.749  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.757  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.763  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.770  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.779  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.786  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.795  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.802  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.812  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.820  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.828  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.837  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.844  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.853  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.861  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.869  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.878  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.886  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.894  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.903  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.911  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.919  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.920  7911-7952  OnlineTTSHelper         com.example.word                     D  === 在线TTS API测试结果 ===
                                                                                                    API地址: https://api.hewoyi.com/api/ai/audio/speech?key=QrK0eMRKAkaq7XIrovZSR3A5i7
                                                                                                    响应码: 200
                                                                                                    响应消息: 
                                                                                                    Content-Type: text/html; charset=UTF-8
                                                                                                    Content-Length: null
                                                                                                    响应体长度: 85
                                                                                                    API状态码: 400
                                                                                                    API消息: type参数错误
2025-06-01 21:48:27.928  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.936  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.941  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@3879b0a[MainActivity]
2025-06-01 21:48:27.942  7911-7911  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#5](f:0,a:1) destructor()
2025-06-01 21:48:27.942  7911-7911  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#5(BLAST Consumer)5](id:1ee700000005,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:27.947  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.951  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.959  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.961  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:27.961  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:27.969  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.976  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.985  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:27.995  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.002  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.011  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.018  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.027  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.035  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.038  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:28.042  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.051  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.060  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.070  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.076  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.085  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.093  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.101  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.103  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000006,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:28.109  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.118  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.126  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.139  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.140  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#6](f:0,a:1) acquireNextBufferLocked size=1090x1209 mFrameNumber=1 applyTransaction=true mTimestamp=263705775213486(auto) mPendingTransactions.size=0 graphicBufferId=33977486278683 transform=0
2025-06-01 21:48:28.141  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:28.146  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.148  7911-7952  PerformanceMonitor      com.example.word                     D  Memory usage: 3% (9MB)
2025-06-01 21:48:28.158  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:28.167  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.174  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (26ms)
2025-06-01 21:48:28.175  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.180  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.181  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.183  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.185  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:28.190  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.196  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.197  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.200  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.206  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.208  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.217  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.218  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.227  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.228  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.231  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.233  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.242  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.243  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.250  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.252  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.259  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.261  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.265  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.267  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.273  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.274  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.282  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.284  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.290  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.298  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.306  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.314  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:28.323  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.622  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263707242, downTime=263707242, phoneEventTime=21:48:29.606 } moveCount:0
2025-06-01 21:48:29.658  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.666  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.668  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263707300, downTime=263707242, phoneEventTime=21:48:29.665 } moveCount:0
2025-06-01 21:48:29.687  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.691  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.695  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:29.695  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:29.696  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.701  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.709  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.717  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.720  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:29.726  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.734  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.737  7911-7952  OnlineTTSHelper         com.example.word                     D  Returning cached voices: 0
2025-06-01 21:48:29.743  7911-7946  OpenGLRenderer          com.example.word                     D  endAllActiveAnimators on 0xb400007411632d00 (RippleDrawable) with handle 0xb400007493a07ec0
2025-06-01 21:48:29.744  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@8db0f6c[MainActivity]
2025-06-01 21:48:29.757  7911-7928  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#6](f:0,a:1) destructor()
2025-06-01 21:48:29.758  7911-7928  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#6(BLAST Consumer)6](id:1ee700000006,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:29.783  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000007,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:29.799  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.800  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#7](f:0,a:1) acquireNextBufferLocked size=1090x432 mFrameNumber=1 applyTransaction=true mTimestamp=263707434840486(auto) mPendingTransactions.size=0 graphicBufferId=33977486278687 transform=0
2025-06-01 21:48:29.801  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:29.841  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@5daf93d[MainActivity]
2025-06-01 21:48:29.842  7911-7911  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#7](f:0,a:1) destructor()
2025-06-01 21:48:29.842  7911-7911  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#7(BLAST Consumer)7](id:1ee700000007,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:29.866  7911-7911  IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:48:29.866  7911-7911  MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:48:29.894  7911-7911  VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:48:29.956  7911-7911  BufferQueueConsumer     com.example.word                     D  [](id:1ee700000008,api:0,p:-1,c:7911) connect: controlledByApp=false
2025-06-01 21:48:29.976  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.977  7911-7946  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#8](f:0,a:1) acquireNextBufferLocked size=1090x586 mFrameNumber=1 applyTransaction=true mTimestamp=263707611615256(auto) mPendingTransactions.size=0 graphicBufferId=33977486278691 transform=0
2025-06-01 21:48:29.978  7911-7946  Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:48:29.983  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:48:29.991  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:29.999  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.003  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.008  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.013  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.014  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:30.014  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.015  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.018  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.023  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.025  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.032  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.033  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.045  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.046  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.049  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.050  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.058  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.059  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.067  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.068  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.075  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.077  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.083  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.085  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.089  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.099  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:30.107  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.547  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.555  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.564  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.576  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.581  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.591  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.599  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.607  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.616  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.625  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.631  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.640  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.649  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.656  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.663  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.671  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.681  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.689  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.698  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.705  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.714  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.722  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.732  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.740  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.748  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.755  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.772  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.781  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.788  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.797  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:31.806  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:32.957  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263710582, downTime=263710582, phoneEventTime=21:48:32.947 } moveCount:0
2025-06-01 21:48:33.000  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263710632, downTime=263710582, phoneEventTime=21:48:32.997 } moveCount:0
2025-06-01 21:48:33.019  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.022  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.028  7911-7946  OpenGLRenderer          com.example.word                     D  endAllActiveAnimators on 0xb400007411710300 (RippleDrawable) with handle 0xb400007493a094e0
2025-06-01 21:48:33.029  7911-7911  View                    com.example.word                     D  [Warning] assignParent to null: this = DecorView@61f0c8c[MainActivity]
2025-06-01 21:48:33.041  7911-8001  BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#8](f:0,a:1) destructor()
2025-06-01 21:48:33.041  7911-8001  BufferQueueConsumer     com.example.word                     D  [VRI[MainActivity]#8(BLAST Consumer)8](id:1ee700000008,api:0,p:-1,c:7911) disconnect
2025-06-01 21:48:33.058  7911-7911  DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:48:33.063  7911-7911  HandWritingStubImpl     com.example.word                     I  refreshLastKeyboardType: 1
2025-06-01 21:48:33.063  7911-7911  HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:48:33.099  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.108  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.118  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.126  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.133  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.140  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.150  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 4% (11MB)
2025-06-01 21:48:33.154  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.156  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (6ms)
2025-06-01 21:48:33.162  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.171  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.177  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:33.188  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.030  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263711654, downTime=263711654, phoneEventTime=21:48:34.019 } moveCount:0
2025-06-01 21:48:34.126  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263711757, downTime=263711654, phoneEventTime=21:48:34.122 } moveCount:1
2025-06-01 21:48:34.141  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.157  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.164  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.173  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.179  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.189  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.197  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.205  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.214  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.221  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.231  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.237  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.247  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.256  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.263  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.269  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.278  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.287  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.296  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.305  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.317  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.321  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.329  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.341  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.348  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.353  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.364  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.373  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.379  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.387  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.396  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.405  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.413  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.429  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.437  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.447  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.453  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.463  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.470  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.478  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.487  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.496  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.504  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.511  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.520  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.529  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.537  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.545  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.554  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.563  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.569  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.578  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.587  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.595  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.605  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.610  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.618  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.628  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.636  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.645  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.654  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.661  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.671  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.676  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.686  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.694  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.702  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.711  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.718  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.727  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.735  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.743  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.752  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.760  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.768  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.777  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.784  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.793  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.801  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.810  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.817  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.826  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.834  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.843  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.851  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.859  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.868  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.875  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.884  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.892  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.900  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.909  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.917  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.926  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.933  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.942  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.950  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.959  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.967  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.975  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.983  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:34.991  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.001  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.007  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.877  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.884  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.894  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.903  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.912  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.921  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.931  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.944  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.948  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.959  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.970  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.977  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.982  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.994  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:35.999  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.007  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.017  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.026  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.031  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.042  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.048  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.057  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.066  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.074  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.082  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.091  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.096  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.110  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.113  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.122  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.133  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.137  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.848  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263714468, downTime=263714468, phoneEventTime=21:48:36.832 } moveCount:0
2025-06-01 21:48:36.910  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263714542, downTime=263714468, phoneEventTime=21:48:36.907 } moveCount:0
2025-06-01 21:48:36.927  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.930  7911-7911  OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:48:36.938  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.951  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.957  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.966  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.976  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.983  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.991  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:36.999  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.008  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.023  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.033  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.039  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.050  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.142  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.166  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.169  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.172  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.181  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.190  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.200  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.205  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.217  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.222  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.230  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.238  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.246  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.256  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.264  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.275  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.902  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263715528, downTime=263715528, phoneEventTime=21:48:37.893 } moveCount:0
2025-06-01 21:48:37.937  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.945  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.954  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.962  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.968  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235831): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:37.971  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:37.982  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.984  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263715614, downTime=263715528, phoneEventTime=21:48:37.979 } moveCount:7
2025-06-01 21:48:37.989  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:37.996  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.004  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.012  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.012  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235832): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.027  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.032  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.038  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.047  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.054  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.052  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235833): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.072  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.076  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.078  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.089  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.103  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.107  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.115  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.121  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.128  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.137  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.136  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235834): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.143  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.146  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.151  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 5% (13MB)
2025-06-01 21:48:38.153  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (2ms)
2025-06-01 21:48:38.156  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.162  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.170  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.180  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.186  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.184  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235835): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.196  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.199  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.203  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.210  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.219  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.228  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.241  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.243  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.253  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.262  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.268  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.273  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263715904, downTime=263715904, phoneEventTime=21:48:38.269 } moveCount:0
2025-06-01 21:48:38.311  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.318  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.326  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.335  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.345  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.349  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263715978, downTime=263715904, phoneEventTime=21:48:38.342 } moveCount:6
2025-06-01 21:48:38.355  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.372  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.377  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.387  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.392  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235836): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.395  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.400  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.405  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.412  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.419  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.427  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.435  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.448  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.454  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.461  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.468  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.476  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.485  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.493  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.499  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.496  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235837): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.509  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.513  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.518  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.528  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.534  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.543  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.551  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.558  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.556  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235838): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:38.564  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.571  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.578  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.584  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.592  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.600  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.608  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.617  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.625  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.633  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.638  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.645  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.651  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.660  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.667  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.673  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.673  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263716293, downTime=263716293, phoneEventTime=21:48:38.657 } moveCount:0
2025-06-01 21:48:38.699  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.707  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.716  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.726  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.732  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.740  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263716371, downTime=263716293, phoneEventTime=21:48:38.735 } moveCount:7
2025-06-01 21:48:38.740  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.748  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.756  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.759  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.774  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.782  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.785  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.790  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.797  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.808  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.811  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.815  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.822  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.826  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.832  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.840  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.849  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.853  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.857  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.865  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.874  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.877  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.881  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.888  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.898  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.902  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.906  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.921  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.924  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.931  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.939  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.948  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.952  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.957  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.964  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.972  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.974  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.981  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:38.989  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:38.997  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.001  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.006  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.014  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.022  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.025  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.030  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.038  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.046  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.056  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.059  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.060  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263716690, downTime=263716690, phoneEventTime=21:48:39.055 } moveCount:0
2025-06-01 21:48:39.088  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.095  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.104  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.115  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.119  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263716748, downTime=263716690, phoneEventTime=21:48:39.113 } moveCount:5
2025-06-01 21:48:39.124  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.132  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.137  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.143  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.148  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.156  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.163  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.160  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235853): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:39.170  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.173  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.179  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.186  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.184  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235854): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:39.194  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.198  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.204  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.214  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.221  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.220  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235855): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:39.230  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.234  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.237  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.244  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235856): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:39.247  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.255  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.259  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.263  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.268  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235857): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:39.271  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.278  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.283  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.287  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.299  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.306  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.312  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.320  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.328  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.337  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.340  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.345  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.353  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.359  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.366  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.370  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.378  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.386  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.394  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.398  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.402  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.411  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.419  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.426  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.430  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.436  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.444  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.452  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.460  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.464  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.469  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.478  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.486  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.494  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.502  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.505  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.510  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.519  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.526  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.531  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.539  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.544  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.552  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.560  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.568  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.575  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.581  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.587  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.595  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.602  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.608  7911-7924  om.example.word         com.example.word                     I  This is sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:48:39.609  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.623  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.627  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.635  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.643  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.651  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.660  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.668  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.675  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.679  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.685  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.692  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.699  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.707  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.717  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.725  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.734  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.740  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.748  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.757  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.775  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.783  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.791  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.798  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.807  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.818  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.825  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.833  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.843  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.856  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.859  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.867  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.876  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  [ANR Warning]Input routeing takes more than 6000ms since 1970-01-01 08:00:00.000, this = com.mediatek.view.impl.ViewDebugManagerImpl@63b3ed6
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$SyntheticInputStage@fcd00bd at 2025-06-01 21:48:39.881
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePostImeInputStage@e213a67 at 2025-06-01 21:48:39.880
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ImeInputStage@12be2f1 at 2025-06-01 21:48:39.866
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePreImeInputStage@f273c7b at 2025-06-01 21:48:39.866
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPreImeInputStage@a446a98 at 2025-06-01 21:48:39.866
2025-06-01 21:48:39.883  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$EarlyPostImeInputStage@4ce0d81 at 2025-06-01 21:48:39.878
2025-06-01 21:48:39.884  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPostImeInputStage@69b7014 at 2025-06-01 21:48:39.880
2025-06-01 21:48:39.929  7911-7911  VRI[MainActivity]       com.example.word                     V  [ANR Warning]Input routeing takes more than 6000ms since 1970-01-01 08:00:00.000, this = com.mediatek.view.impl.ViewDebugManagerImpl@63b3ed6
2025-06-01 21:48:39.929  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$SyntheticInputStage@fcd00bd at 2025-06-01 21:48:39.929
2025-06-01 21:48:39.929  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePostImeInputStage@e213a67 at 2025-06-01 21:48:39.884
2025-06-01 21:48:39.929  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$EarlyPostImeInputStage@4ce0d81 at 2025-06-01 21:48:39.884
2025-06-01 21:48:39.930  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPostImeInputStage@69b7014 at 2025-06-01 21:48:39.884
2025-06-01 21:48:39.939  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:39.955  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.961  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.964  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.972  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.979  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.987  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:39.994  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.005  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.009  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.020  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.025  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.037  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.559  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263718188, downTime=263718188, phoneEventTime=21:48:40.553 } moveCount:0
2025-06-01 21:48:40.614  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263718246, downTime=263718188, phoneEventTime=21:48:40.611 } moveCount:0
2025-06-01 21:48:40.642  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.644  7911-7911  OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:48:40.652  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.655  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.665  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.675  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.685  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.691  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.702  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.708  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.718  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.725  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.733  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.742  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.748  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.759  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.766  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.809  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.811  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.814  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.830  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.833  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.844  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.849  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.857  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.866  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.875  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.883  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.891  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.898  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.906  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.914  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.923  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.931  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:40.941  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.223  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263719848, downTime=263719848, phoneEventTime=21:48:42.212 } moveCount:0
2025-06-01 21:48:42.261  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263719894, downTime=263719848, phoneEventTime=21:48:42.258 } moveCount:0
2025-06-01 21:48:42.287  7911-7946  libEGL                  com.example.word                     E  pre_cache appList: ,,
2025-06-01 21:48:42.341  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.343  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.350  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.360  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.372  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.380  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.388  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.397  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.405  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.413  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.422  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.428  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.439  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.447  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.480  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.483  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.489  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.495  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.504  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.526  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.528  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.538  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.546  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.555  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.563  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.571  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.579  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.588  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.596  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.604  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.612  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.622  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.630  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.638  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.646  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.655  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.662  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.671  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.680  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.688  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.695  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:42.706  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:43.154  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 5% (12MB)
2025-06-01 21:48:43.155  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (1ms)
2025-06-01 21:48:45.004  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263722625, downTime=263722625, phoneEventTime=21:48:44.989 } moveCount:0
2025-06-01 21:48:45.097  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263722729, downTime=263722625, phoneEventTime=21:48:45.093 } moveCount:0
2025-06-01 21:48:45.127  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.133  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.136  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.143  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.152  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.162  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.178  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.184  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.197  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.213  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.216  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.228  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.238  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.250  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.263  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.278  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.288  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.299  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.307  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.318  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.327  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.343  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.349  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.361  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.372  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.382  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.396  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.407  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.417  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.431  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.438  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.452  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.826  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263723450, downTime=263723450, phoneEventTime=21:48:45.815 } moveCount:0
2025-06-01 21:48:45.881  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.888  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.897  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.906  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.915  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.919  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263723549, downTime=263723450, phoneEventTime=21:48:45.914 } moveCount:8
2025-06-01 21:48:45.930  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.933  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.938  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.948  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.956  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.964  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.972  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.981  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.990  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:45.997  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.005  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.013  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.021  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.030  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.037  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.046  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.054  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.062  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.071  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.079  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.088  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.097  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.105  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.113  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.122  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.129  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.138  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.146  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.154  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.162  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.170  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.178  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.187  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.195  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.203  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.211  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.220  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.229  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.233  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263723859, downTime=263723859, phoneEventTime=21:48:46.223 } moveCount:0
2025-06-01 21:48:46.235  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.243  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.251  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.259  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.267  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.277  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.285  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.293  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.301  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.309  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.316  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263723946, downTime=263723859, phoneEventTime=21:48:46.311 } moveCount:6
2025-06-01 21:48:46.320  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.327  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.336  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.343  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.352  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.360  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.369  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.377  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.385  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.393  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.402  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.410  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.418  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.427  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.434  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.443  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.451  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.460  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.468  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.476  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.484  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.493  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.501  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.509  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.518  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.526  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.534  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.542  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.550  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.558  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.567  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.575  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.583  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.591  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.600  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.608  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.616  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.625  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.633  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.641  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.649  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.657  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.666  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.674  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.682  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.734  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263724364, downTime=263724364, phoneEventTime=21:48:46.729 } moveCount:0
2025-06-01 21:48:46.818  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263724451, downTime=263724364, phoneEventTime=21:48:46.815 } moveCount:0
2025-06-01 21:48:46.828  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.835  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.840  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.850  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.853  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:46.852  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235868): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:46.857  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.865  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.873  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.882  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.890  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.898  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.907  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.915  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.923  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.932  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.939  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.948  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.956  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.964  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.973  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.981  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.989  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:46.997  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.006  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.014  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.022  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.030  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.039  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.047  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.055  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.063  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.072  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.081  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.088  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.096  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.104  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.113  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.121  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.131  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.606  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263725233, downTime=263725233, phoneEventTime=21:48:47.598 } moveCount:0
2025-06-01 21:48:47.709  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263725341, downTime=263725233, phoneEventTime=21:48:47.706 } moveCount:0
2025-06-01 21:48:47.719  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.727  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.731  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.744  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235869): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:47.745  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:47.750  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.756  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.774  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.782  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.789  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.798  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.806  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.814  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.822  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.830  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.838  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.847  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.855  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.863  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.871  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.880  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.888  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.896  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.905  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.913  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.922  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.930  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.939  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.947  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.955  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.963  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.971  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.980  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.989  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:47.996  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:48.006  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:48.012  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:48.023  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:48.155  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 5% (13MB)
2025-06-01 21:48:48.156  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (1ms)
2025-06-01 21:48:51.177  7911-7911  VRI[MainActivity]       com.example.word                     V  [ANR Warning]Input routeing takes more than 6000ms since 1970-01-01 08:00:00.000, this = com.mediatek.view.impl.ViewDebugManagerImpl@63b3ed6
2025-06-01 21:48:51.177  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$SyntheticInputStage@fcd00bd at 2025-06-01 21:48:51.176
2025-06-01 21:48:51.177  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePostImeInputStage@e213a67 at 2025-06-01 21:48:51.176
2025-06-01 21:48:51.177  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ImeInputStage@12be2f1 at 2025-06-01 21:48:51.174
2025-06-01 21:48:51.177  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePreImeInputStage@f273c7b at 2025-06-01 21:48:51.174
2025-06-01 21:48:51.178  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPreImeInputStage@a446a98 at 2025-06-01 21:48:51.174
2025-06-01 21:48:51.178  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$EarlyPostImeInputStage@4ce0d81 at 2025-06-01 21:48:51.176
2025-06-01 21:48:51.178  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPostImeInputStage@69b7014 at 2025-06-01 21:48:51.176
2025-06-01 21:48:51.219  7911-7911  VRI[MainActivity]       com.example.word                     V  [ANR Warning]Input routeing takes more than 6000ms since 1970-01-01 08:00:00.000, this = com.mediatek.view.impl.ViewDebugManagerImpl@63b3ed6
2025-06-01 21:48:51.219  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$SyntheticInputStage@fcd00bd at 2025-06-01 21:48:51.219
2025-06-01 21:48:51.219  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$NativePostImeInputStage@e213a67 at 2025-06-01 21:48:51.178
2025-06-01 21:48:51.219  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$EarlyPostImeInputStage@4ce0d81 at 2025-06-01 21:48:51.178
2025-06-01 21:48:51.219  7911-7911  VRI[MainActivity]       com.example.word                     V  Input event delivered to android.view.ViewRootImpl$ViewPostImeInputStage@69b7014 at 2025-06-01 21:48:51.178
2025-06-01 21:48:51.229  7911-7944  libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:48:51.228  7911-7911  FramePolicy             com.example.word                     W  type=1400 audit(0.0:235870): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:48:51.242  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.245  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.253  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.261  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.270  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.278  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.285  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.294  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.303  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.311  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.320  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:51.329  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.830  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.841  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.847  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.853  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.862  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.870  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.879  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.887  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.896  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.903  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.911  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.920  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.928  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.936  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.945  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.956  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.966  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.970  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.979  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.986  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:52.995  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.004  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.015  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.018  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.028  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.038  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.047  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.054  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.060  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.068  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.079  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.087  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.158  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 5% (14MB)
2025-06-01 21:48:53.159  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (0ms)
2025-06-01 21:48:53.876  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263731497, downTime=263731497, phoneEventTime=21:48:53.862 } moveCount:0
2025-06-01 21:48:53.955  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263731587, downTime=263731497, phoneEventTime=21:48:53.952 } moveCount:0
2025-06-01 21:48:53.974  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.978  7911-7911  OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:48:53.986  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:53.993  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.003  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.010  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.020  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.027  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.036  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.038  7911-7911  TTSDebugHelper          com.example.word                     D  Checking TTS availability...
2025-06-01 21:48:54.043  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.044  7911-7911  TTSDebugHelper          com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:48:54.044  7911-7911  VocabularyFragment      com.example.word                     D  TTS availability check: false - TTS初始化失败: -1
2025-06-01 21:48:54.051  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.055  7911-7911  TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:48:54.055  7911-7911  VocabularyFragment      com.example.word                     D  默认TTS引擎: null
2025-06-01 21:48:54.061  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.064  7911-7911  TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:48:54.064  7911-7911  VocabularyFragment      com.example.word                     D  TTS System Info: TTS设置可用: true
2025-06-01 21:48:54.064  7911-7911  VocabularyFragment      com.example.word                     D  TTS System Info: 系统语言: zh_CN
2025-06-01 21:48:54.064  7911-7911  VocabularyFragment      com.example.word                     D  TTS System Info: 可用TTS引擎数量: 0
2025-06-01 21:48:54.064  7911-7911  AppOptimizer            com.example.word                     D  TTS initialization needed: false
2025-06-01 21:48:54.064  7911-7911  VocabularyFragment      com.example.word                     D  TTS managers already initialized, skipping
2025-06-01 21:48:54.064  7911-7911  AppOptimizer            com.example.word                     D  Database check needed: false
2025-06-01 21:48:54.064  7911-7911  VocabularyFragment      com.example.word                     D  Skipping database check (already done)
2025-06-01 21:48:54.070  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.076  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.084  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.092  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.100  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.109  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.143  7911-7924  om.example.word         com.example.word                     I  This is non sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:48:54.176  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.181  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.184  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.199  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.202  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.208  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.218  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.227  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.235  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.244  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.252  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.260  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.267  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.275  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.282  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.292  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.299  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.311  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.319  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.326  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.334  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.341  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.352  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.358  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.366  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.374  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.382  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.391  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.399  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.407  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.415  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.425  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.433  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.441  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.449  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.458  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.467  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.475  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.486  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.489  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:54.499  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:58.161  7911-7951  PerformanceMonitor      com.example.word                     D  Memory usage: 3% (7MB)
2025-06-01 21:48:58.161  7911-7911  PerformanceMonitor      com.example.word                     D  Main thread responsive (0ms)
2025-06-01 21:48:59.033  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=263736655, downTime=263736655, phoneEventTime=21:48:59.020 } moveCount:0
2025-06-01 21:48:59.144  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.149  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.165  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.171  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.179  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.187  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.194  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.202  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.214  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.218  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.228  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.235  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.244  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.252  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.262  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.269  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.277  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.286  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.294  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.302  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.311  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.318  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.328  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.335  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.344  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.351  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.360  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.370  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.377  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.385  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.394  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.401  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.410  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.416  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.427  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.436  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.443  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.451  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.461  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.464  7911-7911  MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=263737093, downTime=263736655, phoneEventTime=21:48:59.457 } moveCount:34
2025-06-01 21:48:59.466  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.476  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.483  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.493  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.500  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.508  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.516  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.525  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.534  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.540  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.551  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.560  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.566  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.576  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.583  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.592  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.599  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.609  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.617  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.624  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.633  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.641  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.650  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.657  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.667  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.674  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.683  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.690  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.700  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.706  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.716  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.723  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.732  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.740  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.748  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.756  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.765  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.773  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.781  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.789  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.799  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.808  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.815  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.827  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.832  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.840  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.849  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.858  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.865  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.873  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.883  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.890  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.899  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.907  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.915  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.922  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.932  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.940  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.951  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.955  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.965  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.973  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.980  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.993  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:48:59.996  7911-7946  gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
