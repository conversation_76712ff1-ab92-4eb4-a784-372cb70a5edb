--------- beginning of crash
--------- beginning of system
2025-06-01 20:51:39.919 15373-15410 ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 20:51:39.919 15373-15410 ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
2025-06-01 20:51:39.938 15373-15373 Looper                  com.example.word                     W  PerfMonitor looperActivity : package=com.example.word/.MainActivity time=0ms latency=555ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=1 wall=60ms seq=1 late=14ms h=android.app.ActivityThread$H w=162) (msgIndex=3 wall=183ms seq=3 late=72ms h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=312ms seq=4 late=243ms h=android.app.ActivityThread$H w=159)
2025-06-01 20:51:40.052 15373-15373 Looper                  com.example.word                     W  PerfMonitor doFrame : time=114ms vsyncFrame=0 latency=312ms procState=-1 historyMsgCount=4 (msgIndex=1 wall=183ms seq=3 late=72ms h=android.app.ActivityThread$H w=110) (msgIndex=2 wall=312ms seq=4 late=243ms h=android.app.ActivityThread$H w=159)
2025-06-01 20:53:19.760 15373-15410 ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 20:53:19.760 15373-15410 ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
--------- beginning of main
2025-06-01 21:00:28.851 21418-21418 om.example.word         com.example.word                     I  Late-enabling -Xcheck:jni
2025-06-01 21:00:28.945 21418-21418 MessageMonitor          com.example.word                     I  Load libmiui_runtime
2025-06-01 21:00:29.013 21418-21436 AppScoutStateMachine    com.example.word                     D  21418-ScoutStateMachinecreated
2025-06-01 21:00:29.016 21418-21418 re-initialized>         com.example.word                     W  type=1400 audit(0.0:234583): avc: granted { execute } for path="/data/data/com.example.word/code_cache/startup_agents/4aa0b38c-agent.so" dev="dm-46" ino=4502476 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c255,c256,c512,c768 tclass=file app=com.example.word
2025-06-01 21:00:29.024 21418-21418 om.example.word         com.example.word                     W  DexFile /data/data/com.example.word/code_cache/.studio/instruments-dd2db83e.jar is in boot class path but is not in a known location
2025-06-01 21:00:29.033 21418-21418 om.example.word         com.example.word                     W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-06-01 21:00:29.033 21418-21418 om.example.word         com.example.word                     W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-06-01 21:00:29.035 21418-21418 Compatibil...geReporter com.example.word                     D  Compat change id reported: 171979766; UID 10255; state: ENABLED
2025-06-01 21:00:29.051 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes3.dm': No such file or directory
2025-06-01 21:00:29.051 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes7.dm': No such file or directory
2025-06-01 21:00:29.051 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
2025-06-01 21:00:29.052 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/data/com.example.word/code_cache/.overlay/base.apk/classes11.dm': No such file or directory
2025-06-01 21:00:29.053 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/app/~~lypVtbV-LBKewMVy9-EzcA==/com.example.word-anFVKtNa9ATxJyLKgK2J3w==/base.dm': No such file or directory
2025-06-01 21:00:29.053 21418-21418 ziparchive              com.example.word                     W  Unable to open '/data/app/~~lypVtbV-LBKewMVy9-EzcA==/com.example.word-anFVKtNa9ATxJyLKgK2J3w==/base.dm': No such file or directory
2025-06-01 21:00:29.186 21418-21418 GraphicsEnvironment     com.example.word                     V  ANGLE Developer option for 'com.example.word' set to: 'default'
2025-06-01 21:00:29.186 21418-21418 GraphicsEnvironment     com.example.word                     V  ANGLE GameManagerService for com.example.word: false
2025-06-01 21:00:29.186 21418-21418 GraphicsEnvironment     com.example.word                     V  App is not on the allowlist for updatable production driver.
2025-06-01 21:00:29.188 21418-21418 ForceDarkHelperStubImpl com.example.word                     I  initialize for com.example.word , ForceDarkOrigin
2025-06-01 21:00:29.189 21418-21418 om.example.word         com.example.word                     D  JNI_OnLoad success
2025-06-01 21:00:29.189 21418-21418 MiuiForceDarkConfig     com.example.word                     I  setConfig density:2.750000, mainRule:0, secondaryRule:0, tertiaryRule:0
2025-06-01 21:00:29.191 21418-21418 NetworkSecurityConfig   com.example.word                     D  No Network Security Config specified, using platform default
2025-06-01 21:00:29.192 21418-21418 NetworkSecurityConfig   com.example.word                     D  No Network Security Config specified, using platform default
2025-06-01 21:00:29.193 21418-21418 Compatibil...geReporter com.example.word                     D  Compat change id reported: 183155436; UID 10255; state: ENABLED
2025-06-01 21:00:29.210 21418-21418 WordApplication         com.example.word                     D  Application starting
2025-06-01 21:00:29.210 21418-21418 ErrorHandler            com.example.word                     D  Initializing error handler
2025-06-01 21:00:29.215 21418-21418 WordApplication         com.example.word                     D  Application initialized successfully
2025-06-01 21:00:29.218 21418-21418 MiuiMultiWindowAdapter  com.example.word                     D  MiuiMultiWindowAdapter::getFreeformVideoWhiteListInSystem::LIST_ABOUT_SUPPORT_LANDSCAPE_VIDEO = [com.hunantv.imgo.activity, com.tencent.qqlive, com.qiyi.video, com.hunantv.imgo.activity.inter, com.tencent.qqlivei18n, com.iqiyi.i18n, tv.danmaku.bili]
2025-06-01 21:00:29.218 21418-21454 libMEOW                 com.example.word                     D  meow new tls: 0xb400007483b3a6c0
2025-06-01 21:00:29.218 21418-21454 libMEOW                 com.example.word                     D  meow reload base cfg path: na
2025-06-01 21:00:29.218 21418-21454 libMEOW                 com.example.word                     D  meow reload overlay cfg path: na
2025-06-01 21:00:29.218 21418-21454 QT                      com.example.word                     W  qt_process_init() called
2025-06-01 21:00:29.218 21418-21454 QT                      com.example.word                     E  [QT]file does not exist
2025-06-01 21:00:29.218 21418-21454 QT                      com.example.word                     W  Support!!
2025-06-01 21:00:29.219 21418-21454 QT                      com.example.word                     E  [QT]file does not exist
2025-06-01 21:00:29.219 21418-21454 libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:00:29.219 21418-21454 libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:00:29.219 21418-21454 libMEOW                 com.example.word                     D  meow delete tls: 0xb400007483b3a6c0
2025-06-01 21:00:29.235 21418-21418 AppCompatDelegate       com.example.word                     D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-06-01 21:00:29.268 21418-21418 om.example.word         com.example.word                     W  type=1400 audit(0.0:234584): avc: denied { read } for name="u:object_r:vendor_displayfeature_prop:s0" dev="tmpfs" ino=442 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:vendor_displayfeature_prop:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:29.272 21418-21418 libc                    com.example.word                     W  Access denied finding property "ro.vendor.df.effect.conflict"
2025-06-01 21:00:29.339 21418-21455 ViewContentFactory      com.example.word                     D  initViewContentFetcherClass
2025-06-01 21:00:29.339 21418-21455 ViewContentFactory      com.example.word                     D  getInterceptorPackageInfo
2025-06-01 21:00:29.340 21418-21455 ViewContentFactory      com.example.word                     D  getInitialApplication took 0ms
2025-06-01 21:00:29.340 21418-21455 ViewContentFactory      com.example.word                     D  packageInfo.packageName: com.miui.catcherpatch
2025-06-01 21:00:29.348 21418-21455 ViewContentFactory      com.example.word                     D  initViewContentFetcherClass took 8ms
2025-06-01 21:00:29.348 21418-21455 ContentCatcher          com.example.word                     I  ViewContentFetcher : ViewContentFetcher
2025-06-01 21:00:29.348 21418-21455 ViewContentFactory      com.example.word                     D  createInterceptor took 9ms
2025-06-01 21:00:29.350 21418-21455 ContentCatcher          com.example.word                     I  Interceptor : Catcher list <NAME_EMAIL>@264623960
2025-06-01 21:00:29.350 21418-21455 ContentCatcher          com.example.word                     I  Interceptor : Get featureInfo from config image_pick_mode
2025-06-01 21:00:29.351 21418-21455 ContentCatcher          com.example.word                     I  Interceptor : Get featureInfo from config pick_mode
2025-06-01 21:00:29.363 21418-21418 Compatibil...geReporter com.example.word                     D  Compat change id reported: 210923482; UID 10255; state: ENABLED
2025-06-01 21:00:29.407 21418-21418 IS_CTS_MODE             com.example.word                     D  false
2025-06-01 21:00:29.407 21418-21418 MULTI_WINDOW_ENABLED    com.example.word                     D  false
2025-06-01 21:00:29.409 21418-21418 DecorView[]             com.example.word                     D  getWindowModeFromSystem  windowmode is 1
2025-06-01 21:00:29.415 21418-21418 om.example.word         com.example.word                     W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
2025-06-01 21:00:29.429 21418-21456 DatabaseInitializer     com.example.word                     D  Starting database initialization
2025-06-01 21:00:29.433 21418-21418 Compatibil...geReporter com.example.word                     D  Compat change id reported: 171228096; UID 10255; state: ENABLED
2025-06-01 21:00:29.434 21418-21456 WordDatabase            com.example.word                     D  Creating database instance
2025-06-01 21:00:29.436 21418-21418 MSYNC3-Var...efreshRate com.example.word                     I  Variable refreshrate is disabled
2025-06-01 21:00:29.438 21418-21460 PowerHalWrapper         com.example.word                     I  PowerHalWrapper.getInstance 
2025-06-01 21:00:29.439 21418-21456 WordDatabase            com.example.word                     D  Database instance created successfully
2025-06-01 21:00:29.440 21418-21456 VocabularyJsonLoader    com.example.word                     D  Loading JSON file: cet4_vocabulary.json
2025-06-01 21:00:29.441 21418-21456 VocabularyJsonLoader    com.example.word                     D  Successfully loaded JSON file: cet4_vocabulary.json (8763 characters)
2025-06-01 21:00:29.442 21418-21456 DatabaseInitializer     com.example.word                     D  Current version: 1.0, Latest version: 1.0
2025-06-01 21:00:29.442 21418-21456 DatabaseInitializer     com.example.word                     D  Database already initialized with latest version
2025-06-01 21:00:29.493 21418-21455 ContentCat...ListHelper com.example.word                     I   mActivities isEmpty
2025-06-01 21:00:29.493 21418-21455 ContentCat...ListHelper com.example.word                     I   blackList size: 12 package is com.example.word isInTaplusWhiteList: true
2025-06-01 21:00:29.497 21418-21418 SurfaceFactory          com.example.word                     I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@6c61507
2025-06-01 21:00:29.501 21418-21462 WordDatabase            com.example.word                     D  Database opened, version: 2
2025-06-01 21:00:29.504 21418-21418 VRI[MainActivity]       com.example.word                     D  hardware acceleration = true, forceHwAccelerated = false
2025-06-01 21:00:29.509 21418-21418 libMEOW                 com.example.word                     D  meow new tls: 0xb4000074238c7d80
2025-06-01 21:00:29.509 21418-21418 libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:00:29.509 21418-21418 libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:00:29.511 21418-21418 Looper                  com.example.word                     W  PerfMonitor looperActivity : package=com.example.word/.MainActivity time=0ms latency=551ms running=0ms  procState=-1  historyMsgCount=4 (msgIndex=1 wall=78ms seq=1 late=7ms h=android.app.ActivityThread$H w=162) (msgIndex=3 wall=181ms seq=3 late=77ms h=android.app.ActivityThread$H w=110) (msgIndex=4 wall=295ms seq=4 late=256ms h=android.app.ActivityThread$H w=159)
2025-06-01 21:00:29.565 21418-21418 BufferQueueConsumer     com.example.word                     D  [](id:53aa00000000,api:0,p:-1,c:21418) connect: controlledByApp=false
2025-06-01 21:00:29.567 21418-21418 FBI                     com.example.word                     E  Can't load library: dlopen failed: library "libmagtsync.so" not found
2025-06-01 21:00:29.568 21418-21452 libMEOW                 com.example.word                     D  meow new tls: 0xb400007493b9e500
2025-06-01 21:00:29.568 21418-21452 libMEOW                 com.example.word                     D  applied 1 plugins for [com.example.word]:
2025-06-01 21:00:29.568 21418-21452 libMEOW                 com.example.word                     D    plugin 1: [libMEOW_gift.so]:
2025-06-01 21:00:29.578 21418-21452 libc                    com.example.word                     W  Access denied finding property "vendor.migl.debug"
2025-06-01 21:00:29.578 21418-21452 libMiGL                 com.example.word                     E  libmigl:This GPU version is note support Variable Shading Rate
2025-06-01 21:00:29.579 21418-21452 libEGL                  com.example.word                     E  pre_cache appList: ,,
2025-06-01 21:00:29.617 21418-21452 om.example.word         com.example.word                     D  MiuiProcessManagerServiceStub setSchedFifo
2025-06-01 21:00:29.617 21418-21452 MiuiProcessManagerImpl  com.example.word                     I  setSchedFifo pid:21418, mode:3
2025-06-01 21:00:29.635 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.636 21418-21452 BLASTBufferQueue        com.example.word                     D  [VRI[MainActivity]#0](f:0,a:1) acquireNextBufferLocked size=1080x2460 mFrameNumber=1 applyTransaction=true mTimestamp=260827271264930(auto) mPendingTransactions.size=0 graphicBufferId=91989609545731 transform=0
2025-06-01 21:00:29.637 21418-21452 Parcel                  com.example.word                     W  Expecting binder but got null!
2025-06-01 21:00:29.648 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.668 21418-21418 DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:00:29.669 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.670 21418-21418 HandWritingStubImpl     com.example.word                     I  refreshLastKeyboardType: 1
2025-06-01 21:00:29.670 21418-21418 HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:00:29.683 21418-21418 HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:00:29.683 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.687 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.707 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.747 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.873 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:29.874 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.064 21418-21418 DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus false
2025-06-01 21:00:31.083 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.089 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.095 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.100 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.109 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.117 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.126 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.136 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.141 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.148 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.158 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.166 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.173 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.181 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.188 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.198 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.205 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.213 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.223 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.229 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.238 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.246 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.254 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.264 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.271 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.280 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.294 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.297 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.307 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.313 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.321 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.330 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.338 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.347 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.355 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.363 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:31.378 21418-21431 om.example.word         com.example.word                     I  Compiler allocated 5099KB to compile void android.view.ViewRootImpl.performTraversals()
2025-06-01 21:00:34.519 21418-21513 ProfileInstaller        com.example.word                     D  Installing profile for com.example.word
2025-06-01 21:00:35.683 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.937 21418-21418 DecorView[]             com.example.word                     D  onWindowFocusChanged hasWindowFocus true
2025-06-01 21:00:35.939 21418-21418 HandWritingStubImpl     com.example.word                     I  refreshLastKeyboardType: 1
2025-06-01 21:00:35.939 21418-21418 HandWritingStubImpl     com.example.word                     I  getCurrentKeyboardType: 1
2025-06-01 21:00:35.951 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.955 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.965 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.970 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.978 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.985 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:35.997 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.003 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.010 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.019 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.027 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.035 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:36.214 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260833845, downTime=260833845, phoneEventTime=21:00:36.210 } moveCount:0
2025-06-01 21:00:36.362 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260833994, downTime=260833845, phoneEventTime=21:00:36.359 } moveCount:13
2025-06-01 21:00:36.478 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260834110, downTime=260834110, phoneEventTime=21:00:36.475 } moveCount:0
2025-06-01 21:00:36.580 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260834213, downTime=260834110, phoneEventTime=21:00:36.578 } moveCount:9
2025-06-01 21:00:37.157 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260834787, downTime=260834787, phoneEventTime=21:00:37.151 } moveCount:0
2025-06-01 21:00:37.187 21418-21447 om.example.word         com.example.word                     I  ProcessProfilingInfo new_methods=0 is saved saved_to_disk=0 resolve_classes_delay=8000
2025-06-01 21:00:37.205 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260834838, downTime=260834787, phoneEventTime=21:00:37.203 } moveCount:0
2025-06-01 21:00:37.218 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.224 21418-21418 OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:00:37.227 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.236 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.247 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.252 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.261 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.264 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234590): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:37.268 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:37.268 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.276 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.285 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.294 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.301 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.310 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.316 21418-21418 TTSDebugHelper          com.example.word                     D  Checking TTS availability...
2025-06-01 21:00:37.317 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.320 21418-21418 TTSDebugHelper          com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:00:37.320 21418-21418 VocabularyFragment      com.example.word                     D  TTS availability check: false - TTS初始化失败: -1
2025-06-01 21:00:37.322 21418-21418 Compatibil...geReporter com.example.word                     D  Compat change id reported: 147798919; UID 10255; state: ENABLED
2025-06-01 21:00:37.326 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.328 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:37.328 21418-21418 VocabularyFragment      com.example.word                     D  默认TTS引擎: null
2025-06-01 21:00:37.330 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:37.330 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: TTS设置可用: true
2025-06-01 21:00:37.330 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: 系统语言: zh_CN
2025-06-01 21:00:37.330 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: 可用TTS引擎数量: 0
2025-06-01 21:00:37.330 21418-21418 TTSFallbackManager      com.example.word                     D  Checking TTS availability...
2025-06-01 21:00:37.332 21418-21418 TTSFallbackManager      com.example.word                     E  TTS initialization failed
2025-06-01 21:00:37.332 21418-21418 VocabularyFragment      com.example.word                     D  TTS Fallback check: false - TTS初始化失败
2025-06-01 21:00:37.334 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.338 21418-21418 TTSFallbackManager      com.example.word                     D  Suggested installing TTS engine
2025-06-01 21:00:37.339 21418-21418 SimpleTTSManager        com.example.word                     D  Initializing TTS...
2025-06-01 21:00:37.341 21418-21418 SimpleTTSManager        com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:00:37.341 21418-21418 VocabularyFragment      com.example.word                     D  Simple TTS initialization: false
2025-06-01 21:00:37.342 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.350 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.358 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.366 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.376 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.377 21418-21418 TTSManager              com.example.word                     E  System TTS initialization failed
2025-06-01 21:00:37.378 21418-21418 VocabularyFragment      com.example.word                     D  === Database Debug Info ===
2025-06-01 21:00:37.378 21418-21418 DatabaseDebugHelper     com.example.word                     D  1.txt file found, size: 351644 bytes
2025-06-01 21:00:37.378 21418-21418 VocabularyFragment      com.example.word                     D  1.txt file exists: true
2025-06-01 21:00:37.379 21418-21456 DatabaseDebugHelper     com.example.word                     D  Testing data organization...
2025-06-01 21:00:37.379 21418-21456 DataOrganizer           com.example.word                     D  Starting to organize data from 1.txt
2025-06-01 21:00:37.381 21418-21456 DataOrganizer           com.example.word                     D  Found high frequency words section
2025-06-01 21:00:37.383 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.391 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.400 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.405 21418-21456 DataOrganizer           com.example.word                     D  Found phrases section
2025-06-01 21:00:37.408 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.418 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.416 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234591): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:37.419 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:37.427 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.433 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.441 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.451 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.459 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.468 21418-21456 DataOrganizer           com.example.word                     D  Found essay sentences section
2025-06-01 21:00:37.468 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.476 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.482 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.487 21418-21456 DataOrganizer           com.example.word                     D  Found essay templates section
2025-06-01 21:00:37.492 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.499 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.507 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.524 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.525 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:37.524 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234592): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:37.525 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.541 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:37.540 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234593): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:37.544 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.545 21418-21456 DataOrganizer           com.example.word                     D  Found CET-4 words section
2025-06-01 21:00:37.546 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.551 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.559 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.568 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.576 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.585 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.593 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.601 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.608 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.615 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.625 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.632 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.640 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.649 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.654 21418-21456 DataOrganizer           com.example.word                     D  Data organization completed:
2025-06-01 21:00:37.654 21418-21456 DataOrganizer           com.example.word                     D    Words: 1175
2025-06-01 21:00:37.654 21418-21456 DataOrganizer           com.example.word                     D    Phrases: 263
2025-06-01 21:00:37.654 21418-21456 DataOrganizer           com.example.word                     D    Essay Templates: 0
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D  Data organization results:
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D    Words organized: 1175
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D    Phrases organized: 263
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D    Templates organized: 0
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D    First word: available - 可利用的，可得到
2025-06-01 21:00:37.654 21418-21456 DatabaseDebugHelper     com.example.word                     D    First phrase: a series of - 一系列,一连串
2025-06-01 21:00:37.657 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.657 21418-21418 VocabularyFragment      com.example.word                     D  Text parsing result: TextParsingResult(wordCount=1175, phraseCount=263, templateCount=0, success=true, error=null)
2025-06-01 21:00:37.663 21418-21456 DatabaseDebugHelper     com.example.word                     D  Database Status:
2025-06-01 21:00:37.664 21418-21456 DatabaseDebugHelper     com.example.word                     D    Words: 10
2025-06-01 21:00:37.664 21418-21456 DatabaseDebugHelper     com.example.word                     D    Phrases: 5
2025-06-01 21:00:37.664 21418-21456 DatabaseDebugHelper     com.example.word                     D    Templates: 3
2025-06-01 21:00:37.664 21418-21456 DatabaseDebugHelper     com.example.word                     D    Initialized: true
2025-06-01 21:00:37.664 21418-21456 DatabaseDebugHelper     com.example.word                     D    Version: 1.0
2025-06-01 21:00:37.664 21418-21418 VocabularyFragment      com.example.word                     D  Database status: DatabaseStatus(wordCount=10, phraseCount=5, templateCount=3, isInitialized=true, currentVersion=1.0, error=null)
2025-06-01 21:00:37.664 21418-21418 VocabularyFragment      com.example.word                     D  === End Database Debug Info ===
2025-06-01 21:00:37.665 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.674 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.682 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.690 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.698 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.706 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.715 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.723 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.731 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.739 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.747 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.755 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.764 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.772 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.781 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.789 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.797 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.805 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.814 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.822 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.830 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.839 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.847 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.855 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.863 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.872 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.880 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.888 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.896 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.905 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.913 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.921 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.930 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.938 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.946 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.954 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.963 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.971 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.979 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.988 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:37.996 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.004 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.013 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.021 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.029 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.037 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.045 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.054 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.062 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.070 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.078 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.357 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260835988, downTime=260835988, phoneEventTime=21:00:38.353 } moveCount:0
2025-06-01 21:00:38.405 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260836037, downTime=260835988, phoneEventTime=21:00:38.402 } moveCount:0
2025-06-01 21:00:38.416 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.422 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.431 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.436 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:38.436 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234594): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:38.440 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.447 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.456 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.464 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.471 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.480 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.488 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.496 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.505 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.513 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.522 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.530 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.542 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.545 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.556 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.564 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.571 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.580 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.588 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.597 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.605 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.613 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.621 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.630 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.638 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.646 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.653 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.662 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.671 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:38.668 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234595): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:38.672 21418-21418 TextToSpeech            com.example.word                     W  stop failed: not bound to TTS engine
2025-06-01 21:00:38.672 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:38.672 21418-21418 SimpleTTSManager        com.example.word                     D  TTS resources released
2025-06-01 21:00:38.673 21418-21418 TextToSpeech            com.example.word                     W  stop failed: not bound to TTS engine
2025-06-01 21:00:38.673 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:38.677 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.679 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.686 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.695 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.703 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.711 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.720 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.728 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.736 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.745 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.752 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.761 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.769 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.777 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.785 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.794 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.802 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.810 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.819 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.827 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.836 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.844 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.852 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.860 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.868 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.876 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.885 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.893 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.901 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.909 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.918 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.931 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.943 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.952 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.960 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.968 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.977 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.979 21418-21432 om.example.word         com.example.word                     I  This is non sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:00:38.985 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:38.993 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.002 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.009 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.014 21418-21434 System                  com.example.word                     W  A resource failed to call close. 
2025-06-01 21:00:39.018 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.026 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.034 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.042 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.051 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.059 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.059 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260836688, downTime=260836688, phoneEventTime=21:00:39.052 } moveCount:0
2025-06-01 21:00:39.067 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.075 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.084 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.092 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.100 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.108 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.117 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.125 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.133 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.141 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.150 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.156 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260836786, downTime=260836688, phoneEventTime=21:00:39.151 } moveCount:0
2025-06-01 21:00:39.170 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.175 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.184 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.184 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234596): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:39.187 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:39.192 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.197 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:39.196 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234597): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:39.200 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.208 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.217 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.224 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.233 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.241 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.250 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.257 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.266 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.274 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.283 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.291 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.299 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.308 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.319 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.324 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.332 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.340 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.348 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.357 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.365 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.373 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.382 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.390 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.398 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.406 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.415 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.423 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.431 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.439 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.448 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.457 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.466 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.473 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.506 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.508 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260837121, downTime=260837121, phoneEventTime=21:00:39.486 } moveCount:0
2025-06-01 21:00:39.510 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.514 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.520 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.528 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.536 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.543 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.550 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.559 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.567 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.575 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.578 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260837208, downTime=260837121, phoneEventTime=21:00:39.573 } moveCount:0
2025-06-01 21:00:39.586 21418-21418 NavigationUI            com.example.word                     I  Ignoring onNavDestinationSelected for MenuItem com.example.word:id/navigation_settings as it cannot be found from the current destination Destination(com.example.word:id/navigation_progress) label=进度 class=com.example.word.ui.progress.ProgressFragment (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Navigation action/destination com.example.word:id/navigation_settings cannot be found from the current destination Destination(com.example.word:id/navigation_progress) label=进度 class=com.example.word.ui.progress.ProgressFragment
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1687)
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1605)
                                                                                                    	at androidx.navigation.ui.NavigationUI.onNavDestinationSelected(NavigationUI.kt:96)
                                                                                                    	at androidx.navigation.ui.NavigationUI.setupWithNavController$lambda$6(NavigationUI.kt:631)
                                                                                                    	at androidx.navigation.ui.NavigationUI.$r8$lambda$WstRxbcmMw_4X03-oLYYK21cjjU(Unknown Source:0)
                                                                                                    	at androidx.navigation.ui.NavigationUI$$ExternalSyntheticLambda4.onNavigationItemSelected(D8$$SyntheticClass:0)
                                                                                                    	at com.google.android.material.navigation.NavigationBarView$1.onMenuItemSelected(NavigationBarView.java:311)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.dispatchMenuItemSelected(MenuBuilder.java:833)
                                                                                                    	at androidx.appcompat.view.menu.MenuItemImpl.invoke(MenuItemImpl.java:157)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.performItemAction(MenuBuilder.java:984)
                                                                                                    	at com.google.android.material.navigation.NavigationBarMenuView$1.onClick(NavigationBarMenuView.java:141)
                                                                                                    	at android.view.View.performClick(View.java:7564)
                                                                                                    	at android.view.View.performClickInternal(View.java:7537)
                                                                                                    	at android.view.View.-$$Nest$mperformClickInternal(Unknown Source:0)
                                                                                                    	at android.view.View$PerformClick.run(View.java:29761)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:210)
                                                                                                    	at android.os.Looper.loop(Looper.java:299)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8136)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1028)
2025-06-01 21:00:39.587 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.595 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.603 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.611 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.621 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.629 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.637 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.645 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.656 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.662 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.669 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.679 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.684 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.695 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.703 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.713 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.721 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.730 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.738 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.747 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.755 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.763 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.771 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.779 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.787 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.797 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.804 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.813 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.820 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.829 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.837 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.841 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260837465, downTime=260837465, phoneEventTime=21:00:39.830 } moveCount:0
2025-06-01 21:00:39.845 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.853 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.861 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.870 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.878 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.886 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.895 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.903 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.911 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.919 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.928 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.936 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.936 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260837568, downTime=260837465, phoneEventTime=21:00:39.932 } moveCount:0
2025-06-01 21:00:39.946 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.953 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.961 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.970 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.978 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.986 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:39.995 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.002 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.011 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.020 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.030 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.038 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.039 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260837668, downTime=260837668, phoneEventTime=21:00:40.033 } moveCount:0
2025-06-01 21:00:40.044 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.052 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.061 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.069 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.078 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.086 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.093 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.102 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.110 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.118 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.126 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.134 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.143 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.144 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260837775, downTime=260837668, phoneEventTime=21:00:40.139 } moveCount:1
2025-06-01 21:00:40.151 21418-21418 NavigationUI            com.example.word                     I  Ignoring onNavDestinationSelected for MenuItem com.example.word:id/navigation_settings as it cannot be found from the current destination Destination(com.example.word:id/navigation_home) label=首页 class=com.example.word.ui.home.HomeFragment (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Navigation action/destination com.example.word:id/navigation_settings cannot be found from the current destination Destination(com.example.word:id/navigation_home) label=首页 class=com.example.word.ui.home.HomeFragment
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1687)
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1605)
                                                                                                    	at androidx.navigation.ui.NavigationUI.onNavDestinationSelected(NavigationUI.kt:96)
                                                                                                    	at androidx.navigation.ui.NavigationUI.setupWithNavController$lambda$6(NavigationUI.kt:631)
                                                                                                    	at androidx.navigation.ui.NavigationUI.$r8$lambda$WstRxbcmMw_4X03-oLYYK21cjjU(Unknown Source:0)
                                                                                                    	at androidx.navigation.ui.NavigationUI$$ExternalSyntheticLambda4.onNavigationItemSelected(D8$$SyntheticClass:0)
                                                                                                    	at com.google.android.material.navigation.NavigationBarView$1.onMenuItemSelected(NavigationBarView.java:311)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.dispatchMenuItemSelected(MenuBuilder.java:833)
                                                                                                    	at androidx.appcompat.view.menu.MenuItemImpl.invoke(MenuItemImpl.java:157)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.performItemAction(MenuBuilder.java:984)
                                                                                                    	at com.google.android.material.navigation.NavigationBarMenuView$1.onClick(NavigationBarMenuView.java:141)
                                                                                                    	at android.view.View.performClick(View.java:7564)
                                                                                                    	at android.view.View.performClickInternal(View.java:7537)
                                                                                                    	at android.view.View.-$$Nest$mperformClickInternal(Unknown Source:0)
                                                                                                    	at android.view.View$PerformClick.run(View.java:29761)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:210)
                                                                                                    	at android.os.Looper.loop(Looper.java:299)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8136)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1028)
2025-06-01 21:00:40.151 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.161 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.168 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.178 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.185 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.193 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.202 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.210 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.218 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.227 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.234 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.244 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.251 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.260 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.267 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.277 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.284 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.292 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.301 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.309 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.318 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.326 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.334 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.342 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.351 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.358 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.366 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.375 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.384 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.388 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260838016, downTime=260838016, phoneEventTime=21:00:40.381 } moveCount:0
2025-06-01 21:00:40.390 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.398 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.407 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.415 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.423 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.432 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.440 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.447 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.453 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260838086, downTime=260838016, phoneEventTime=21:00:40.450 } moveCount:0
2025-06-01 21:00:40.459 21418-21418 OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:00:40.459 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.466 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.474 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.483 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.491 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.499 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.507 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.515 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.524 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.532 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.540 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.549 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.557 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.565 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.575 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.582 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.590 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.598 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.606 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.615 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.623 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.631 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.640 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.648 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.666 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260838292, downTime=260838292, phoneEventTime=21:00:40.657 } moveCount:0
2025-06-01 21:00:40.675 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.688 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.691 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.699 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.707 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.716 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.724 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.731 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260838363, downTime=260838292, phoneEventTime=21:00:40.728 } moveCount:0
2025-06-01 21:00:40.732 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.739 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.751 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.761 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.766 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.772 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.780 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.788 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.797 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.805 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.811 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260838392, downTime=260838392, phoneEventTime=21:00:40.757 } moveCount:0
2025-06-01 21:00:40.813 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.821 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.831 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.838 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.847 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.854 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.863 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.872 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.880 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.890 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.898 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260838524, downTime=260838392, phoneEventTime=21:00:40.889 } moveCount:0
2025-06-01 21:00:40.898 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.905 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.913 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.921 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.930 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.938 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.946 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.954 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.962 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.971 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.986 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.988 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:40.996 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.004 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.014 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.023 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.030 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.038 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.048 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.055 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.065 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.071 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.080 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.086 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.096 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.103 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.111 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.124 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.128 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.128 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260838760, downTime=260838760, phoneEventTime=21:00:41.125 } moveCount:0
2025-06-01 21:00:41.136 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.144 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.151 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.160 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.168 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.176 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.184 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.193 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.201 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.202 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260838834, downTime=260838760, phoneEventTime=21:00:41.199 } moveCount:0
2025-06-01 21:00:41.211 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.212 21418-21418 OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:00:41.219 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.228 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.236 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.244 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.252 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.260 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.269 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.277 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.285 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.293 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.301 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.310 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.318 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.326 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.335 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.343 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.351 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.360 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.368 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.371 21418-21432 om.example.word         com.example.word                     I  This is sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:00:41.377 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.385 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.393 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.401 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.409 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.417 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.426 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.441 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.449 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.454 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.462 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.471 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.478 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.490 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.495 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.507 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.511 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.522 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.524 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.535 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.541 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.551 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.559 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.567 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.576 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.584 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.592 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.600 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.609 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.616 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.625 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.634 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.642 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.651 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.658 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.666 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.676 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.683 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.691 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.699 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.708 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.716 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.723 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.733 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.741 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.749 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.757 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.766 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.774 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.782 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.791 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.798 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.807 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.815 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.823 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.832 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.840 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.849 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.857 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.865 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.873 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.882 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.890 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.898 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.906 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.914 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.925 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.928 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260839555, downTime=260839555, phoneEventTime=21:00:41.920 } moveCount:0
2025-06-01 21:00:41.930 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.939 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.947 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.955 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.963 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.972 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.980 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.988 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:41.996 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.005 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.009 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260839641, downTime=260839555, phoneEventTime=21:00:42.006 } moveCount:0
2025-06-01 21:00:42.015 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.015 21418-21418 NavigationUI            com.example.word                     I  Ignoring onNavDestinationSelected for MenuItem com.example.word:id/navigation_settings as it cannot be found from the current destination Destination(com.example.word:id/navigation_progress) label=进度 class=com.example.word.ui.progress.ProgressFragment (Ask Gemini)
                                                                                                    java.lang.IllegalArgumentException: Navigation action/destination com.example.word:id/navigation_settings cannot be found from the current destination Destination(com.example.word:id/navigation_progress) label=进度 class=com.example.word.ui.progress.ProgressFragment
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1687)
                                                                                                    	at androidx.navigation.NavController.navigate(NavController.kt:1605)
                                                                                                    	at androidx.navigation.ui.NavigationUI.onNavDestinationSelected(NavigationUI.kt:96)
                                                                                                    	at androidx.navigation.ui.NavigationUI.setupWithNavController$lambda$6(NavigationUI.kt:631)
                                                                                                    	at androidx.navigation.ui.NavigationUI.$r8$lambda$WstRxbcmMw_4X03-oLYYK21cjjU(Unknown Source:0)
                                                                                                    	at androidx.navigation.ui.NavigationUI$$ExternalSyntheticLambda4.onNavigationItemSelected(D8$$SyntheticClass:0)
                                                                                                    	at com.google.android.material.navigation.NavigationBarView$1.onMenuItemSelected(NavigationBarView.java:311)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.dispatchMenuItemSelected(MenuBuilder.java:833)
                                                                                                    	at androidx.appcompat.view.menu.MenuItemImpl.invoke(MenuItemImpl.java:157)
                                                                                                    	at androidx.appcompat.view.menu.MenuBuilder.performItemAction(MenuBuilder.java:984)
                                                                                                    	at com.google.android.material.navigation.NavigationBarMenuView$1.onClick(NavigationBarMenuView.java:141)
                                                                                                    	at android.view.View.performClick(View.java:7564)
                                                                                                    	at android.view.View.performClickInternal(View.java:7537)
                                                                                                    	at android.view.View.-$$Nest$mperformClickInternal(Unknown Source:0)
                                                                                                    	at android.view.View$PerformClick.run(View.java:29761)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:210)
                                                                                                    	at android.os.Looper.loop(Looper.java:299)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8136)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:580)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1028)
2025-06-01 21:00:42.021 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.032 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.038 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.048 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.057 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.063 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.072 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.081 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.089 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.097 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.105 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.113 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.122 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.131 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.139 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.146 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.154 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.163 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.171 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.179 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260839807, downTime=260839807, phoneEventTime=21:00:42.171 } moveCount:0
2025-06-01 21:00:42.179 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.187 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.195 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.202 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.211 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.220 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.228 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.237 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.245 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.253 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.261 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.266 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260839898, downTime=260839807, phoneEventTime=21:00:42.262 } moveCount:0
2025-06-01 21:00:42.270 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.278 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.287 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.295 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.303 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.312 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.320 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.328 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.336 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.347 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.356 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.362 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.371 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.380 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.387 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.397 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.403 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.413 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.420 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.429 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.436 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.445 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.453 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.461 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.470 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.477 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.486 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.495 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.505 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.510 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.521 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.526 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.537 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.545 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.551 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.560 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.570 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.576 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.585 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.593 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.602 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.610 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.618 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.627 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.634 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.643 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.652 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.659 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.669 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.676 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.684 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.692 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.701 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.709 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.717 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.727 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.734 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.743 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.749 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.759 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.767 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.775 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.786 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.791 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.802 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.808 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.817 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.826 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.834 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.842 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.849 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.858 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.867 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.874 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.883 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.892 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.899 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.908 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.916 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.925 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.932 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.942 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.948 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.958 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.965 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.974 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.982 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.991 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:42.998 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.008 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.015 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.024 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.031 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.041 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.048 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.056 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.065 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.074 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.082 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.091 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.099 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.107 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.114 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.122 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.131 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.530 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260841156, downTime=260841156, phoneEventTime=21:00:43.521 } moveCount:0
2025-06-01 21:00:43.589 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260841222, downTime=260841156, phoneEventTime=21:00:43.586 } moveCount:0
2025-06-01 21:00:43.816 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260841446, downTime=260841446, phoneEventTime=21:00:43.811 } moveCount:0
2025-06-01 21:00:43.884 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260841516, downTime=260841446, phoneEventTime=21:00:43.880 } moveCount:0
2025-06-01 21:00:43.928 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.935 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.944 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.953 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.958 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.967 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.970 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260841599, downTime=260841599, phoneEventTime=21:00:43.963 } moveCount:0
2025-06-01 21:00:43.974 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.983 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.990 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:43.999 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.007 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.015 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.024 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.032 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.033 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260841665, downTime=260841599, phoneEventTime=21:00:44.030 } moveCount:0
2025-06-01 21:00:44.043 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.052 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.057 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.067 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.074 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.085 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.090 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.100 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.107 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.116 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.126 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.132 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.142 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.149 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.158 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.166 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.174 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.182 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.360 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260841988, downTime=260841988, phoneEventTime=21:00:44.353 } moveCount:0
2025-06-01 21:00:44.421 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260842054, downTime=260841988, phoneEventTime=21:00:44.418 } moveCount:0
2025-06-01 21:00:44.434 21418-21418 OnBackInvokedCallback   com.example.word                     W  OnBackInvokedCallback is not enabled for the application.
                                                                                                    Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
2025-06-01 21:00:44.435 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.446 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.455 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.463 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.472 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.476 21418-21418 TTSDebugHelper          com.example.word                     D  Checking TTS availability...
2025-06-01 21:00:44.480 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.481 21418-21418 TTSDebugHelper          com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:00:44.481 21418-21418 VocabularyFragment      com.example.word                     D  TTS availability check: false - TTS初始化失败: -1
2025-06-01 21:00:44.488 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.491 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:44.491 21418-21418 VocabularyFragment      com.example.word                     D  默认TTS引擎: null
2025-06-01 21:00:44.493 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:00:44.493 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: TTS设置可用: true
2025-06-01 21:00:44.493 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: 系统语言: zh_CN
2025-06-01 21:00:44.493 21418-21418 VocabularyFragment      com.example.word                     D  TTS System Info: 可用TTS引擎数量: 0
2025-06-01 21:00:44.493 21418-21418 TTSFallbackManager      com.example.word                     D  Checking TTS availability...
2025-06-01 21:00:44.494 21418-21418 TTSFallbackManager      com.example.word                     E  TTS initialization failed
2025-06-01 21:00:44.494 21418-21418 VocabularyFragment      com.example.word                     D  TTS Fallback check: false - TTS初始化失败
2025-06-01 21:00:44.496 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.497 21418-21418 TTSFallbackManager      com.example.word                     D  Suggested installing TTS engine
2025-06-01 21:00:44.497 21418-21418 SimpleTTSManager        com.example.word                     D  Initializing TTS...
2025-06-01 21:00:44.499 21418-21418 SimpleTTSManager        com.example.word                     E  TTS initialization failed with status: -1
2025-06-01 21:00:44.499 21418-21418 VocabularyFragment      com.example.word                     D  Simple TTS initialization: false
2025-06-01 21:00:44.502 21418-21418 TTSManager              com.example.word                     E  System TTS initialization failed
2025-06-01 21:00:44.503 21418-21418 VocabularyFragment      com.example.word                     D  === Database Debug Info ===
2025-06-01 21:00:44.503 21418-21418 DatabaseDebugHelper     com.example.word                     D  1.txt file found, size: 351644 bytes
2025-06-01 21:00:44.503 21418-21418 VocabularyFragment      com.example.word                     D  1.txt file exists: true
2025-06-01 21:00:44.503 21418-21456 DatabaseDebugHelper     com.example.word                     D  Testing data organization...
2025-06-01 21:00:44.503 21418-21456 DataOrganizer           com.example.word                     D  Starting to organize data from 1.txt
2025-06-01 21:00:44.504 21418-21456 DataOrganizer           com.example.word                     D  Found high frequency words section
2025-06-01 21:00:44.504 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.512 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.520 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.523 21418-21456 DataOrganizer           com.example.word                     D  Found phrases section
2025-06-01 21:00:44.529 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.565 21418-21456 DataOrganizer           com.example.word                     D  Found essay sentences section
2025-06-01 21:00:44.591 21418-21456 DataOrganizer           com.example.word                     D  Found essay templates section
2025-06-01 21:00:44.598 21418-21432 om.example.word         com.example.word                     I  This is non sticky GC, maxfree is 8388608 minfree is 524288
2025-06-01 21:00:44.602 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.607 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.611 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.623 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.628 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.637 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.644 21418-21456 DataOrganizer           com.example.word                     D  Found CET-4 words section
2025-06-01 21:00:44.644 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.653 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.661 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.671 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.677 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.686 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.693 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.702 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.710 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.718 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.726 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.736 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.743 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.750 21418-21456 DataOrganizer           com.example.word                     D  Data organization completed:
2025-06-01 21:00:44.750 21418-21456 DataOrganizer           com.example.word                     D    Words: 1175
2025-06-01 21:00:44.750 21418-21456 DataOrganizer           com.example.word                     D    Phrases: 263
2025-06-01 21:00:44.750 21418-21456 DataOrganizer           com.example.word                     D    Essay Templates: 0
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D  Data organization results:
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D    Words organized: 1175
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D    Phrases organized: 263
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D    Templates organized: 0
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D    First word: available - 可利用的，可得到
2025-06-01 21:00:44.751 21418-21456 DatabaseDebugHelper     com.example.word                     D    First phrase: a series of - 一系列,一连串
2025-06-01 21:00:44.751 21418-21418 VocabularyFragment      com.example.word                     D  Text parsing result: TextParsingResult(wordCount=1175, phraseCount=263, templateCount=0, success=true, error=null)
2025-06-01 21:00:44.752 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D  Database Status:
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D    Words: 10
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D    Phrases: 5
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D    Templates: 3
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D    Initialized: true
2025-06-01 21:00:44.757 21418-21456 DatabaseDebugHelper     com.example.word                     D    Version: 1.0
2025-06-01 21:00:44.757 21418-21418 VocabularyFragment      com.example.word                     D  Database status: DatabaseStatus(wordCount=10, phraseCount=5, templateCount=3, isInitialized=true, currentVersion=1.0, error=null)
2025-06-01 21:00:44.758 21418-21418 VocabularyFragment      com.example.word                     D  === End Database Debug Info ===
2025-06-01 21:00:44.760 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.768 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.777 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.785 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.793 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.802 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.810 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.817 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.827 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.834 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.842 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.851 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.860 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.868 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.876 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.884 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.893 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.901 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.909 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.917 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.925 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.934 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:44.942 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.236 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260842865, downTime=260842865, phoneEventTime=21:00:45.229 } moveCount:0
2025-06-01 21:00:45.297 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.304 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.313 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.320 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.328 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.338 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.350 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260842969, downTime=260842865, phoneEventTime=21:00:45.334 } moveCount:8
2025-06-01 21:00:45.357 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.363 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.370 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.380 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.387 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.396 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234598): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:45.398 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:45.407 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.410 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.412 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.420 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.431 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:45.428 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234599): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:45.439 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.442 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.446 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.453 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.460 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234600): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:45.461 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:45.464 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.472 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.479 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.487 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.495 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.500 21418-21418 FramePolicy             com.example.word                     W  type=1400 audit(0.0:234601): avc: denied { ioctl } for path="/proc/perfmgr/perf_ioctl" dev="proc" ino=4026535427 ioctlcmd=0x6716 scontext=u:r:untrusted_app:s0:c255,c256,c512,c768 tcontext=u:object_r:proc_perfmgr:s0 tclass=file permissive=0 app=com.example.word
2025-06-01 21:00:45.503 21418-21460 libPerfCtl              com.example.word                     I  fbcNotifySbeRescue ret=-1
2025-06-01 21:00:45.507 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.512 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.519 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.528 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.535 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.547 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260843176, downTime=260843176, phoneEventTime=21:00:45.541 } moveCount:0
2025-06-01 21:00:45.547 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.554 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.559 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.568 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.576 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.585 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.593 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.601 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.610 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.618 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.626 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.631 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260843262, downTime=260843176, phoneEventTime=21:00:45.627 } moveCount:7
2025-06-01 21:00:45.634 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.643 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.652 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.660 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.669 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.677 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.685 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.693 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.701 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.709 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.718 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.725 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.734 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.742 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.751 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.760 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.768 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.775 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.784 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.792 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.801 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.809 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.817 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.825 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.831 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260843457, downTime=260843457, phoneEventTime=21:00:45.821 } moveCount:0
2025-06-01 21:00:45.835 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.841 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.849 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.858 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.867 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.874 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.882 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.891 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.900 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.908 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.915 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.921 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260843552, downTime=260843457, phoneEventTime=21:00:45.917 } moveCount:8
2025-06-01 21:00:45.924 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.934 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.942 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.950 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.958 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.966 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.974 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.982 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.991 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:45.999 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.008 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.016 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.025 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.033 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.041 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.049 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.058 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.066 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.074 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.082 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.090 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.099 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.107 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.115 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.124 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.132 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.140 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.149 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.156 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.165 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.173 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.181 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.190 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.197 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.206 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.214 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.222 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.231 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.238 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.248 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.256 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.264 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.273 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.280 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:46.289 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.201 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260845832, downTime=260845832, phoneEventTime=21:00:48.196 } moveCount:0
2025-06-01 21:00:48.248 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260845881, downTime=260845832, phoneEventTime=21:00:48.246 } moveCount:0
2025-06-01 21:00:48.267 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.270 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.283 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.291 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.300 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.310 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.315 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.324 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.332 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.342 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.348 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.359 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.365 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.375 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.383 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.391 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.400 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.408 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.415 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.424 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.432 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.441 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.450 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.458 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.466 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.475 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.481 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.491 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.500 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.508 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.516 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.522 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.532 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.540 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.548 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.558 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.564 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.573 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.581 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.590 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.597 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.606 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.615 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.622 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.632 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.640 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.647 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.655 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.664 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.673 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.680 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.690 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.698 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.706 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.715 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.721 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.731 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.738 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.748 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.756 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.764 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.773 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.779 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.789 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.797 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.804 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.814 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.821 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.830 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.838 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.846 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.854 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.863 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.872 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.880 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.888 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.896 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.904 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.912 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.920 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.929 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.938 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.945 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.955 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.962 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.970 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.978 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.988 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:48.995 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.003 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.011 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.020 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.028 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.036 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.044 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.053 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.061 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.069 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.078 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.086 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.094 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.101 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:00:49.111 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:05.999 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1, eventTime=260863623, downTime=260863623, phoneEventTime=21:01:05.987 } moveCount:0
2025-06-01 21:01:06.065 21418-21418 MIUIInput               com.example.word                     D  [MotionEvent] ViewRootImpl windowName 'com.example.word/com.example.word.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1, eventTime=260863697, downTime=260863623, phoneEventTime=21:01:06.061 } moveCount:0
2025-06-01 21:01:06.072 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.079 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.091 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.096 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.103 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.111 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.119 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.127 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.135 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.144 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.155 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.160 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.171 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.177 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.188 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.196 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.202 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.212 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.219 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.228 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.236 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.244 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.253 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.260 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.269 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.277 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.285 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.294 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.297 21418-21418 TextToSpeech            com.example.word                     W  stop failed: not bound to TTS engine
2025-06-01 21:01:06.297 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:01:06.297 21418-21418 SimpleTTSManager        com.example.word                     D  TTS resources released
2025-06-01 21:01:06.297 21418-21418 TextToSpeech            com.example.word                     W  stop failed: not bound to TTS engine
2025-06-01 21:01:06.297 21418-21418 TextToSpeech            com.example.word                     W  shutdown failed: not bound to TTS engine
2025-06-01 21:01:06.300 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.310 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.319 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.326 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.335 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.343 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.351 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.361 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.368 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.376 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.384 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.393 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.402 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.409 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.418 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.426 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.434 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.441 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.451 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.458 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.467 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.475 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.483 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.492 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.500 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.509 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.517 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.525 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.534 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.542 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.550 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.558 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.567 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.574 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.582 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.590 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.599 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.608 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.616 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.624 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.632 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.640 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.649 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.657 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.665 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.675 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.681 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.690 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.700 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.707 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.715 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.724 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.731 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.740 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.749 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.756 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.765 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.773 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.781 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.789 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.798 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.806 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.815 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.823 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.830 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.839 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.847 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.856 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.864 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.873 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.881 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.888 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.897 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.906 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.913 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
2025-06-01 21:01:06.921 21418-21452 gralloc4                com.example.word                     E  Empty SMPTE 2094-40 data
