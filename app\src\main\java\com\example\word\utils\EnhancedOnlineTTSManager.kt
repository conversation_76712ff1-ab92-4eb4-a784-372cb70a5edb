package com.example.word.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*

/**
 * 增强的在线TTS管理器
 * 集成OnlineTTSHelper，提供完整的在线TTS解决方案
 */
class EnhancedOnlineTTSManager(private val context: Context) {
    
    companion object {
        private const val TAG = "EnhancedOnlineTTSManager"
    }
    
    private var isInitialized = false
    private var availableVoices: List<OnlineTTSHelper.TTSVoice> = emptyList()
    private var preferredVoice: OnlineTTSHelper.TTSVoice? = null
    
    /**
     * 初始化在线TTS
     */
    fun initialize(callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        if (isInitialized) {
            Log.d(TAG, "Already initialized")
            callback(true, "在线TTS已初始化")
            return
        }
        
        Log.d(TAG, "Initializing Enhanced Online TTS...")
        
        // 获取可用语音列表
        OnlineTTSHelper.getAvailableVoices { success, voices, message ->
            if (success && voices.isNotEmpty()) {
                availableVoices = voices
                preferredVoice = OnlineTTSHelper.getRecommendedEnglishVoice()
                isInitialized = true
                
                Log.d(TAG, "Initialization successful: ${voices.size} voices available")
                Log.d(TAG, "Preferred voice: ${preferredVoice?.displayName ?: "None"}")
                
                callback(true, "在线TTS初始化成功，获取到${voices.size}个语音")
            } else {
                Log.e(TAG, "Initialization failed: $message")
                callback(false, "在线TTS初始化失败: $message")
            }
        }
    }
    
    /**
     * 播放文本
     */
    fun speak(text: String, slowSpeed: Boolean = false, callback: ((Boolean, String) -> Unit)? = null) {
        if (!isInitialized) {
            Log.w(TAG, "Not initialized, attempting to initialize first")
            initialize { success, message ->
                if (success) {
                    speak(text, slowSpeed, callback)
                } else {
                    callback?.invoke(false, "初始化失败: $message")
                }
            }
            return
        }
        
        if (text.isBlank()) {
            callback?.invoke(false, "文本为空")
            return
        }
        
        Log.d(TAG, "Speaking: '$text', slow: $slowSpeed")
        
        val speed = if (slowSpeed) 0.7f else 1.0f
        val voice = preferredVoice
        
        if (voice == null) {
            callback?.invoke(false, "没有可用的语音")
            return
        }
        
        OnlineTTSHelper.generateTTSAudio(text, voice, speed) { success, message ->
            Log.d(TAG, "TTS generation result: $success - $message")
            callback?.invoke(success, message)
        }
    }
    
    /**
     * 测试在线TTS功能
     */
    fun testOnlineTTS(callback: (String) -> Unit) {
        Log.d(TAG, "Testing online TTS functionality...")
        
        OnlineTTSHelper.simpleAPITest { result ->
            val enhancedResult = buildString {
                appendLine("=== 增强在线TTS测试结果 ===")
                appendLine("初始化状态: $isInitialized")
                appendLine("可用语音数量: ${availableVoices.size}")
                appendLine("首选语音: ${preferredVoice?.displayName ?: "未设置"}")
                appendLine()
                appendLine(result)
                appendLine()
                appendLine("=== 语音列表详情 ===")
                
                if (availableVoices.isNotEmpty()) {
                    val englishVoices = availableVoices.filter { it.isEnglish }
                    val chineseVoices = availableVoices.filter { it.isChinese }
                    
                    appendLine("英语语音 (${englishVoices.size}个):")
                    englishVoices.take(3).forEach { voice ->
                        appendLine("  - ${voice.displayName}")
                    }
                    
                    appendLine("中文语音 (${chineseVoices.size}个):")
                    chineseVoices.take(3).forEach { voice ->
                        appendLine("  - ${voice.displayName}")
                    }
                } else {
                    appendLine("暂无可用语音")
                }
            }
            
            Log.d(TAG, enhancedResult)
            callback(enhancedResult)
        }
    }
    
    /**
     * 获取状态信息
     */
    fun getStatus(): String {
        return buildString {
            appendLine("=== 增强在线TTS状态 ===")
            appendLine("初始化状态: $isInitialized")
            appendLine("可用语音数量: ${availableVoices.size}")
            appendLine("首选语音: ${preferredVoice?.displayName ?: "未设置"}")
            
            if (availableVoices.isNotEmpty()) {
                val englishCount = availableVoices.count { it.isEnglish }
                val chineseCount = availableVoices.count { it.isChinese }
                appendLine("英语语音: ${englishCount}个")
                appendLine("中文语音: ${chineseCount}个")
            }
            
            appendLine()
            append(OnlineTTSHelper.getAPIStatusSummary())
        }
    }
    
    /**
     * 设置首选语音
     */
    fun setPreferredVoice(voice: OnlineTTSHelper.TTSVoice) {
        preferredVoice = voice
        Log.d(TAG, "Preferred voice set to: ${voice.displayName}")
    }
    
    /**
     * 获取可用语音列表
     */
    fun getAvailableVoices(): List<OnlineTTSHelper.TTSVoice> {
        return availableVoices.toList()
    }
    
    /**
     * 获取英语语音列表
     */
    fun getEnglishVoices(): List<OnlineTTSHelper.TTSVoice> {
        return availableVoices.filter { it.isEnglish }
    }
    
    /**
     * 获取中文语音列表
     */
    fun getChineseVoices(): List<OnlineTTSHelper.TTSVoice> {
        return availableVoices.filter { it.isChinese }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isReady(): Boolean = isInitialized && preferredVoice != null
    
    /**
     * 重新初始化
     */
    fun reinitialize(callback: (Boolean, String) -> Unit) {
        Log.d(TAG, "Reinitializing...")
        isInitialized = false
        availableVoices = emptyList()
        preferredVoice = null
        
        // 清除缓存
        OnlineTTSHelper.clearCache()
        
        initialize(callback)
    }
    
    /**
     * 释放资源
     */
    fun release() {
        Log.d(TAG, "Releasing resources...")
        isInitialized = false
        availableVoices = emptyList()
        preferredVoice = null
    }
}
