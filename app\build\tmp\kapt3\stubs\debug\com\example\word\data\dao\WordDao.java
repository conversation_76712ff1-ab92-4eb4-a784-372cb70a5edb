package com.example.word.data.dao;

/**
 * 单词数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\t\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u000e\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u000e\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b2\u0006\u0010\u0012\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u000e\u0010\u0014\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u000e\u0010\u0019\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u001b\u001a\u00020\u0010H\'J$\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u001d\u001a\u00020\u00102\u0006\u0010\u001e\u001a\u00020\u0010H\'J\u001c\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010 \u001a\u00020!H\'J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010#\u001a\u00020\u0017H\'J\u0016\u0010$\u001a\u00020\u00172\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010%\u001a\u00020\u00032\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00a7@\u00a2\u0006\u0002\u0010\'J\u001c\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010)\u001a\u00020!H\'J\u001e\u0010*\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010+\u001a\u00020,H\u00a7@\u00a2\u0006\u0002\u0010-J.\u0010.\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010/\u001a\u00020\u00172\u0006\u00100\u001a\u00020\u00102\u0006\u00101\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u00102J&\u00103\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u00104\u001a\u00020\u00172\u0006\u00105\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u00106J\u0016\u00107\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u00068"}, d2 = {"Lcom/example/word/data/dao/WordDao;", "", "deleteAllWords", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWord", "word", "Lcom/example/word/data/entities/Word;", "(Lcom/example/word/data/entities/Word;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllWords", "Landroidx/lifecycle/LiveData;", "", "getAverageAccuracy", "", "getBookmarkedWords", "getBookmarkedWordsCount", "", "getRandomWords", "count", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStudiedWordsCount", "getWordById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWordCount", "getWordsByDifficulty", "level", "getWordsByFrequencyRange", "startRank", "endRank", "getWordsByPartOfSpeech", "pos", "", "getWordsForReview", "currentTime", "insertWord", "insertWords", "words", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchWords", "query", "updateBookmarkStatus", "isBookmarked", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateReviewInfo", "nextTime", "interval", "strength", "(JJIFLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateStudyStats", "time", "accuracy", "(JJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWord", "app_debug"})
@androidx.room.Dao()
public abstract interface WordDao {
    
    @androidx.room.Query(value = "SELECT * FROM words ORDER BY frequencyRank ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getAllWords();
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWordById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.word.data.entities.Word> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE word LIKE \'%\' || :query || \'%\' OR translation LIKE \'%\' || :query || \'%\' ORDER BY frequencyRank ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> searchWords(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE difficultyLevel = :level ORDER BY frequencyRank ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getWordsByDifficulty(int level);
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE partOfSpeech = :pos ORDER BY frequencyRank ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getWordsByPartOfSpeech(@org.jetbrains.annotations.NotNull()
    java.lang.String pos);
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getBookmarkedWords();
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE nextReviewTime <= :currentTime AND studyCount > 0 ORDER BY nextReviewTime ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getWordsForReview(long currentTime);
    
    @androidx.room.Query(value = "SELECT * FROM words ORDER BY RANDOM() LIMIT :count")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRandomWords(int count, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.word.data.entities.Word>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM words WHERE frequencyRank BETWEEN :startRank AND :endRank ORDER BY frequencyRank ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Word>> getWordsByFrequencyRange(int startRank, int endRank);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM words WHERE studyCount > 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStudiedWordsCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM words WHERE isBookmarked = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBookmarkedWordsCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(accuracyRate) FROM words WHERE studyCount > 0")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageAccuracy(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM words")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWordCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWord(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Word word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertWords(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.word.data.entities.Word> words, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWord(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Word word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE words SET isBookmarked = :isBookmarked WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBookmarkStatus(long id, boolean isBookmarked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE words SET studyCount = studyCount + 1, lastStudiedTime = :time, accuracyRate = :accuracy WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateStudyStats(long id, long time, int accuracy, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE words SET nextReviewTime = :nextTime, reviewInterval = :interval, memoryStrength = :strength WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateReviewInfo(long id, long nextTime, int interval, float strength, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWord(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Word word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM words")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllWords(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}