package com.example.word.utils;

/**
 * 崩溃预防工具类
 * 提供安全的操作方法，避免常见的崩溃问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002JZ\u0010\u0005\u001a\u0004\u0018\u0001H\u0006\"\u0004\b\u0000\u0010\u00062\u001c\u0010\u0007\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00060\t\u0012\u0006\u0012\u0004\u0018\u00010\u00010\b2\u0018\b\u0002\u0010\n\u001a\u0012\u0012\b\u0012\u00060\u000bj\u0002`\f\u0012\u0004\u0012\u00020\r0\b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u0001H\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJG\u0010\u0010\u001a\u0004\u0018\u0001H\u0006\"\u0004\b\u0000\u0010\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u0002H\u00060\u00112\u0018\b\u0002\u0010\n\u001a\u0012\u0012\b\u0012\u00060\u000bj\u0002`\f\u0012\u0004\u0012\u00020\r0\b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u0001H\u0006\u00a2\u0006\u0002\u0010\u0012JZ\u0010\u0013\u001a\u0004\u0018\u0001H\u0006\"\u0004\b\u0000\u0010\u00062\u001c\u0010\u0007\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00060\t\u0012\u0006\u0012\u0004\u0018\u00010\u00010\b2\u0018\b\u0002\u0010\n\u001a\u0012\u0012\b\u0012\u00060\u000bj\u0002`\f\u0012\u0004\u0012\u00020\r0\b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u0001H\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ!\u0010\u0014\u001a\u0004\u0018\u0001H\u0006\"\u0004\b\u0000\u0010\u0006*\u0004\u0018\u0001H\u00062\u0006\u0010\u0015\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0016J\n\u0010\u0017\u001a\u00020\u0018*\u00020\u0019J\u001e\u0010\u001a\u001a\u0004\u0018\u0001H\u0006\"\u0006\b\u0000\u0010\u0006\u0018\u0001*\u0004\u0018\u00010\u0001H\u0086\b\u00a2\u0006\u0002\u0010\u001bJ\f\u0010\u001c\u001a\u0004\u0018\u00010\u001d*\u00020\u0019J\f\u0010\u001e\u001a\u0004\u0018\u00010\u001f*\u00020\u0019J,\u0010 \u001a\u00020\u0018*\u00020\u00192\u0006\u0010!\u001a\u00020\"2\u0018\b\u0002\u0010\n\u001a\u0012\u0012\b\u0012\u00060\u000bj\u0002`\f\u0012\u0004\u0012\u00020\r0\bJ\u0018\u0010#\u001a\u00020\r*\u00020\u00192\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\r0\u0011JE\u0010$\u001a\u0004\u0018\u0001H\u0006\"\u0004\b\u0000\u0010\u0006*\u00020\u00192\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u0002H\u00060\b2\u0018\b\u0002\u0010\n\u001a\u0012\u0012\b\u0012\u00060\u000bj\u0002`\f\u0012\u0004\u0012\u00020\r0\b\u00a2\u0006\u0002\u0010%R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/example/word/utils/CrashPrevention;", "", "()V", "TAG", "", "safeDatabaseOperation", "T", "operation", "Lkotlin/Function1;", "Lkotlin/coroutines/Continuation;", "onError", "Ljava/lang/Exception;", "Lkotlin/Exception;", "", "defaultValue", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "safeExecute", "Lkotlin/Function0;", "(Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;)Ljava/lang/Object;", "safeNetworkOperation", "checkNotNull", "name", "(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;", "isSafeState", "", "Landroidx/fragment/app/Fragment;", "safeCast", "(Ljava/lang/Object;)Ljava/lang/Object;", "safeContext", "Landroid/content/Context;", "safeNavController", "Landroidx/navigation/NavController;", "safeNavigate", "actionId", "", "safeUIOperation", "withSafeContext", "(Landroidx/fragment/app/Fragment;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "app_debug"})
public final class CrashPrevention {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CrashPrevention";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.CrashPrevention INSTANCE = null;
    
    private CrashPrevention() {
        super();
    }
    
    /**
     * 安全地执行可能抛出异常的操作
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>T safeExecute(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<? extends T> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError, @org.jetbrains.annotations.Nullable()
    T defaultValue) {
        return null;
    }
    
    /**
     * 安全地执行导航操作
     */
    public final boolean safeNavigate(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$safeNavigate, int actionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError) {
        return false;
    }
    
    /**
     * 安全地获取NavController
     */
    @org.jetbrains.annotations.Nullable()
    public final androidx.navigation.NavController safeNavController(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$safeNavController) {
        return null;
    }
    
    /**
     * 安全地执行UI操作
     */
    public final void safeUIOperation(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$safeUIOperation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> operation) {
    }
    
    /**
     * 安全地获取Context
     */
    @org.jetbrains.annotations.Nullable()
    public final android.content.Context safeContext(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$safeContext) {
        return null;
    }
    
    /**
     * 检查Fragment是否处于安全状态
     */
    public final boolean isSafeState(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$isSafeState) {
        return false;
    }
    
    /**
     * 安全地执行需要Context的操作
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>T withSafeContext(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$withSafeContext, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.content.Context, ? extends T> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError) {
        return null;
    }
    
    /**
     * 安全地执行数据库操作
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>java.lang.Object safeDatabaseOperation(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super T>, ? extends java.lang.Object> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError, @org.jetbrains.annotations.Nullable()
    T defaultValue, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super T> $completion) {
        return null;
    }
    
    /**
     * 安全地执行网络操作
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>java.lang.Object safeNetworkOperation(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super T>, ? extends java.lang.Object> operation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError, @org.jetbrains.annotations.Nullable()
    T defaultValue, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super T> $completion) {
        return null;
    }
    
    /**
     * 检查对象是否为null并记录警告
     */
    @org.jetbrains.annotations.Nullable()
    public final <T extends java.lang.Object>T checkNotNull(@org.jetbrains.annotations.Nullable()
    T $this$checkNotNull, @org.jetbrains.annotations.NotNull()
    java.lang.String name) {
        return null;
    }
}