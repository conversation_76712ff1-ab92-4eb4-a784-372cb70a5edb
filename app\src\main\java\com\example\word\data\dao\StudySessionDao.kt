package com.example.word.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.word.data.entities.StudySession
import com.example.word.data.entities.UserProgress

/**
 * 学习记录数据访问对象
 */
@Dao
interface StudySessionDao {
    
    // 插入学习记录
    @Insert
    suspend fun insertStudySession(session: StudySession)
    
    // 获取所有学习记录
    @Query("SELECT * FROM study_sessions ORDER BY studyDate DESC")
    fun getAllStudySessions(): LiveData<List<StudySession>>
    
    // 获取指定日期的学习记录
    @Query("SELECT * FROM study_sessions WHERE studyDate BETWEEN :startDate AND :endDate ORDER BY studyDate DESC")
    fun getStudySessionsByDateRange(startDate: Long, endDate: Long): LiveData<List<StudySession>>
    
    // 获取指定类型的学习记录
    @Query("SELECT * FROM study_sessions WHERE studyType = :type ORDER BY studyDate DESC")
    fun getStudySessionsByType(type: String): LiveData<List<StudySession>>
    
    // 获取学习统计
    @Query("SELECT COUNT(*) FROM study_sessions WHERE studyDate BETWEEN :startDate AND :endDate")
    suspend fun getStudyCountByDateRange(startDate: Long, endDate: Long): Int
    
    @Query("SELECT COUNT(*) FROM study_sessions WHERE result = 'correct' AND studyDate BETWEEN :startDate AND :endDate")
    suspend fun getCorrectAnswersCount(startDate: Long, endDate: Long): Int
    
    @Query("SELECT AVG(responseTime) FROM study_sessions WHERE studyDate BETWEEN :startDate AND :endDate")
    suspend fun getAverageResponseTime(startDate: Long, endDate: Long): Float
    
    // 删除旧的学习记录（保留最近30天）
    @Query("DELETE FROM study_sessions WHERE studyDate < :cutoffDate")
    suspend fun deleteOldStudySessions(cutoffDate: Long)
}


