/ Header Record For PersistentHashMapValueStorage3 2app/src/main/java/com/example/word/MainActivity.kt@ ?app/src/main/java/com/example/word/data/dao/EssayTemplateDao.kt9 8app/src/main/java/com/example/word/data/dao/PhraseDao.kt? >app/src/main/java/com/example/word/data/dao/StudySessionDao.kt7 6app/src/main/java/com/example/word/data/dao/WordDao.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktB Aapp/src/main/java/com/example/word/data/entities/EssayTemplate.kt; :app/src/main/java/com/example/word/data/entities/Phrase.ktA @app/src/main/java/com/example/word/data/entities/StudySession.kt9 8app/src/main/java/com/example/word/data/entities/Word.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.kt= <app/src/main/java/com/example/word/ui/essay/EssayFragment.kt? >app/src/main/java/com/example/word/ui/phrases/PhraseAdapter.ktA @app/src/main/java/com/example/word/ui/phrases/PhrasesFragment.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.kt; :app/src/main/java/com/example/word/ui/quiz/QuizFragment.ktC Bapp/src/main/java/com/example/word/ui/viewmodel/PhraseViewModel.ktA @app/src/main/java/com/example/word/ui/viewmodel/WordViewModel.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt@ ?app/src/main/java/com/example/word/ui/vocabulary/WordAdapter.ktK Japp/src/main/java/com/example/word/data/database/VocabularyDataProvider.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.kt= <app/src/main/java/com/example/word/ui/essay/EssayFragment.ktD Capp/src/main/java/com/example/word/ui/essay/EssayTemplateAdapter.kt> =app/src/main/java/com/example/word/ui/essay/EssayViewModel.ktE Dapp/src/main/java/com/example/word/ui/progress/AchievementAdapter.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktD Capp/src/main/java/com/example/word/ui/progress/ProgressViewModel.kt; :app/src/main/java/com/example/word/ui/quiz/QuizFragment.kt< ;app/src/main/java/com/example/word/ui/quiz/QuizViewModel.ktA @app/src/main/java/com/example/word/ui/viewmodel/WordViewModel.kt; :app/src/main/java/com/example/word/ui/quiz/QuizFragment.kt; :app/src/main/java/com/example/word/ui/quiz/QuizFragment.ktK Japp/src/main/java/com/example/word/data/database/VocabularyDataProvider.kt@ ?app/src/main/java/com/example/word/data/entities/Achievement.kt9 8app/src/main/java/com/example/word/data/entities/Word.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktC Bapp/src/main/java/com/example/word/ui/settings/SettingsFragment.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt@ ?app/src/main/java/com/example/word/ui/vocabulary/WordAdapter.kt8 7app/src/main/java/com/example/word/utils/ChartHelper.ktA @app/src/main/java/com/example/word/utils/GameificationManager.kt@ ?app/src/main/java/com/example/word/utils/NotificationManager.ktF Eapp/src/main/java/com/example/word/utils/SpacedRepetitionAlgorithm.kt7 6app/src/main/java/com/example/word/data/dao/WordDao.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.kt< ;app/src/main/java/com/example/word/ui/quiz/QuizViewModel.ktA @app/src/main/java/com/example/word/ui/viewmodel/WordViewModel.kt3 2app/src/main/java/com/example/word/MainActivity.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktK Japp/src/main/java/com/example/word/data/database/VocabularyDataProvider.ktI Happ/src/main/java/com/example/word/data/database/VocabularyJsonLoader.kt8 7app/src/main/java/com/example/word/utils/ChartHelper.ktA @app/src/main/java/com/example/word/utils/GameificationManager.ktF Eapp/src/main/java/com/example/word/utils/SpacedRepetitionAlgorithm.ktF Eapp/src/main/java/com/example/word/utils/SpacedRepetitionAlgorithm.ktC Bapp/src/main/java/com/example/word/ui/settings/SettingsFragment.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt< ;app/src/main/java/com/example/word/utils/OnlineTTSHelper.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt< ;app/src/main/java/com/example/word/utils/OnlineTTSHelper.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt< ;app/src/main/java/com/example/word/utils/OnlineTTSHelper.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt? >app/src/main/java/com/example/word/data/dao/StudySessionDao.kt? >app/src/main/java/com/example/word/data/dao/UserProgressDao.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktA @app/src/main/java/com/example/word/data/entities/StudySession.ktA @app/src/main/java/com/example/word/data/entities/UserProgress.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.kt; :app/src/main/java/com/example/word/ui/home/<USER>/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktD Capp/src/main/java/com/example/word/ui/progress/ProgressViewModel.kt; :app/src/main/java/com/example/word/ui/home/<USER>/src/main/java/com/example/word/data/dao/StudySessionDao.kt? >app/src/main/java/com/example/word/data/dao/UserProgressDao.ktA @app/src/main/java/com/example/word/data/entities/StudySession.ktE Dapp/src/main/java/com/example/word/ui/progress/AchievementAdapter.ktD Capp/src/main/java/com/example/word/ui/progress/ProgressViewModel.ktA @app/src/main/java/com/example/word/utils/GameificationManager.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.kt; :app/src/main/java/com/example/word/ui/home/<USER>/src/main/java/com/example/word/ui/progress/ProgressFragment.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.kt? >app/src/main/java/com/example/word/data/dao/UserProgressDao.ktE Dapp/src/main/java/com/example/word/ui/progress/AchievementAdapter.ktD Capp/src/main/java/com/example/word/ui/progress/ProgressViewModel.ktA @app/src/main/java/com/example/word/utils/GameificationManager.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktA @app/src/main/java/com/example/word/utils/GameificationManager.ktA @app/src/main/java/com/example/word/utils/GameificationManager.kt