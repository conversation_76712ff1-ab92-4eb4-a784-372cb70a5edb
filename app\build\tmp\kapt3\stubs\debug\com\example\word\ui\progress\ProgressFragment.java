package com.example.word.ui.progress;

/**
 * 学习进度Fragment
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u000e\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00160\u0015H\u0002J$\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010\u001f\u001a\u00020 H\u0016J\u001a\u0010!\u001a\u00020 2\u0006\u0010\"\u001a\u00020\u00182\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010#\u001a\u00020 H\u0002J\b\u0010$\u001a\u00020 H\u0002J\b\u0010%\u001a\u00020 H\u0002J\u001c\u0010&\u001a\u00020 2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020)\u0012\u0004\u0012\u00020*0(H\u0002J\u0010\u0010+\u001a\u00020 2\u0006\u0010,\u001a\u00020-H\u0002J\u0012\u0010.\u001a\u00020 2\b\u0010/\u001a\u0004\u0018\u000100H\u0002J\u0010\u00101\u001a\u00020 2\u0006\u00102\u001a\u000203H\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u001b\u0010\n\u001a\u00020\u000b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\f\u0010\r\u00a8\u00064"}, d2 = {"Lcom/example/word/ui/progress/ProgressFragment;", "Landroidx/fragment/app/Fragment;", "()V", "_binding", "Lcom/example/word/databinding/FragmentProgressBinding;", "achievementAdapter", "Lcom/example/word/ui/progress/AchievementAdapter;", "binding", "getBinding", "()Lcom/example/word/databinding/FragmentProgressBinding;", "progressViewModel", "Lcom/example/word/ui/progress/ProgressViewModel;", "getProgressViewModel", "()Lcom/example/word/ui/progress/ProgressViewModel;", "progressViewModel$delegate", "Lkotlin/Lazy;", "formatStudyTime", "", "timeMinutes", "", "generateStudyProgressData", "", "Lcom/example/word/utils/StudyDataPoint;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "", "onViewCreated", "view", "setupCharts", "setupObservers", "setupRecyclerView", "updateDifficultyStats", "difficultyStats", "", "", "Lcom/example/word/ui/progress/DifficultyStats;", "updateStudyStatistics", "statistics", "Lcom/example/word/ui/progress/StudyStatistics;", "updateUserProgress", "progress", "Lcom/example/word/data/entities/UserProgress;", "updateVocabularyMastery", "mastery", "Lcom/example/word/ui/progress/VocabularyMastery;", "app_debug"})
public final class ProgressFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.Nullable()
    private com.example.word.databinding.FragmentProgressBinding _binding;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy progressViewModel$delegate = null;
    private com.example.word.ui.progress.AchievementAdapter achievementAdapter;
    
    public ProgressFragment() {
        super();
    }
    
    private final com.example.word.databinding.FragmentProgressBinding getBinding() {
        return null;
    }
    
    private final com.example.word.ui.progress.ProgressViewModel getProgressViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置观察者
     */
    private final void setupObservers() {
    }
    
    /**
     * 更新用户进度显示
     */
    private final void updateUserProgress(com.example.word.data.entities.UserProgress progress) {
    }
    
    /**
     * 更新学习统计显示
     */
    private final void updateStudyStatistics(com.example.word.ui.progress.StudyStatistics statistics) {
    }
    
    /**
     * 更新词汇掌握情况显示
     */
    private final void updateVocabularyMastery(com.example.word.ui.progress.VocabularyMastery mastery) {
    }
    
    /**
     * 更新难度等级统计
     */
    private final void updateDifficultyStats(java.util.Map<java.lang.Integer, com.example.word.ui.progress.DifficultyStats> difficultyStats) {
    }
    
    /**
     * 设置图表
     */
    private final void setupCharts() {
    }
    
    /**
     * 生成学习进度数据
     */
    private final java.util.List<com.example.word.utils.StudyDataPoint> generateStudyProgressData() {
        return null;
    }
    
    /**
     * 格式化学习时间
     */
    private final java.lang.String formatStudyTime(long timeMinutes) {
        return null;
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
}