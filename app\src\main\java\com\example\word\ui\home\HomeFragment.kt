package com.example.word.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.example.word.R
import com.example.word.databinding.FragmentHomeBinding
import com.example.word.ui.viewmodel.WordViewModel
import com.example.word.utils.GameificationManager
import kotlinx.coroutines.launch

/**
 * 首页Fragment
 * 显示学习概览、今日目标、快速开始等功能
 */
class HomeFragment : Fragment() {
    
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private val wordViewModel: WordViewModel by viewModels()
    private lateinit var gamificationManager: GameificationManager
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        gamificationManager = GameificationManager
        
        setupUI()
        setupObservers()
        setupClickListeners()
        loadData()
    }
    
    private fun setupUI() {
        // 设置欢迎信息
        binding.textViewWelcome.text = getWelcomeMessage()
        
        // 设置今日日期
        binding.textViewDate.text = getCurrentDateString()
    }
    
    private fun setupObservers() {
        // 观察用户进度
        wordViewModel.allWords.observe(viewLifecycleOwner) { words ->
            updateWordStats(words.size)
        }
        
        // 观察需要复习的单词
        wordViewModel.wordsForReview.observe(viewLifecycleOwner) { reviewWords ->
            updateReviewStats(reviewWords.size)
        }
    }
    
    private fun setupClickListeners() {
        // 开始学习按钮
        binding.buttonStartStudy.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_vocabulary)
        }
        
        // 开始复习按钮
        binding.buttonStartReview.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_vocabulary)
        }
        
        // 开始测验按钮
        binding.buttonStartQuiz.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_quiz)
        }
        
        // 查看进度按钮
        binding.buttonViewProgress.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_progress)
        }
        
        // 短语学习按钮
        binding.buttonPhrases.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_phrases)
        }
        
        // 作文模板按钮
        binding.buttonEssays.setOnClickListener {
            findNavController().navigate(R.id.action_home_to_essays)
        }
    }
    
    private fun loadData() {
        lifecycleScope.launch {
            try {
                // 加载用户等级信息
                val userLevel = gamificationManager.calculateUserLevel(0) // 默认经验值为0
                updateLevelInfo(userLevel)
                
                // 加载今日学习目标
                updateDailyGoal()
                
            } catch (e: Exception) {
                // 处理错误
                e.printStackTrace()
            }
        }
    }
    
    private fun updateWordStats(totalWords: Int) {
        binding.textViewTotalWords.text = totalWords.toString()
    }
    
    private fun updateReviewStats(reviewCount: Int) {
        binding.textViewReviewCount.text = reviewCount.toString()
        
        // 更新复习按钮状态
        if (reviewCount > 0) {
            binding.buttonStartReview.isEnabled = true
            binding.buttonStartReview.text = "复习 ($reviewCount)"
        } else {
            binding.buttonStartReview.isEnabled = false
            binding.buttonStartReview.text = "暂无复习"
        }
    }
    
    private fun updateLevelInfo(userLevel: GameificationManager.UserLevel) {
        binding.apply {
            textViewLevel.text = "Lv.${userLevel.level}"
            textViewLevelName.text = userLevel.title
            textViewExperience.text = "${userLevel.currentExperience}/${userLevel.experienceRequired}"

            // 更新经验进度条
            progressBarExperience.max = userLevel.experienceRequired
            progressBarExperience.progress = userLevel.currentExperience
        }
    }
    
    private fun updateDailyGoal() {
        // 这里可以从SharedPreferences或数据库获取每日目标
        val dailyGoal = 20 // 默认每日学习20个单词
        val todayProgress = 0 // 今日已学习的单词数
        
        binding.apply {
            textViewDailyGoal.text = "$todayProgress / $dailyGoal"
            progressBarDaily.max = dailyGoal
            progressBarDaily.progress = todayProgress
        }
    }
    
    private fun getWelcomeMessage(): String {
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        return when (hour) {
            in 5..11 -> "早上好！"
            in 12..17 -> "下午好！"
            in 18..22 -> "晚上好！"
            else -> "夜深了，注意休息！"
        }
    }
    
    private fun getCurrentDateString(): String {
        val calendar = java.util.Calendar.getInstance()
        val year = calendar.get(java.util.Calendar.YEAR)
        val month = calendar.get(java.util.Calendar.MONTH) + 1
        val day = calendar.get(java.util.Calendar.DAY_OF_MONTH)
        
        val weekDays = arrayOf("周日", "周一", "周二", "周三", "周四", "周五", "周六")
        val weekDay = weekDays[calendar.get(java.util.Calendar.DAY_OF_WEEK) - 1]
        
        return "${year}年${month}月${day}日 $weekDay"
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
