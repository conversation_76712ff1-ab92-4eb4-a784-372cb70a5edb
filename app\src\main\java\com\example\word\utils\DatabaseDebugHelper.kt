package com.example.word.utils

import android.content.Context
import android.util.Log
import com.example.word.data.database.DatabaseInitializer
import com.example.word.data.database.WordDatabase
import com.example.word.data.parser.TextDataParser
import com.example.word.data.organizer.DataOrganizer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 数据库调试助手
 * 用于诊断数据加载问题
 */
object DatabaseDebugHelper {
    
    private const val TAG = "DatabaseDebugHelper"
    
    /**
     * 检查数据库状态
     */
    suspend fun checkDatabaseStatus(context: Context): DatabaseStatus {
        return withContext(Dispatchers.IO) {
            try {
                val database = WordDatabase.getDatabase(context)
                
                val wordCount = database.wordDao().getWordCount()
                val phraseCount = database.phraseDao().getPhraseCount()
                val templateCount = database.essayTemplateDao().getTemplateCount()
                
                val isInitialized = DatabaseInitializer.isDatabaseInitialized(context)
                val currentVersion = DatabaseInitializer.getCurrentDataVersion(context)
                
                Log.d(TAG, "Database Status:")
                Log.d(TAG, "  Words: $wordCount")
                Log.d(TAG, "  Phrases: $phraseCount")
                Log.d(TAG, "  Templates: $templateCount")
                Log.d(TAG, "  Initialized: $isInitialized")
                Log.d(TAG, "  Version: $currentVersion")
                
                DatabaseStatus(
                    wordCount = wordCount,
                    phraseCount = phraseCount,
                    templateCount = templateCount,
                    isInitialized = isInitialized,
                    currentVersion = currentVersion
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error checking database status", e)
                DatabaseStatus(
                    wordCount = -1,
                    phraseCount = -1,
                    templateCount = -1,
                    isInitialized = false,
                    currentVersion = "error",
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 测试文本解析功能
     */
    suspend fun testTextParsing(context: Context): TextParsingResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Testing data organization...")

                val organizedData = DataOrganizer.organizeAllData(context)
                val words = organizedData.words
                val phrases = organizedData.phrases
                val templates = organizedData.essayTemplates

                Log.d(TAG, "Data organization results:")
                Log.d(TAG, "  Words organized: ${words.size}")
                Log.d(TAG, "  Phrases organized: ${phrases.size}")
                Log.d(TAG, "  Templates organized: ${templates.size}")

                // 显示前几个解析结果作为示例
                if (words.isNotEmpty()) {
                    Log.d(TAG, "  First word: ${words[0].word} - ${words[0].translation}")
                }
                if (phrases.isNotEmpty()) {
                    Log.d(TAG, "  First phrase: ${phrases[0].phrase} - ${phrases[0].translation}")
                }
                if (templates.isNotEmpty()) {
                    Log.d(TAG, "  First template: ${templates[0].content}")
                }

                TextParsingResult(
                    wordCount = words.size,
                    phraseCount = phrases.size,
                    templateCount = templates.size,
                    success = true
                )

            } catch (e: Exception) {
                Log.e(TAG, "Error testing data organization", e)
                TextParsingResult(
                    wordCount = 0,
                    phraseCount = 0,
                    templateCount = 0,
                    success = false,
                    error = e.message
                )
            }
        }
    }
    
    /**
     * 强制重新初始化数据库
     */
    suspend fun forceReinitializeDatabase(context: Context): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Force reinitializing database...")

                // 使用DatabaseInitializer的强制重新加载方法
                DatabaseInitializer.forceReloadData(context)

                Log.d(TAG, "Database reinitialization completed")
                true

            } catch (e: Exception) {
                Log.e(TAG, "Error reinitializing database", e)
                false
            }
        }
    }
    
    /**
     * 检查1.txt文件是否存在
     */
    fun check1TxtFile(context: Context): Boolean {
        return try {
            val inputStream = context.assets.open("1.txt")
            val size = inputStream.available()
            inputStream.close()
            
            Log.d(TAG, "1.txt file found, size: $size bytes")
            size > 0
            
        } catch (e: Exception) {
            Log.e(TAG, "1.txt file not found or error reading", e)
            false
        }
    }
    
    /**
     * 数据库状态数据类
     */
    data class DatabaseStatus(
        val wordCount: Int,
        val phraseCount: Int,
        val templateCount: Int,
        val isInitialized: Boolean,
        val currentVersion: String,
        val error: String? = null
    )
    
    /**
     * 文本解析结果数据类
     */
    data class TextParsingResult(
        val wordCount: Int,
        val phraseCount: Int,
        val templateCount: Int,
        val success: Boolean,
        val error: String? = null
    )
}
