package com.example.word.data.entities;

/**
 * 用户进度实体类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\f\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\bN\b\u0087\b\u0018\u00002\u00020\u0001B\u009d\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\r\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0018\u0012\b\b\u0002\u0010 \u001a\u00020\u0018\u0012\b\b\u0002\u0010!\u001a\u00020\u0003\u0012\b\b\u0002\u0010\"\u001a\u00020\u0003\u00a2\u0006\u0002\u0010#J\t\u0010E\u001a\u00020\u0003H\u00c6\u0003J\t\u0010F\u001a\u00020\u0005H\u00c6\u0003J\t\u0010G\u001a\u00020\u0005H\u00c6\u0003J\t\u0010H\u001a\u00020\u0005H\u00c6\u0003J\t\u0010I\u001a\u00020\u0005H\u00c6\u0003J\t\u0010J\u001a\u00020\u0012H\u00c6\u0003J\t\u0010K\u001a\u00020\u0014H\u00c6\u0003J\t\u0010L\u001a\u00020\u0005H\u00c6\u0003J\t\u0010M\u001a\u00020\u0005H\u00c6\u0003J\t\u0010N\u001a\u00020\u0018H\u00c6\u0003J\t\u0010O\u001a\u00020\u0014H\u00c6\u0003J\t\u0010P\u001a\u00020\u0005H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0018H\u00c6\u0003J\t\u0010R\u001a\u00020\u0018H\u00c6\u0003J\t\u0010S\u001a\u00020\u0014H\u00c6\u0003J\t\u0010T\u001a\u00020\u0018H\u00c6\u0003J\t\u0010U\u001a\u00020\u0018H\u00c6\u0003J\t\u0010V\u001a\u00020\u0018H\u00c6\u0003J\t\u0010W\u001a\u00020\u0018H\u00c6\u0003J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0005H\u00c6\u0003J\t\u0010[\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0005H\u00c6\u0003J\t\u0010]\u001a\u00020\u0005H\u00c6\u0003J\t\u0010^\u001a\u00020\u0003H\u00c6\u0003J\t\u0010_\u001a\u00020\u0005H\u00c6\u0003J\t\u0010`\u001a\u00020\u0005H\u00c6\u0003J\u00a1\u0002\u0010a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\u00052\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00052\b\b\u0002\u0010\u0016\u001a\u00020\u00052\b\b\u0002\u0010\u0017\u001a\u00020\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u00142\b\b\u0002\u0010\u001a\u001a\u00020\u00182\b\b\u0002\u0010\u001b\u001a\u00020\u00182\b\b\u0002\u0010\u001c\u001a\u00020\u00142\b\b\u0002\u0010\u001d\u001a\u00020\u00182\b\b\u0002\u0010\u001e\u001a\u00020\u00182\b\b\u0002\u0010\u001f\u001a\u00020\u00182\b\b\u0002\u0010 \u001a\u00020\u00182\b\b\u0002\u0010!\u001a\u00020\u00032\b\b\u0002\u0010\"\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010b\u001a\u00020\u00182\b\u0010c\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010d\u001a\u00020\u0005H\u00d6\u0001J\t\u0010e\u001a\u00020\u0014H\u00d6\u0001R\u0011\u0010 \u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u001a\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010%R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010*R\u0011\u0010!\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010*R\u0011\u0010\u0016\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010*R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010*R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010-R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010-R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010%R\u0011\u0010\u0019\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u0011\u0010\u001d\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010%R\u0011\u0010\u001e\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010%R\u0011\u0010\u001f\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010%R\u0011\u0010\u001b\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010%R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010*R\u0011\u0010\u001c\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00105R\u0011\u0010\u0015\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010*R\u0011\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010*R\u0011\u0010\r\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010*R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010*R\u0011\u0010\u000e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010*R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010-R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010*R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u00105R\u0011\u0010\"\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010-\u00a8\u0006f"}, d2 = {"Lcom/example/word/data/entities/UserProgress;", "", "id", "", "totalWordsLearned", "", "totalPhrasesLearned", "totalStudyTime", "studyDays", "consecutiveDays", "lastStudyDate", "currentLevel", "experiencePoints", "totalExperience", "totalQuizzes", "correctAnswers", "totalAnswers", "averageAccuracy", "", "unlockedAchievements", "", "totalAchievements", "dailyGoal", "reminderEnabled", "", "reminderTime", "autoPlayPronunciation", "slowPronunciation", "themeMode", "showDifficulty", "showFrequency", "showStreak", "autoBackup", "createdAt", "updatedAt", "(JIIJIIJIIIIIIFLjava/lang/String;IIZLjava/lang/String;ZZLjava/lang/String;ZZZZJJ)V", "getAutoBackup", "()Z", "getAutoPlayPronunciation", "getAverageAccuracy", "()F", "getConsecutiveDays", "()I", "getCorrectAnswers", "getCreatedAt", "()J", "getCurrentLevel", "getDailyGoal", "getExperiencePoints", "getId", "getLastStudyDate", "getReminderEnabled", "getReminderTime", "()Ljava/lang/String;", "getShowDifficulty", "getShowFrequency", "getShowStreak", "getSlowPronunciation", "getStudyDays", "getThemeMode", "getTotalAchievements", "getTotalAnswers", "getTotalExperience", "getTotalPhrasesLearned", "getTotalQuizzes", "getTotalStudyTime", "getTotalWordsLearned", "getUnlockedAchievements", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "user_progress")
public final class UserProgress {
    @androidx.room.PrimaryKey()
    private final long id = 0L;
    private final int totalWordsLearned = 0;
    private final int totalPhrasesLearned = 0;
    private final long totalStudyTime = 0L;
    private final int studyDays = 0;
    private final int consecutiveDays = 0;
    private final long lastStudyDate = 0L;
    private final int currentLevel = 0;
    private final int experiencePoints = 0;
    private final int totalExperience = 0;
    private final int totalQuizzes = 0;
    private final int correctAnswers = 0;
    private final int totalAnswers = 0;
    private final float averageAccuracy = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String unlockedAchievements = null;
    private final int totalAchievements = 0;
    private final int dailyGoal = 0;
    private final boolean reminderEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String reminderTime = null;
    private final boolean autoPlayPronunciation = false;
    private final boolean slowPronunciation = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String themeMode = null;
    private final boolean showDifficulty = false;
    private final boolean showFrequency = false;
    private final boolean showStreak = false;
    private final boolean autoBackup = false;
    private final long createdAt = 0L;
    private final long updatedAt = 0L;
    
    public UserProgress(long id, int totalWordsLearned, int totalPhrasesLearned, long totalStudyTime, int studyDays, int consecutiveDays, long lastStudyDate, int currentLevel, int experiencePoints, int totalExperience, int totalQuizzes, int correctAnswers, int totalAnswers, float averageAccuracy, @org.jetbrains.annotations.NotNull()
    java.lang.String unlockedAchievements, int totalAchievements, int dailyGoal, boolean reminderEnabled, @org.jetbrains.annotations.NotNull()
    java.lang.String reminderTime, boolean autoPlayPronunciation, boolean slowPronunciation, @org.jetbrains.annotations.NotNull()
    java.lang.String themeMode, boolean showDifficulty, boolean showFrequency, boolean showStreak, boolean autoBackup, long createdAt, long updatedAt) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final int getTotalWordsLearned() {
        return 0;
    }
    
    public final int getTotalPhrasesLearned() {
        return 0;
    }
    
    public final long getTotalStudyTime() {
        return 0L;
    }
    
    public final int getStudyDays() {
        return 0;
    }
    
    public final int getConsecutiveDays() {
        return 0;
    }
    
    public final long getLastStudyDate() {
        return 0L;
    }
    
    public final int getCurrentLevel() {
        return 0;
    }
    
    public final int getExperiencePoints() {
        return 0;
    }
    
    public final int getTotalExperience() {
        return 0;
    }
    
    public final int getTotalQuizzes() {
        return 0;
    }
    
    public final int getCorrectAnswers() {
        return 0;
    }
    
    public final int getTotalAnswers() {
        return 0;
    }
    
    public final float getAverageAccuracy() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUnlockedAchievements() {
        return null;
    }
    
    public final int getTotalAchievements() {
        return 0;
    }
    
    public final int getDailyGoal() {
        return 0;
    }
    
    public final boolean getReminderEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getReminderTime() {
        return null;
    }
    
    public final boolean getAutoPlayPronunciation() {
        return false;
    }
    
    public final boolean getSlowPronunciation() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getThemeMode() {
        return null;
    }
    
    public final boolean getShowDifficulty() {
        return false;
    }
    
    public final boolean getShowFrequency() {
        return false;
    }
    
    public final boolean getShowStreak() {
        return false;
    }
    
    public final boolean getAutoBackup() {
        return false;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    public UserProgress() {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    public final float component14() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component15() {
        return null;
    }
    
    public final int component16() {
        return 0;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final boolean component18() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component19() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final boolean component21() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component22() {
        return null;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final boolean component26() {
        return false;
    }
    
    public final long component27() {
        return 0L;
    }
    
    public final long component28() {
        return 0L;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final long component4() {
        return 0L;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.word.data.entities.UserProgress copy(long id, int totalWordsLearned, int totalPhrasesLearned, long totalStudyTime, int studyDays, int consecutiveDays, long lastStudyDate, int currentLevel, int experiencePoints, int totalExperience, int totalQuizzes, int correctAnswers, int totalAnswers, float averageAccuracy, @org.jetbrains.annotations.NotNull()
    java.lang.String unlockedAchievements, int totalAchievements, int dailyGoal, boolean reminderEnabled, @org.jetbrains.annotations.NotNull()
    java.lang.String reminderTime, boolean autoPlayPronunciation, boolean slowPronunciation, @org.jetbrains.annotations.NotNull()
    java.lang.String themeMode, boolean showDifficulty, boolean showFrequency, boolean showStreak, boolean autoBackup, long createdAt, long updatedAt) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}