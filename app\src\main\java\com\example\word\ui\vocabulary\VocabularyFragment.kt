package com.example.word.ui.vocabulary

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import kotlinx.coroutines.launch
import com.example.word.databinding.FragmentVocabularyBinding
import com.example.word.ui.viewmodel.WordViewModel
import com.example.word.utils.TTSManager
import com.example.word.utils.TTSDebugHelper
import com.example.word.utils.DatabaseDebugHelper
import com.example.word.utils.SimpleTTSManager
import com.example.word.utils.CrashPrevention.safeUIOperation
import com.example.word.utils.CrashPrevention.withSafeContext

/**
 * 词汇学习Fragment
 */
class VocabularyFragment : Fragment() {

    companion object {
        private const val TAG = "VocabularyFragment"
    }

    private var _binding: FragmentVocabularyBinding? = null
    private val binding get() = _binding!!

    private val wordViewModel: WordViewModel by viewModels()
    private lateinit var wordAdapter: WordAdapter
    private var ttsManager: TTSManager? = null
    private var simpleTTSManager: SimpleTTSManager? = null
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentVocabularyBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        try {
            setupRecyclerView()
            setupSearchView()
            setupObservers()
            setupClickListeners()
            initTTSManager()
            debugDatabaseStatus()
        } catch (e: Exception) {
            Log.e(TAG, "Error in onViewCreated", e)
        }
    }
    
    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        try {
            wordAdapter = WordAdapter(
                onWordClick = { word ->
                    safeUIOperation {
                        // 显示单词详情
                        wordViewModel.setCurrentWord(word)
                        // TODO: 导航到单词详情页面
                    }
                },
                onBookmarkClick = { word ->
                    safeUIOperation {
                        wordViewModel.toggleBookmark(word)
                    }
                },
                onSpeakClick = { wordText ->
                    safeUIOperation {
                        speakWord(wordText)
                    }
                }
            )

            val context = context
            if (context != null) {
                binding.recyclerViewWords.apply {
                    layoutManager = LinearLayoutManager(context)
                    adapter = wordAdapter
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up RecyclerView", e)
        }
    }
    
    /**
     * 设置搜索功能
     */
    private fun setupSearchView() {
        binding.searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                query?.let { wordViewModel.searchWords(it) }
                return true
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                if (newText.isNullOrBlank()) {
                    // 清空搜索，显示所有单词
                    wordViewModel.allWords.observe(viewLifecycleOwner) { words ->
                        wordAdapter.submitList(words)
                    }
                } else if (newText.length >= 2) {
                    // 开始搜索
                    wordViewModel.searchWords(newText)
                }
                return true
            }
        })
    }
    
    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察所有单词
        wordViewModel.allWords.observe(viewLifecycleOwner) { words ->
            if (binding.searchView.query.isNullOrBlank()) {
                wordAdapter.submitList(words)
                updateEmptyState(words.isEmpty())
            }
        }
        
        // 观察搜索结果
        wordViewModel.searchResults.observe(viewLifecycleOwner) { words ->
            if (!binding.searchView.query.isNullOrBlank()) {
                wordAdapter.submitList(words)
                updateEmptyState(words.isEmpty())
            }
        }
        
        // 观察加载状态
        wordViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
        
        // 观察错误信息
        wordViewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
                wordViewModel.clearErrorMessage()
            }
        }
    }
    
    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 筛选按钮
        binding.buttonFilter.setOnClickListener {
            showFilterDialog()
        }
        
        // 收藏按钮
        binding.buttonBookmarks.setOnClickListener {
            showBookmarkedWords()
        }
        
        // 复习按钮
        binding.buttonReview.setOnClickListener {
            showReviewWords()
        }
        
        // 随机学习按钮
        binding.buttonRandomStudy.setOnClickListener {
            startRandomStudy()
        }
    }
    
    /**
     * 更新空状态显示
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.textViewEmpty.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.recyclerViewWords.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }
    
    /**
     * 显示筛选对话框
     */
    private fun showFilterDialog() {
        // TODO: 实现筛选对话框
        Toast.makeText(requireContext(), "筛选功能开发中", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示收藏的单词
     */
    private fun showBookmarkedWords() {
        wordViewModel.bookmarkedWords.observe(viewLifecycleOwner) { words ->
            wordAdapter.submitList(words)
            updateEmptyState(words.isEmpty())
        }
    }
    
    /**
     * 显示需要复习的单词
     */
    private fun showReviewWords() {
        wordViewModel.wordsForReview.observe(viewLifecycleOwner) { words ->
            wordAdapter.submitList(words)
            updateEmptyState(words.isEmpty())
        }
    }
    
    /**
     * 开始随机学习
     */
    private fun startRandomStudy() {
        // TODO: 实现随机学习功能
        Toast.makeText(requireContext(), "随机学习功能开发中", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 初始化TTS管理器
     */
    private fun initTTSManager() {
        try {
            val context = context
            if (context != null) {
                // 首先进行TTS调试检查
                TTSDebugHelper.checkTTSAvailability(context) { isAvailable, message ->
                    Log.d(TAG, "TTS availability check: $isAvailable - $message")
                    safeUIOperation {
                        Toast.makeText(context, "TTS检查: $message", Toast.LENGTH_LONG).show()
                    }
                }

                // 获取TTS引擎信息
                val engineInfo = TTSDebugHelper.getTTSEngineInfo(context)
                Log.d(TAG, engineInfo)

                // 获取系统TTS设置信息
                val systemInfo = TTSDebugHelper.checkSystemTTSSettings(context)
                systemInfo.forEach { info ->
                    Log.d(TAG, "TTS System Info: $info")
                }

                // 初始化简化的TTS管理器
                simpleTTSManager = SimpleTTSManager(context)
                simpleTTSManager?.initialize { success ->
                    Log.d(TAG, "Simple TTS initialization: $success")
                    safeUIOperation {
                        val message = if (success) "TTS初始化成功" else "TTS初始化失败"
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    }
                }

                // 也保留原来的TTS管理器作为备用
                ttsManager = TTSManager(context)
                ttsManager?.onTTSStatusChanged = { isSuccess, message ->
                    Log.d(TAG, "TTS Status: $isSuccess - $message")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing TTS manager", e)
        }
    }

    /**
     * 朗读单词
     */
    private fun speakWord(word: String) {
        try {
            Log.d(TAG, "Attempting to speak word: '$word'")
            val context = context ?: return

            // 从设置中获取是否使用慢速发音
            val sharedPrefs = context.getSharedPreferences("app_settings", android.content.Context.MODE_PRIVATE)
            val useSlowSpeed = sharedPrefs.getBoolean("slow_pronunciation", false)

            // 优先使用简化的TTS管理器
            simpleTTSManager?.let { simpleTTS ->
                Log.d(TAG, "Using SimpleTTSManager, status: ${simpleTTS.getStatus()}")

                if (simpleTTS.isReady()) {
                    val success = simpleTTS.speak(word, useSlowSpeed)
                    Log.d(TAG, "SimpleTTS speak result: $success")

                    safeUIOperation {
                        val message = if (success) "播放: $word" else "播放失败: $word"
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                    }
                    return
                } else {
                    Log.w(TAG, "SimpleTTS not ready, trying to initialize...")
                    simpleTTS.initialize { success ->
                        if (success) {
                            simpleTTS.speak(word, useSlowSpeed)
                        }
                    }
                }
            }

            // 备用：使用原来的TTS管理器
            ttsManager?.let { tts ->
                Log.d(TAG, "Using fallback TTSManager")
                Log.d(TAG, "TTS status: ${tts.getTTSStatus()}")
                Log.d(TAG, "TTS available: ${tts.isTTSAvailable()}")

                tts.speak(word, useSlowSpeed)

                safeUIOperation {
                    Toast.makeText(context, "备用TTS播放: $word", Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                Log.w(TAG, "No TTS manager available")
                safeUIOperation {
                    Toast.makeText(context, "语音功能不可用", Toast.LENGTH_SHORT).show()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error speaking word: $word", e)
            safeUIOperation {
                val ctx = context
                if (ctx != null) {
                    Toast.makeText(ctx, "语音播放错误: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    /**
     * 调试数据库状态
     */
    private fun debugDatabaseStatus() {
        lifecycleScope.launch {
            try {
                val context = context ?: return@launch

                Log.d(TAG, "=== Database Debug Info ===")

                // 检查1.txt文件
                val txtFileExists = DatabaseDebugHelper.check1TxtFile(context)
                Log.d(TAG, "1.txt file exists: $txtFileExists")

                // 测试文本解析
                val parsingResult = DatabaseDebugHelper.testTextParsing(context)
                Log.d(TAG, "Text parsing result: $parsingResult")

                // 检查数据库状态
                val dbStatus = DatabaseDebugHelper.checkDatabaseStatus(context)
                Log.d(TAG, "Database status: $dbStatus")

                // 如果数据库为空，强制重新初始化
                if (dbStatus.wordCount == 0 && dbStatus.phraseCount == 0) {
                    Log.w(TAG, "Database is empty, forcing reinitialization...")
                    safeUIOperation {
                        Toast.makeText(context, "数据库为空，正在重新加载数据...", Toast.LENGTH_LONG).show()
                    }

                    val success = DatabaseDebugHelper.forceReinitializeDatabase(context)
                    if (success) {
                        Log.d(TAG, "Database reinitialization successful")
                        safeUIOperation {
                            Toast.makeText(context, "数据重新加载成功", Toast.LENGTH_SHORT).show()
                        }

                        // 重新检查状态
                        val newStatus = DatabaseDebugHelper.checkDatabaseStatus(context)
                        Log.d(TAG, "New database status: $newStatus")
                    } else {
                        Log.e(TAG, "Database reinitialization failed")
                        safeUIOperation {
                            Toast.makeText(context, "数据重新加载失败", Toast.LENGTH_SHORT).show()
                        }
                    }
                }

                Log.d(TAG, "=== End Database Debug Info ===")

            } catch (e: Exception) {
                Log.e(TAG, "Error in database debug", e)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            simpleTTSManager?.release()
            simpleTTSManager = null

            ttsManager?.release()
            ttsManager = null
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing TTS managers", e)
        }
        _binding = null
    }
}
