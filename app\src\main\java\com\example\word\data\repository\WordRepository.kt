package com.example.word.data.repository

import androidx.lifecycle.LiveData
import com.example.word.data.dao.*
import com.example.word.data.entities.*

/**
 * 词汇学习应用的数据仓库
 * 统一管理所有数据访问操作
 */
class WordRepository(
    private val wordDao: WordDao,
    private val phraseDao: PhraseDao,
    private val essayTemplateDao: EssayTemplateDao,
    private val studySessionDao: StudySessionDao,
    private val userProgressDao: UserProgressDao
) {
    
    // ==================== 单词相关操作 ====================
    
    fun getAllWords(): LiveData<List<Word>> = wordDao.getAllWords()
    
    suspend fun getWordById(id: Long): Word? = wordDao.getWordById(id)
    
    fun searchWords(query: String): LiveData<List<Word>> = wordDao.searchWords(query)
    
    fun getWordsByDifficulty(level: Int): LiveData<List<Word>> = wordDao.getWordsByDifficulty(level)
    
    fun getWordsByPartOfSpeech(pos: String): LiveData<List<Word>> = wordDao.getWordsByPartOfSpeech(pos)
    
    fun getBookmarkedWords(): LiveData<List<Word>> = wordDao.getBookmarkedWords()
    
    fun getWordsForReview(currentTime: Long): LiveData<List<Word>> = wordDao.getWordsForReview(currentTime)
    
    suspend fun getRandomWords(count: Int): List<Word> = wordDao.getRandomWords(count)
    
    fun getWordsByFrequencyRange(startRank: Int, endRank: Int): LiveData<List<Word>> = 
        wordDao.getWordsByFrequencyRange(startRank, endRank)
    
    suspend fun insertWord(word: Word): Long = wordDao.insertWord(word)
    
    suspend fun updateWord(word: Word) = wordDao.updateWord(word)
    
    suspend fun updateWordBookmarkStatus(id: Long, isBookmarked: Boolean) = 
        wordDao.updateBookmarkStatus(id, isBookmarked)
    
    suspend fun updateWordStudyStats(id: Long, time: Long, accuracy: Int) = 
        wordDao.updateStudyStats(id, time, accuracy)
    
    suspend fun updateWordReviewInfo(id: Long, nextTime: Long, interval: Int, strength: Float) = 
        wordDao.updateReviewInfo(id, nextTime, interval, strength)
    
    // ==================== 短语相关操作 ====================
    
    fun getAllPhrases(): LiveData<List<Phrase>> = phraseDao.getAllPhrases()
    
    suspend fun getPhraseById(id: Long): Phrase? = phraseDao.getPhraseById(id)
    
    fun searchPhrases(query: String): LiveData<List<Phrase>> = phraseDao.searchPhrases(query)
    
    fun getPhrasesByType(type: String): LiveData<List<Phrase>> = phraseDao.getPhrasesByType(type)
    
    fun getPhrasesByCategory(category: String): LiveData<List<Phrase>> = phraseDao.getPhrasesByCategory(category)
    
    fun getPhrasesByDifficulty(level: Int): LiveData<List<Phrase>> = phraseDao.getPhrasesByDifficulty(level)
    
    fun getBookmarkedPhrases(): LiveData<List<Phrase>> = phraseDao.getBookmarkedPhrases()
    
    suspend fun getRandomPhrases(count: Int): List<Phrase> = phraseDao.getRandomPhrases(count)
    
    suspend fun getAllPhraseTypes(): List<String> = phraseDao.getAllPhraseTypes()
    
    suspend fun getAllPhraseCategories(): List<String> = phraseDao.getAllCategories()
    
    suspend fun insertPhrase(phrase: Phrase): Long = phraseDao.insertPhrase(phrase)
    
    suspend fun updatePhrase(phrase: Phrase) = phraseDao.updatePhrase(phrase)
    
    suspend fun updatePhraseBookmarkStatus(id: Long, isBookmarked: Boolean) = 
        phraseDao.updateBookmarkStatus(id, isBookmarked)
    
    suspend fun updatePhraseStudyStats(id: Long, time: Long) = 
        phraseDao.updateStudyStats(id, time)
    
    // ==================== 作文模板相关操作 ====================
    
    fun getAllTemplates(): LiveData<List<EssayTemplate>> = essayTemplateDao.getAllTemplates()
    
    suspend fun getTemplateById(id: Long): EssayTemplate? = essayTemplateDao.getTemplateById(id)
    
    fun getTemplatesByType(type: String): LiveData<List<EssayTemplate>> = essayTemplateDao.getTemplatesByType(type)
    
    fun getTemplatesByCategory(category: String): LiveData<List<EssayTemplate>> = essayTemplateDao.getTemplatesByCategory(category)
    
    fun getTemplatesByDifficulty(level: Int): LiveData<List<EssayTemplate>> = essayTemplateDao.getTemplatesByDifficulty(level)
    
    fun searchTemplates(query: String): LiveData<List<EssayTemplate>> = essayTemplateDao.searchTemplates(query)
    
    fun getBookmarkedTemplates(): LiveData<List<EssayTemplate>> = essayTemplateDao.getBookmarkedTemplates()
    
    fun getMostUsedTemplates(count: Int): LiveData<List<EssayTemplate>> = essayTemplateDao.getMostUsedTemplates(count)
    
    suspend fun getAllTemplateTypes(): List<String> = essayTemplateDao.getAllTemplateTypes()
    
    suspend fun getAllTemplateCategories(): List<String> = essayTemplateDao.getAllCategories()
    
    suspend fun insertTemplate(template: EssayTemplate): Long = essayTemplateDao.insertTemplate(template)
    
    suspend fun updateTemplate(template: EssayTemplate) = essayTemplateDao.updateTemplate(template)
    
    suspend fun updateTemplateBookmarkStatus(id: Long, isBookmarked: Boolean) = 
        essayTemplateDao.updateBookmarkStatus(id, isBookmarked)
    
    suspend fun incrementTemplateUsageCount(id: Long) = essayTemplateDao.incrementUsageCount(id)
    
    // ==================== 学习记录相关操作 ====================
    
    suspend fun insertStudySession(session: StudySession) = studySessionDao.insertStudySession(session)
    
    fun getAllStudySessions(): LiveData<List<StudySession>> = studySessionDao.getAllStudySessions()
    
    fun getStudySessionsByDateRange(startDate: Long, endDate: Long): LiveData<List<StudySession>> = 
        studySessionDao.getStudySessionsByDateRange(startDate, endDate)
    
    fun getStudySessionsByType(type: String): LiveData<List<StudySession>> = 
        studySessionDao.getStudySessionsByType(type)
    
    suspend fun getStudyCountByDateRange(startDate: Long, endDate: Long): Int = 
        studySessionDao.getStudyCountByDateRange(startDate, endDate)
    
    suspend fun getCorrectAnswersCount(startDate: Long, endDate: Long): Int = 
        studySessionDao.getCorrectAnswersCount(startDate, endDate)
    
    suspend fun getAverageResponseTime(startDate: Long, endDate: Long): Float = 
        studySessionDao.getAverageResponseTime(startDate, endDate)
    
    // ==================== 用户进度相关操作 ====================

    fun getUserProgress(): LiveData<UserProgress?> = userProgressDao.getUserProgress()

    suspend fun getUserProgressSync(): UserProgress? = userProgressDao.getUserProgressSync()

    suspend fun updateUserProgress(progress: UserProgress) = userProgressDao.insertOrUpdateUserProgress(progress)

    suspend fun updateStudyTime(additionalTime: Long, lastTime: Long) =
        userProgressDao.addStudyTime(additionalTime)

    suspend fun addExperiencePoints(exp: Int) = userProgressDao.addExperience(exp)
    
    // ==================== 统计相关操作 ====================
    
    suspend fun getStudiedWordsCount(): Int = wordDao.getStudiedWordsCount()
    
    suspend fun getBookmarkedWordsCount(): Int = wordDao.getBookmarkedWordsCount()
    
    suspend fun getWordsAverageAccuracy(): Float = wordDao.getAverageAccuracy()
}
