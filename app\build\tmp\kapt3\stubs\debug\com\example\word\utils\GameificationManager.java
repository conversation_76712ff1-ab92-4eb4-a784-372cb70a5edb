package com.example.word.utils;

/**
 * 游戏化系统管理器
 * 负责处理积分、等级、成就等游戏化元素
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0005\u001f !\"#B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0004J\u001c\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0010J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0013\u001a\u00020\u0004J\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0015\u001a\u00020\u0004J\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0017\u001a\u00020\u0004J\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0019\u001a\u00020\u0004J\u0010\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u0004H\u0002J\u0010\u0010\u001c\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u0004H\u0002J\u0010\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001b\u001a\u00020\u0004H\u0002\u00a8\u0006$"}, d2 = {"Lcom/example/word/utils/GameificationManager;", "", "()V", "calculateExperienceReward", "", "type", "Lcom/example/word/utils/GameificationManager$ExperienceType;", "multiplier", "", "calculateUserLevel", "Lcom/example/word/utils/GameificationManager$UserLevel;", "totalExperience", "checkNewAchievements", "", "Lcom/example/word/utils/GameificationManager$Achievement;", "oldStats", "Lcom/example/word/utils/GameificationManager$StudyStats;", "newStats", "createConsecutiveAchievements", "consecutiveDays", "createExperienceAchievements", "exp", "createStudyDaysAchievements", "studyDays", "createVocabularyAchievements", "studiedWords", "getExperienceRequiredForLevel", "level", "getLevelIcon", "getLevelTitle", "", "Achievement", "AchievementCategory", "ExperienceType", "StudyStats", "UserLevel", "app_debug"})
public final class GameificationManager {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.GameificationManager INSTANCE = null;
    
    private GameificationManager() {
        super();
    }
    
    /**
     * 计算用户等级
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.word.utils.GameificationManager.UserLevel calculateUserLevel(int totalExperience) {
        return null;
    }
    
    /**
     * 获取等级所需经验值
     */
    private final int getExperienceRequiredForLevel(int level) {
        return 0;
    }
    
    /**
     * 获取等级称号
     */
    private final java.lang.String getLevelTitle(int level) {
        return null;
    }
    
    /**
     * 获取等级图标
     */
    private final int getLevelIcon(int level) {
        return 0;
    }
    
    /**
     * 计算经验值奖励
     */
    public final int calculateExperienceReward(@org.jetbrains.annotations.NotNull()
    com.example.word.utils.GameificationManager.ExperienceType type, float multiplier) {
        return 0;
    }
    
    /**
     * 检查是否有新成就解锁
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.GameificationManager.Achievement> checkNewAchievements(@org.jetbrains.annotations.NotNull()
    com.example.word.utils.GameificationManager.StudyStats oldStats, @org.jetbrains.annotations.NotNull()
    com.example.word.utils.GameificationManager.StudyStats newStats) {
        return null;
    }
    
    /**
     * 创建学习天数成就
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.GameificationManager.Achievement> createStudyDaysAchievements(int studyDays) {
        return null;
    }
    
    /**
     * 创建词汇量成就
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.GameificationManager.Achievement> createVocabularyAchievements(int studiedWords) {
        return null;
    }
    
    /**
     * 创建连续学习成就
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.GameificationManager.Achievement> createConsecutiveAchievements(int consecutiveDays) {
        return null;
    }
    
    /**
     * 创建经验值成就
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.word.utils.GameificationManager.Achievement> createExperienceAchievements(int exp) {
        return null;
    }
    
    /**
     * 成就数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001c\b\u0086\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u0007\u0012\u0006\u0010\r\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\u0007H\u00c6\u0003J\t\u0010!\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\"\u001a\u00020\u000bH\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\u000eH\u00c6\u0003Jc\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u000eH\u00c6\u0001J\u0013\u0010&\u001a\u00020\u000b2\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020\u0007H\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0019R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015\u00a8\u0006*"}, d2 = {"Lcom/example/word/utils/GameificationManager$Achievement;", "", "id", "", "title", "description", "iconResId", "", "targetValue", "currentValue", "isUnlocked", "", "experienceReward", "category", "Lcom/example/word/utils/GameificationManager$AchievementCategory;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIZILcom/example/word/utils/GameificationManager$AchievementCategory;)V", "getCategory", "()Lcom/example/word/utils/GameificationManager$AchievementCategory;", "getCurrentValue", "()I", "getDescription", "()Ljava/lang/String;", "getExperienceReward", "getIconResId", "getId", "()Z", "getTargetValue", "getTitle", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
    public static final class Achievement {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String description = null;
        private final int iconResId = 0;
        private final int targetValue = 0;
        private final int currentValue = 0;
        private final boolean isUnlocked = false;
        private final int experienceReward = 0;
        @org.jetbrains.annotations.NotNull()
        private final com.example.word.utils.GameificationManager.AchievementCategory category = null;
        
        public Achievement(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String description, int iconResId, int targetValue, int currentValue, boolean isUnlocked, int experienceReward, @org.jetbrains.annotations.NotNull()
        com.example.word.utils.GameificationManager.AchievementCategory category) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDescription() {
            return null;
        }
        
        public final int getIconResId() {
            return 0;
        }
        
        public final int getTargetValue() {
            return 0;
        }
        
        public final int getCurrentValue() {
            return 0;
        }
        
        public final boolean isUnlocked() {
            return false;
        }
        
        public final int getExperienceReward() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.GameificationManager.AchievementCategory getCategory() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final boolean component7() {
            return false;
        }
        
        public final int component8() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.GameificationManager.AchievementCategory component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.GameificationManager.Achievement copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.lang.String title, @org.jetbrains.annotations.NotNull()
        java.lang.String description, int iconResId, int targetValue, int currentValue, boolean isUnlocked, int experienceReward, @org.jetbrains.annotations.NotNull()
        com.example.word.utils.GameificationManager.AchievementCategory category) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 成就分类枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/word/utils/GameificationManager$AchievementCategory;", "", "(Ljava/lang/String;I)V", "STUDY_DAYS", "VOCABULARY", "CONSECUTIVE", "EXPERIENCE", "app_debug"})
    public static enum AchievementCategory {
        /*public static final*/ STUDY_DAYS /* = new STUDY_DAYS() */,
        /*public static final*/ VOCABULARY /* = new VOCABULARY() */,
        /*public static final*/ CONSECUTIVE /* = new CONSECUTIVE() */,
        /*public static final*/ EXPERIENCE /* = new EXPERIENCE() */;
        
        AchievementCategory() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.word.utils.GameificationManager.AchievementCategory> getEntries() {
            return null;
        }
    }
    
    /**
     * 经验值类型枚举
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/example/word/utils/GameificationManager$ExperienceType;", "", "points", "", "(Ljava/lang/String;II)V", "getPoints", "()I", "WORD_LEARNED", "PHRASE_LEARNED", "QUIZ_CORRECT", "QUIZ_PERFECT", "DAILY_GOAL_COMPLETED", "STREAK_BONUS", "app_debug"})
    public static enum ExperienceType {
        /*public static final*/ WORD_LEARNED /* = new WORD_LEARNED(0) */,
        /*public static final*/ PHRASE_LEARNED /* = new PHRASE_LEARNED(0) */,
        /*public static final*/ QUIZ_CORRECT /* = new QUIZ_CORRECT(0) */,
        /*public static final*/ QUIZ_PERFECT /* = new QUIZ_PERFECT(0) */,
        /*public static final*/ DAILY_GOAL_COMPLETED /* = new DAILY_GOAL_COMPLETED(0) */,
        /*public static final*/ STREAK_BONUS /* = new STREAK_BONUS(0) */;
        private final int points = 0;
        
        ExperienceType(int points) {
        }
        
        public final int getPoints() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.word.utils.GameificationManager.ExperienceType> getEntries() {
            return null;
        }
    }
    
    /**
     * 学习统计数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J;\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\nR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\n\u00a8\u0006\u001b"}, d2 = {"Lcom/example/word/utils/GameificationManager$StudyStats;", "", "totalStudyDays", "", "consecutiveStudyDays", "totalWordsLearned", "totalPhrasesLearned", "totalExperience", "(IIIII)V", "getConsecutiveStudyDays", "()I", "getTotalExperience", "getTotalPhrasesLearned", "getTotalStudyDays", "getTotalWordsLearned", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class StudyStats {
        private final int totalStudyDays = 0;
        private final int consecutiveStudyDays = 0;
        private final int totalWordsLearned = 0;
        private final int totalPhrasesLearned = 0;
        private final int totalExperience = 0;
        
        public StudyStats(int totalStudyDays, int consecutiveStudyDays, int totalWordsLearned, int totalPhrasesLearned, int totalExperience) {
            super();
        }
        
        public final int getTotalStudyDays() {
            return 0;
        }
        
        public final int getConsecutiveStudyDays() {
            return 0;
        }
        
        public final int getTotalWordsLearned() {
            return 0;
        }
        
        public final int getTotalPhrasesLearned() {
            return 0;
        }
        
        public final int getTotalExperience() {
            return 0;
        }
        
        public StudyStats() {
            super();
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.GameificationManager.StudyStats copy(int totalStudyDays, int consecutiveStudyDays, int totalWordsLearned, int totalPhrasesLearned, int totalExperience) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * 用户等级数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J;\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010\u00a8\u0006\u001c"}, d2 = {"Lcom/example/word/utils/GameificationManager$UserLevel;", "", "level", "", "title", "", "experienceRequired", "currentExperience", "iconResId", "(ILjava/lang/String;III)V", "getCurrentExperience", "()I", "getExperienceRequired", "getIconResId", "getLevel", "getTitle", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class UserLevel {
        private final int level = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String title = null;
        private final int experienceRequired = 0;
        private final int currentExperience = 0;
        private final int iconResId = 0;
        
        public UserLevel(int level, @org.jetbrains.annotations.NotNull()
        java.lang.String title, int experienceRequired, int currentExperience, int iconResId) {
            super();
        }
        
        public final int getLevel() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTitle() {
            return null;
        }
        
        public final int getExperienceRequired() {
            return 0;
        }
        
        public final int getCurrentExperience() {
            return 0;
        }
        
        public final int getIconResId() {
            return 0;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.GameificationManager.UserLevel copy(int level, @org.jetbrains.annotations.NotNull()
        java.lang.String title, int experienceRequired, int currentExperience, int iconResId) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}