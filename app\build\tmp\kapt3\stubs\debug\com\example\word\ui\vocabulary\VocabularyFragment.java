package com.example.word.ui.vocabulary;

/**
 * 词汇学习Fragment
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000e\n\u0002\b\u0006\u0018\u0000 62\u00020\u0001:\u00016B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001a\u001a\u00020\u001bH\u0002J\b\u0010\u001c\u001a\u00020\u001bH\u0002J$\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\"2\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J\b\u0010%\u001a\u00020\u001bH\u0016J\u001a\u0010&\u001a\u00020\u001b2\u0006\u0010\'\u001a\u00020\u001e2\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J\b\u0010(\u001a\u00020\u001bH\u0002J\b\u0010)\u001a\u00020\u001bH\u0002J\b\u0010*\u001a\u00020\u001bH\u0002J\b\u0010+\u001a\u00020\u001bH\u0002J\b\u0010,\u001a\u00020\u001bH\u0002J\b\u0010-\u001a\u00020\u001bH\u0002J\b\u0010.\u001a\u00020\u001bH\u0002J\u0010\u0010/\u001a\u00020\u001b2\u0006\u00100\u001a\u000201H\u0002J\b\u00102\u001a\u00020\u001bH\u0002J\b\u00103\u001a\u00020\u001bH\u0002J\u0010\u00104\u001a\u00020\u001b2\u0006\u00105\u001a\u00020\u000bH\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0014\u001a\u00020\u00158BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0018\u0010\u0019\u001a\u0004\b\u0016\u0010\u0017\u00a8\u00067"}, d2 = {"Lcom/example/word/ui/vocabulary/VocabularyFragment;", "Landroidx/fragment/app/Fragment;", "()V", "_binding", "Lcom/example/word/databinding/FragmentVocabularyBinding;", "binding", "getBinding", "()Lcom/example/word/databinding/FragmentVocabularyBinding;", "enhancedOnlineTTSManager", "Lcom/example/word/utils/EnhancedOnlineTTSManager;", "hasPerformedDebugCheck", "", "simpleTTSManager", "Lcom/example/word/utils/SimpleTTSManager;", "ttsFallbackManager", "Lcom/example/word/utils/TTSFallbackManager;", "ttsManager", "Lcom/example/word/utils/TTSManager;", "wordAdapter", "Lcom/example/word/ui/vocabulary/WordAdapter;", "wordViewModel", "Lcom/example/word/ui/viewmodel/WordViewModel;", "getWordViewModel", "()Lcom/example/word/ui/viewmodel/WordViewModel;", "wordViewModel$delegate", "Lkotlin/Lazy;", "debugDatabaseStatus", "", "initTTSManager", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "onViewCreated", "view", "setupClickListeners", "setupObservers", "setupRecyclerView", "setupSearchView", "showBookmarkedWords", "showFilterDialog", "showReviewWords", "speakWord", "word", "", "startRandomStudy", "testOnlineTTS", "updateEmptyState", "isEmpty", "Companion", "app_debug"})
public final class VocabularyFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "VocabularyFragment";
    @org.jetbrains.annotations.Nullable()
    private com.example.word.databinding.FragmentVocabularyBinding _binding;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy wordViewModel$delegate = null;
    private com.example.word.ui.vocabulary.WordAdapter wordAdapter;
    @org.jetbrains.annotations.Nullable()
    private com.example.word.utils.TTSManager ttsManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.word.utils.SimpleTTSManager simpleTTSManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.word.utils.TTSFallbackManager ttsFallbackManager;
    @org.jetbrains.annotations.Nullable()
    private com.example.word.utils.EnhancedOnlineTTSManager enhancedOnlineTTSManager;
    private boolean hasPerformedDebugCheck = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.ui.vocabulary.VocabularyFragment.Companion Companion = null;
    
    public VocabularyFragment() {
        super();
    }
    
    private final com.example.word.databinding.FragmentVocabularyBinding getBinding() {
        return null;
    }
    
    private final com.example.word.ui.viewmodel.WordViewModel getWordViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * 设置RecyclerView
     */
    private final void setupRecyclerView() {
    }
    
    /**
     * 设置搜索功能
     */
    private final void setupSearchView() {
    }
    
    /**
     * 设置观察者
     */
    private final void setupObservers() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners() {
    }
    
    /**
     * 更新空状态显示
     */
    private final void updateEmptyState(boolean isEmpty) {
    }
    
    /**
     * 显示筛选对话框
     */
    private final void showFilterDialog() {
    }
    
    /**
     * 显示收藏的单词
     */
    private final void showBookmarkedWords() {
    }
    
    /**
     * 显示需要复习的单词
     */
    private final void showReviewWords() {
    }
    
    /**
     * 开始随机学习
     */
    private final void startRandomStudy() {
    }
    
    /**
     * 初始化TTS管理器
     */
    private final void initTTSManager() {
    }
    
    /**
     * 朗读单词
     */
    private final void speakWord(java.lang.String word) {
    }
    
    /**
     * 调试数据库状态
     */
    private final void debugDatabaseStatus() {
    }
    
    /**
     * 测试在线TTS功能
     */
    private final void testOnlineTTS() {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/word/ui/vocabulary/VocabularyFragment$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}