package com.example.word.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * 学习提醒广播接收器
 * 处理定时学习提醒通知
 */
class StudyReminderReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "StudyReminderReceiver"
    }
    
    override fun onReceive(context: Context?, intent: Intent?) {
        try {
            Log.d(TAG, "Received study reminder broadcast")

            // 确保context不为null
            if (context == null) {
                Log.e(TAG, "Context is null, cannot show notification")
                return
            }

            // 显示学习提醒通知
            StudyNotificationManager.showStudyReminderNotification(context)

        } catch (e: Exception) {
            Log.e(TAG, "Error in onReceive", e)
            // 不抛出异常，避免系统崩溃
        }
    }
}
