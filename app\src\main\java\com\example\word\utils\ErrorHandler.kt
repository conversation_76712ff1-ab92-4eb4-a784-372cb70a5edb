package com.example.word.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 错误处理和崩溃报告系统
 */
object ErrorHandler {
    
    private const val TAG = "ErrorHandler"
    private const val CRASH_LOG_FILE = "crash_logs.txt"
    private const val ERROR_LOG_FILE = "error_logs.txt"
    private const val MAX_LOG_SIZE = 1024 * 1024 // 1MB
    private const val PREFS_NAME = "error_handler"
    private const val KEY_CRASH_COUNT = "crash_count"
    private const val KEY_LAST_CRASH_TIME = "last_crash_time"
    
    /**
     * 初始化错误处理器
     */
    fun initialize(context: Context) {
        try {
            Log.d(TAG, "Initializing error handler")
            
            // 设置全局异常处理器
            val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
                handleCrash(context, thread, exception)
                defaultHandler?.uncaughtException(thread, exception)
            }
            
            // 清理旧日志
            cleanupOldLogs(context)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize error handler", e)
        }
    }
    
    /**
     * 处理崩溃
     */
    private fun handleCrash(context: Context, thread: Thread, exception: Throwable) {
        try {
            Log.e(TAG, "Application crashed", exception)
            
            // 记录崩溃信息
            logCrash(context, thread, exception)
            
            // 更新崩溃统计
            updateCrashStats(context)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling crash", e)
        }
    }
    
    /**
     * 记录崩溃信息
     */
    private fun logCrash(context: Context, thread: Thread, exception: Throwable) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val crashLog = buildCrashReport(thread, exception)
                writeToFile(context, CRASH_LOG_FILE, crashLog)
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to log crash", e)
            }
        }
    }
    
    /**
     * 记录一般错误
     */
    fun logError(context: Context, tag: String, message: String, throwable: Throwable? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val errorLog = buildErrorReport(tag, message, throwable)
                writeToFile(context, ERROR_LOG_FILE, errorLog)
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to log error", e)
            }
        }
    }
    
    /**
     * 构建崩溃报告
     */
    private fun buildCrashReport(thread: Thread, exception: Throwable): String {
        return buildString {
            appendLine("=== CRASH REPORT ===")
            appendLine("Time: ${getCurrentTimestamp()}")
            appendLine("Thread: ${thread.name}")
            appendLine("Exception: ${exception.javaClass.simpleName}")
            appendLine("Message: ${exception.message}")
            appendLine("Stack trace:")
            exception.stackTrace.forEach { element ->
                appendLine("  at $element")
            }
            
            // 记录原因链
            var cause = exception.cause
            var level = 1
            while (cause != null && level <= 5) {
                appendLine("Caused by (level $level): ${cause.javaClass.simpleName}")
                appendLine("Message: ${cause.message}")
                cause.stackTrace.take(5).forEach { element ->
                    appendLine("  at $element")
                }
                cause = cause.cause
                level++
            }
            
            appendLine("=== END CRASH REPORT ===")
            appendLine()
        }
    }
    
    /**
     * 构建错误报告
     */
    private fun buildErrorReport(tag: String, message: String, throwable: Throwable?): String {
        return buildString {
            appendLine("=== ERROR REPORT ===")
            appendLine("Time: ${getCurrentTimestamp()}")
            appendLine("Tag: $tag")
            appendLine("Message: $message")
            
            throwable?.let { t ->
                appendLine("Exception: ${t.javaClass.simpleName}")
                appendLine("Exception Message: ${t.message}")
                appendLine("Stack trace:")
                t.stackTrace.take(10).forEach { element ->
                    appendLine("  at $element")
                }
            }
            
            appendLine("=== END ERROR REPORT ===")
            appendLine()
        }
    }
    
    /**
     * 写入文件
     */
    private fun writeToFile(context: Context, fileName: String, content: String) {
        try {
            val file = File(context.filesDir, fileName)
            
            // 检查文件大小，如果太大则清理
            if (file.exists() && file.length() > MAX_LOG_SIZE) {
                file.delete()
            }
            
            FileWriter(file, true).use { writer ->
                writer.append(content)
                writer.flush()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write to file: $fileName", e)
        }
    }
    
    /**
     * 更新崩溃统计
     */
    private fun updateCrashStats(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val crashCount = prefs.getInt(KEY_CRASH_COUNT, 0)
            
            prefs.edit()
                .putInt(KEY_CRASH_COUNT, crashCount + 1)
                .putLong(KEY_LAST_CRASH_TIME, System.currentTimeMillis())
                .apply()
                
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update crash stats", e)
        }
    }
    
    /**
     * 获取崩溃统计
     */
    fun getCrashStats(context: Context): CrashStats {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            CrashStats(
                crashCount = prefs.getInt(KEY_CRASH_COUNT, 0),
                lastCrashTime = prefs.getLong(KEY_LAST_CRASH_TIME, 0)
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get crash stats", e)
            CrashStats(0, 0)
        }
    }
    
    /**
     * 清理旧日志
     */
    private fun cleanupOldLogs(context: Context) {
        try {
            val crashFile = File(context.filesDir, CRASH_LOG_FILE)
            val errorFile = File(context.filesDir, ERROR_LOG_FILE)
            
            // 如果文件超过7天，删除它们
            val weekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
            
            if (crashFile.exists() && crashFile.lastModified() < weekAgo) {
                crashFile.delete()
                Log.d(TAG, "Cleaned up old crash log")
            }
            
            if (errorFile.exists() && errorFile.lastModified() < weekAgo) {
                errorFile.delete()
                Log.d(TAG, "Cleaned up old error log")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup old logs", e)
        }
    }
    
    /**
     * 获取当前时间戳
     */
    private fun getCurrentTimestamp(): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
            .format(Date())
    }
    
    /**
     * 崩溃统计数据类
     */
    data class CrashStats(
        val crashCount: Int,
        val lastCrashTime: Long
    )
}
