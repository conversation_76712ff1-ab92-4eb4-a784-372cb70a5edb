package com.example.word.data.dao;

/**
 * 作文模板数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000eH\'J\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000eH\'J\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u0011\u001a\u00020\u0012H\'J\u0018\u0010\u0013\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u000e\u0010\u0017\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u0019\u001a\u00020\u000bH\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u001b\u001a\u00020\u0012H\'J\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u001d\u001a\u00020\u000bH\'J\u0016\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u001f\u001a\u00020\u00152\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010 \u001a\u00020\u00032\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\nH\u00a7@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010$\u001a\u00020\u000bH\'J\u001e\u0010%\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010&\u001a\u00020\'H\u00a7@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006*"}, d2 = {"Lcom/example/word/data/dao/EssayTemplateDao;", "", "deleteAllTemplates", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteTemplate", "template", "Lcom/example/word/data/entities/EssayTemplate;", "(Lcom/example/word/data/entities/EssayTemplate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCategories", "", "", "getAllTemplateTypes", "getAllTemplates", "Landroidx/lifecycle/LiveData;", "getBookmarkedTemplates", "getMostUsedTemplates", "count", "", "getTemplateById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTemplateCount", "getTemplatesByCategory", "category", "getTemplatesByDifficulty", "level", "getTemplatesByType", "type", "incrementUsageCount", "insertTemplate", "insertTemplates", "templates", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTemplates", "query", "updateBookmarkStatus", "isBookmarked", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTemplate", "app_debug"})
@androidx.room.Dao()
public abstract interface EssayTemplateDao {
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates ORDER BY type, difficultyLevel")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getAllTemplates();
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTemplateById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.word.data.entities.EssayTemplate> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE type = :type ORDER BY difficultyLevel")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getTemplatesByType(@org.jetbrains.annotations.NotNull()
    java.lang.String type);
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE category = :category ORDER BY difficultyLevel")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getTemplatesByCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE difficultyLevel = :level ORDER BY type")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getTemplatesByDifficulty(int level);
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE title LIKE \'%\' || :query || \'%\' OR content LIKE \'%\' || :query || \'%\' OR description LIKE \'%\' || :query || \'%\'")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> searchTemplates(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates WHERE isBookmarked = 1 ORDER BY usageCount DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getBookmarkedTemplates();
    
    @androidx.room.Query(value = "SELECT * FROM essay_templates ORDER BY usageCount DESC LIMIT :count")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.EssayTemplate>> getMostUsedTemplates(int count);
    
    @androidx.room.Query(value = "SELECT DISTINCT type FROM essay_templates")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllTemplateTypes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @androidx.room.Query(value = "SELECT DISTINCT category FROM essay_templates")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllCategories(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM essay_templates")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTemplateCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertTemplate(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.EssayTemplate template, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertTemplates(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.word.data.entities.EssayTemplate> templates, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTemplate(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.EssayTemplate template, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE essay_templates SET isBookmarked = :isBookmarked WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBookmarkStatus(long id, boolean isBookmarked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE essay_templates SET usageCount = usageCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementUsageCount(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTemplate(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.EssayTemplate template, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM essay_templates")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllTemplates(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}