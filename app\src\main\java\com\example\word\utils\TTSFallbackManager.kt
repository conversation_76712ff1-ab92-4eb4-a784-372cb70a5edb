package com.example.word.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.speech.tts.TextToSpeech
import android.util.Log
import android.widget.Toast
import java.util.*

/**
 * TTS备用管理器
 * 当系统TTS不可用时提供备用方案
 */
class TTSFallbackManager(private val context: Context) {
    
    companion object {
        private const val TAG = "TTSFallbackManager"
    }
    
    /**
     * 检查TTS可用性并提供解决方案
     */
    fun checkAndProvideSolution(onResult: (Boolean, String) -> Unit) {
        Log.d(TAG, "Checking TTS availability...")
        
        try {
            var testTTS: TextToSpeech? = null
            testTTS = TextToSpeech(context) { status ->
                when (status) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "TTS initialization successful")
                        testTTS?.let { tts ->
                            val langResult = tts.setLanguage(Locale.US)
                            when (langResult) {
                                TextToSpeech.LANG_MISSING_DATA,
                                TextToSpeech.LANG_NOT_SUPPORTED -> {
                                    Log.w(TAG, "English language not supported")
                                    tts.shutdown()
                                    onResult(false, "需要安装英语TTS语言包")
                                    showLanguagePackSolution()
                                }
                                else -> {
                                    Log.d(TAG, "TTS is working properly")
                                    tts.shutdown()
                                    onResult(true, "TTS功能正常")
                                }
                            }
                        }
                    }
                    TextToSpeech.ERROR -> {
                        Log.e(TAG, "TTS initialization failed")
                        onResult(false, "TTS初始化失败")
                        showTTSInstallSolution()
                    }
                    else -> {
                        Log.e(TAG, "TTS initialization failed with status: $status")
                        onResult(false, "TTS不可用")
                        showTTSInstallSolution()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking TTS", e)
            onResult(false, "TTS检查失败")
            showTTSInstallSolution()
        }
    }
    
    /**
     * 显示TTS安装解决方案
     */
    private fun showTTSInstallSolution() {
        try {
            Toast.makeText(
                context,
                "设备缺少TTS引擎，请安装Google文字转语音或其他TTS应用",
                Toast.LENGTH_LONG
            ).show()
            
            // 可以尝试打开Google Play商店安装TTS
            // 但这里只是提示用户
            Log.d(TAG, "Suggested installing TTS engine")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing TTS install solution", e)
        }
    }
    
    /**
     * 显示语言包解决方案
     */
    private fun showLanguagePackSolution() {
        try {
            Toast.makeText(
                context,
                "请在系统设置中下载英语TTS语言包",
                Toast.LENGTH_LONG
            ).show()
            
            Log.d(TAG, "Suggested installing English language pack")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing language pack solution", e)
        }
    }
    
    /**
     * 尝试打开TTS设置
     */
    fun openTTSSettings(): Boolean {
        return try {
            val intent = Intent("com.android.settings.TTS_SETTINGS")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Cannot open TTS settings", e)
            false
        }
    }
    
    /**
     * 提供文本显示作为语音的替代方案
     */
    fun showTextAsFallback(text: String) {
        try {
            Toast.makeText(
                context,
                "📢 $text",
                Toast.LENGTH_SHORT
            ).show()
            
            Log.d(TAG, "Showing text as fallback: $text")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error showing text fallback", e)
        }
    }
    
    /**
     * 获取TTS状态信息
     */
    fun getTTSStatusInfo(): String {
        return try {
            var info = "TTS状态检查:\n"
            
            // 检查TTS意图是否可用
            val ttsIntent = Intent(TextToSpeech.Engine.ACTION_CHECK_TTS_DATA)
            val activities = context.packageManager.queryIntentActivities(ttsIntent, 0)
            info += "TTS检查意图可用: ${activities.isNotEmpty()}\n"
            
            // 检查系统语言
            val locale = Locale.getDefault()
            info += "系统语言: ${locale.language}_${locale.country}\n"
            
            // 检查TTS设置意图
            val settingsIntent = Intent("com.android.settings.TTS_SETTINGS")
            val settingsActivities = context.packageManager.queryIntentActivities(settingsIntent, 0)
            info += "TTS设置可用: ${settingsActivities.isNotEmpty()}"
            
            info
        } catch (e: Exception) {
            "获取TTS状态信息失败: ${e.message}"
        }
    }
}
