package com.example.word.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.word.data.entities.StudySession;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class StudySessionDao_Impl implements StudySessionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<StudySession> __insertionAdapterOfStudySession;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldStudySessions;

  public StudySessionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfStudySession = new EntityInsertionAdapter<StudySession>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `study_sessions` (`id`,`studyDate`,`studyType`,`itemId`,`result`,`responseTime`,`studyMode`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final StudySession entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getStudyDate());
        if (entity.getStudyType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getStudyType());
        }
        statement.bindLong(4, entity.getItemId());
        if (entity.getResult() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getResult());
        }
        statement.bindLong(6, entity.getResponseTime());
        if (entity.getStudyMode() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getStudyMode());
        }
      }
    };
    this.__preparedStmtOfDeleteOldStudySessions = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM study_sessions WHERE studyDate < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertStudySession(final StudySession session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfStudySession.insert(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldStudySessions(final long cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldStudySessions.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, cutoffDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldStudySessions.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<StudySession>> getAllStudySessions() {
    final String _sql = "SELECT * FROM study_sessions ORDER BY studyDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"study_sessions"}, false, new Callable<List<StudySession>>() {
      @Override
      @Nullable
      public List<StudySession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfStudyDate = CursorUtil.getColumnIndexOrThrow(_cursor, "studyDate");
          final int _cursorIndexOfStudyType = CursorUtil.getColumnIndexOrThrow(_cursor, "studyType");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "itemId");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfResponseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "responseTime");
          final int _cursorIndexOfStudyMode = CursorUtil.getColumnIndexOrThrow(_cursor, "studyMode");
          final List<StudySession> _result = new ArrayList<StudySession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StudySession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpStudyDate;
            _tmpStudyDate = _cursor.getLong(_cursorIndexOfStudyDate);
            final String _tmpStudyType;
            if (_cursor.isNull(_cursorIndexOfStudyType)) {
              _tmpStudyType = null;
            } else {
              _tmpStudyType = _cursor.getString(_cursorIndexOfStudyType);
            }
            final long _tmpItemId;
            _tmpItemId = _cursor.getLong(_cursorIndexOfItemId);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final long _tmpResponseTime;
            _tmpResponseTime = _cursor.getLong(_cursorIndexOfResponseTime);
            final String _tmpStudyMode;
            if (_cursor.isNull(_cursorIndexOfStudyMode)) {
              _tmpStudyMode = null;
            } else {
              _tmpStudyMode = _cursor.getString(_cursorIndexOfStudyMode);
            }
            _item = new StudySession(_tmpId,_tmpStudyDate,_tmpStudyType,_tmpItemId,_tmpResult,_tmpResponseTime,_tmpStudyMode);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<StudySession>> getStudySessionsByDateRange(final long startDate,
      final long endDate) {
    final String _sql = "SELECT * FROM study_sessions WHERE studyDate BETWEEN ? AND ? ORDER BY studyDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    return __db.getInvalidationTracker().createLiveData(new String[] {"study_sessions"}, false, new Callable<List<StudySession>>() {
      @Override
      @Nullable
      public List<StudySession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfStudyDate = CursorUtil.getColumnIndexOrThrow(_cursor, "studyDate");
          final int _cursorIndexOfStudyType = CursorUtil.getColumnIndexOrThrow(_cursor, "studyType");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "itemId");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfResponseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "responseTime");
          final int _cursorIndexOfStudyMode = CursorUtil.getColumnIndexOrThrow(_cursor, "studyMode");
          final List<StudySession> _result = new ArrayList<StudySession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StudySession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpStudyDate;
            _tmpStudyDate = _cursor.getLong(_cursorIndexOfStudyDate);
            final String _tmpStudyType;
            if (_cursor.isNull(_cursorIndexOfStudyType)) {
              _tmpStudyType = null;
            } else {
              _tmpStudyType = _cursor.getString(_cursorIndexOfStudyType);
            }
            final long _tmpItemId;
            _tmpItemId = _cursor.getLong(_cursorIndexOfItemId);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final long _tmpResponseTime;
            _tmpResponseTime = _cursor.getLong(_cursorIndexOfResponseTime);
            final String _tmpStudyMode;
            if (_cursor.isNull(_cursorIndexOfStudyMode)) {
              _tmpStudyMode = null;
            } else {
              _tmpStudyMode = _cursor.getString(_cursorIndexOfStudyMode);
            }
            _item = new StudySession(_tmpId,_tmpStudyDate,_tmpStudyType,_tmpItemId,_tmpResult,_tmpResponseTime,_tmpStudyMode);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<StudySession>> getStudySessionsByType(final String type) {
    final String _sql = "SELECT * FROM study_sessions WHERE studyType = ? ORDER BY studyDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"study_sessions"}, false, new Callable<List<StudySession>>() {
      @Override
      @Nullable
      public List<StudySession> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfStudyDate = CursorUtil.getColumnIndexOrThrow(_cursor, "studyDate");
          final int _cursorIndexOfStudyType = CursorUtil.getColumnIndexOrThrow(_cursor, "studyType");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "itemId");
          final int _cursorIndexOfResult = CursorUtil.getColumnIndexOrThrow(_cursor, "result");
          final int _cursorIndexOfResponseTime = CursorUtil.getColumnIndexOrThrow(_cursor, "responseTime");
          final int _cursorIndexOfStudyMode = CursorUtil.getColumnIndexOrThrow(_cursor, "studyMode");
          final List<StudySession> _result = new ArrayList<StudySession>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StudySession _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpStudyDate;
            _tmpStudyDate = _cursor.getLong(_cursorIndexOfStudyDate);
            final String _tmpStudyType;
            if (_cursor.isNull(_cursorIndexOfStudyType)) {
              _tmpStudyType = null;
            } else {
              _tmpStudyType = _cursor.getString(_cursorIndexOfStudyType);
            }
            final long _tmpItemId;
            _tmpItemId = _cursor.getLong(_cursorIndexOfItemId);
            final String _tmpResult;
            if (_cursor.isNull(_cursorIndexOfResult)) {
              _tmpResult = null;
            } else {
              _tmpResult = _cursor.getString(_cursorIndexOfResult);
            }
            final long _tmpResponseTime;
            _tmpResponseTime = _cursor.getLong(_cursorIndexOfResponseTime);
            final String _tmpStudyMode;
            if (_cursor.isNull(_cursorIndexOfStudyMode)) {
              _tmpStudyMode = null;
            } else {
              _tmpStudyMode = _cursor.getString(_cursorIndexOfStudyMode);
            }
            _item = new StudySession(_tmpId,_tmpStudyDate,_tmpStudyType,_tmpItemId,_tmpResult,_tmpResponseTime,_tmpStudyMode);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getStudyCountByDateRange(final long startDate, final long endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM study_sessions WHERE studyDate BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCorrectAnswersCount(final long startDate, final long endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM study_sessions WHERE result = 'correct' AND studyDate BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageResponseTime(final long startDate, final long endDate,
      final Continuation<? super Float> $completion) {
    final String _sql = "SELECT AVG(responseTime) FROM study_sessions WHERE studyDate BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @NonNull
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getFloat(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
