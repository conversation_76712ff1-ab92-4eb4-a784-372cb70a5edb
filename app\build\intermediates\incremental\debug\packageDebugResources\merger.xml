<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\src\main\res"><file name="difficulty_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\difficulty_background.xml" qualifiers="" type="drawable"/><file name="frequency_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\frequency_background.xml" qualifiers="" type="drawable"/><file name="ic_achievement_locked" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_achievement_locked.xml" qualifiers="" type="drawable"/><file name="ic_achievement_unlocked" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_achievement_unlocked.xml" qualifiers="" type="drawable"/><file name="ic_bookmark_border" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_bookmark_border.xml" qualifiers="" type="drawable"/><file name="ic_bookmark_filled" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_bookmark_filled.xml" qualifiers="" type="drawable"/><file name="ic_essay" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_essay.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_phrases" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_phrases.xml" qualifiers="" type="drawable"/><file name="ic_progress" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_progress.xml" qualifiers="" type="drawable"/><file name="ic_quiz" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_quiz.xml" qualifiers="" type="drawable"/><file name="ic_vocabulary" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_vocabulary.xml" qualifiers="" type="drawable"/><file name="part_of_speech_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\part_of_speech_background.xml" qualifiers="" type="drawable"/><file name="search_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="template_content_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\template_content_background.xml" qualifiers="" type="drawable"/><file name="type_argumentative_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\type_argumentative_background.xml" qualifiers="" type="drawable"/><file name="type_descriptive_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\type_descriptive_background.xml" qualifiers="" type="drawable"/><file name="type_narrative_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\type_narrative_background.xml" qualifiers="" type="drawable"/><file name="type_sentence_pattern_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\type_sentence_pattern_background.xml" qualifiers="" type="drawable"/><file name="type_transition_background" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\type_transition_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_essay" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_essay.xml" qualifiers="" type="layout"/><file name="fragment_phrases" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_phrases.xml" qualifiers="" type="layout"/><file name="fragment_progress" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_progress.xml" qualifiers="" type="layout"/><file name="fragment_quiz" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_quiz.xml" qualifiers="" type="layout"/><file name="fragment_vocabulary" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_vocabulary.xml" qualifiers="" type="layout"/><file name="item_achievement" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\item_achievement.xml" qualifiers="" type="layout"/><file name="item_essay_template" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\item_essay_template.xml" qualifiers="" type="layout"/><file name="item_phrase" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\item_phrase.xml" qualifiers="" type="layout"/><file name="item_word" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\item_word.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="D:\AndroidStudioProjects\word\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\AndroidStudioProjects\word\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="D:\AndroidStudioProjects\word\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="D:\AndroidStudioProjects\word\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="type_argumentative">#FF5722</color><color name="type_descriptive">#4CAF50</color><color name="type_narrative">#2196F3</color><color name="type_transition">#9C27B0</color><color name="type_sentence_pattern">#FF9800</color><color name="type_default">#607D8B</color></file><file path="D:\AndroidStudioProjects\word\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">CET-4词汇学习</string><string name="vocabulary">词汇</string><string name="phrases">短语</string><string name="essay">作文</string><string name="quiz">测验</string><string name="progress">进度</string><string name="search">搜索</string><string name="bookmark">收藏</string><string name="unbookmark">取消收藏</string><string name="difficulty">难度</string><string name="frequency">频率</string><string name="category">分类</string><string name="type">类型</string><string name="example">例句</string><string name="translation">翻译</string><string name="phonetic">音标</string><string name="part_of_speech">词性</string><string name="word_list">单词列表</string><string name="word_detail">单词详情</string><string name="study_word">学习单词</string><string name="review_words">复习单词</string><string name="bookmarked_words">收藏的单词</string><string name="word_count">单词数量</string><string name="accuracy_rate">正确率</string><string name="study_count">学习次数</string><string name="last_studied">最后学习</string><string name="phrase_list">短语列表</string><string name="phrase_detail">短语详情</string><string name="phrasal_verb">短语动词</string><string name="idiom">习语</string><string name="collocation">搭配</string><string name="essay_templates">作文模板</string><string name="argumentative">议论文</string><string name="descriptive">描述文</string><string name="narrative">记叙文</string><string name="transition_words">过渡词</string><string name="sentence_patterns">句型</string><string name="start_quiz">开始测验</string><string name="quiz_result">测验结果</string><string name="correct">正确</string><string name="incorrect">错误</string><string name="score">得分</string><string name="time_spent">用时</string><string name="study_progress">学习进度</string><string name="total_words_learned">已学单词</string><string name="total_phrases_learned">已学短语</string><string name="study_days">学习天数</string><string name="consecutive_days">连续天数</string><string name="total_study_time">总学习时间</string><string name="current_level">当前等级</string><string name="experience_points">经验值</string><string name="next">下一个</string><string name="previous">上一个</string><string name="finish">完成</string><string name="retry">重试</string><string name="skip">跳过</string><string name="submit">提交</string><string name="cancel">取消</string><string name="confirm">确认</string><string name="filter">筛选</string><string name="clear_filter">清除筛选</string><string name="sort">排序</string><string name="loading">加载中...</string><string name="no_data">暂无数据</string><string name="network_error">网络错误</string><string name="operation_success">操作成功</string><string name="operation_failed">操作失败</string></file><file path="D:\AndroidStudioProjects\word\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Word" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Word" parent="Base.Theme.Word"/></file><file path="D:\AndroidStudioProjects\word\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Word" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\AndroidStudioProjects\word\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\AndroidStudioProjects\word\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_calendar" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_level_1" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_1.xml" qualifiers="" type="drawable"/><file name="ic_level_2" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_2.xml" qualifiers="" type="drawable"/><file name="ic_level_3" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_3.xml" qualifiers="" type="drawable"/><file name="ic_level_4" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_4.xml" qualifiers="" type="drawable"/><file name="ic_level_5" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_5.xml" qualifiers="" type="drawable"/><file name="ic_level_master" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_level_master.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_streak" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_streak.xml" qualifiers="" type="drawable"/><file name="ic_volume_up" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_volume_up.xml" qualifiers="" type="drawable"/><file name="fragment_settings" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="bg_tag" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\bg_tag.xml" qualifiers="" type="drawable"/><file name="ic_difficulty" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_difficulty.xml" qualifiers="" type="drawable"/><file name="ic_frequency" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_frequency.xml" qualifiers="" type="drawable"/><file name="ic_home" path="D:\AndroidStudioProjects\word\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="fragment_home" path="D:\AndroidStudioProjects\word\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\AndroidStudioProjects\word\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>