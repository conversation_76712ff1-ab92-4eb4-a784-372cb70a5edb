package com.example.word.ui.progress

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.example.word.data.database.WordDatabase
import com.example.word.data.entities.UserProgress
import com.example.word.data.repository.WordRepository
import com.example.word.utils.GameificationManager
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 学习进度ViewModel
 */
class ProgressViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: WordRepository
    
    // 用户进度
    val userProgress: LiveData<UserProgress?>
    
    // 学习统计
    private val _studyStatistics = MutableLiveData<StudyStatistics>()
    val studyStatistics: LiveData<StudyStatistics> = _studyStatistics
    
    // 学习历史
    private val _studyHistory = MutableLiveData<List<DailyStudyRecord>>()
    val studyHistory: LiveData<List<DailyStudyRecord>> = _studyHistory
    
    // 词汇掌握情况
    private val _vocabularyMastery = MutableLiveData<VocabularyMastery>()
    val vocabularyMastery: LiveData<VocabularyMastery> = _vocabularyMastery
    
    // 学习成就
    private val _achievements = MutableLiveData<List<GameificationManager.Achievement>>()
    val achievements: LiveData<List<GameificationManager.Achievement>> = _achievements
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    init {
        val database = WordDatabase.getDatabase(application)
        repository = WordRepository(
            database.wordDao(),
            database.phraseDao(),
            database.essayTemplateDao(),
            database.studySessionDao(),
            database.userProgressDao()
        )
        
        userProgress = repository.getUserProgress()
        
        loadStudyStatistics()
        loadStudyHistory()
        loadVocabularyMastery()
        loadAchievements()
    }
    
    /**
     * 加载学习统计
     */
    private fun loadStudyStatistics() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                val progress = repository.getUserProgressSync()
                val studiedWordsCount = repository.getStudiedWordsCount()
                val bookmarkedWordsCount = repository.getBookmarkedWordsCount()
                val averageAccuracy = repository.getWordsAverageAccuracy()
                
                // 计算今日学习情况
                val today = getTodayStartTime()
                val tomorrow = today + 24 * 60 * 60 * 1000
                val todayStudyCount = repository.getStudyCountByDateRange(today, tomorrow)
                val todayCorrectCount = repository.getCorrectAnswersCount(today, tomorrow)
                val todayAccuracy = if (todayStudyCount > 0) (todayCorrectCount * 100 / todayStudyCount) else 0
                
                // 计算本周学习情况
                val weekStart = getWeekStartTime()
                val weekStudyCount = repository.getStudyCountByDateRange(weekStart, System.currentTimeMillis())
                
                val statistics = StudyStatistics(
                    totalStudyDays = progress?.studyDays ?: 0,
                    consecutiveStudyDays = progress?.consecutiveDays ?: 0,
                    totalStudyTime = progress?.totalStudyTime ?: 0,
                    studiedWordsCount = studiedWordsCount,
                    bookmarkedWordsCount = bookmarkedWordsCount,
                    averageAccuracy = averageAccuracy,
                    todayStudyCount = todayStudyCount,
                    todayAccuracy = todayAccuracy,
                    weekStudyCount = weekStudyCount,
                    currentLevel = progress?.currentLevel ?: 1,
                    experiencePoints = progress?.experiencePoints ?: 0,
                    nextLevelExp = calculateNextLevelExp(progress?.currentLevel ?: 1)
                )
                
                _studyStatistics.value = statistics
                
            } catch (e: Exception) {
                _errorMessage.value = "加载学习统计失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 加载学习历史
     */
    private fun loadStudyHistory() {
        viewModelScope.launch {
            try {
                val history = mutableListOf<DailyStudyRecord>()
                val calendar = Calendar.getInstance()
                
                // 获取最近30天的学习记录
                for (i in 0 until 30) {
                    val dayStart = getDayStartTime(calendar.time)
                    val dayEnd = dayStart + 24 * 60 * 60 * 1000
                    
                    val studyCount = repository.getStudyCountByDateRange(dayStart, dayEnd)
                    val correctCount = repository.getCorrectAnswersCount(dayStart, dayEnd)
                    val accuracy = if (studyCount > 0) (correctCount * 100 / studyCount) else 0
                    
                    history.add(
                        DailyStudyRecord(
                            date = calendar.time,
                            studyCount = studyCount,
                            correctCount = correctCount,
                            accuracy = accuracy
                        )
                    )
                    
                    calendar.add(Calendar.DAY_OF_MONTH, -1)
                }
                
                _studyHistory.value = history.reversed()
                
            } catch (e: Exception) {
                _errorMessage.value = "加载学习历史失败: ${e.message}"
            }
        }
    }
    
    /**
     * 加载词汇掌握情况
     */
    private fun loadVocabularyMastery() {
        viewModelScope.launch {
            try {
                val totalWords = 4500 // CET-4总词汇量
                val studiedWords = repository.getStudiedWordsCount()
                val masteredWords = studiedWords // 简化计算，实际可以根据正确率等条件判断
                
                // 按难度等级统计
                val difficultyStats = mutableMapOf<Int, DifficultyStats>()
                for (level in 1..5) {
                    // 这里简化处理，实际应该查询数据库
                    val totalInLevel = totalWords / 5 // 假设每个难度等级平均分配
                    val studiedInLevel = studiedWords / 5 // 简化计算
                    difficultyStats[level] = DifficultyStats(
                        level = level,
                        totalWords = totalInLevel,
                        studiedWords = studiedInLevel,
                        masteredWords = studiedInLevel * 80 / 100 // 假设80%掌握
                    )
                }
                
                val mastery = VocabularyMastery(
                    totalWords = totalWords,
                    studiedWords = studiedWords,
                    masteredWords = masteredWords,
                    masteryRate = if (totalWords > 0) (masteredWords * 100 / totalWords) else 0,
                    difficultyStats = difficultyStats
                )
                
                _vocabularyMastery.value = mastery
                
            } catch (e: Exception) {
                _errorMessage.value = "加载词汇掌握情况失败: ${e.message}"
            }
        }
    }
    
    /**
     * 加载学习成就
     */
    private fun loadAchievements() {
        viewModelScope.launch {
            try {
                val progress = repository.getUserProgressSync()
                val studiedWords = repository.getStudiedWordsCount()
                val achievements = mutableListOf<GameificationManager.Achievement>()

                // 学习天数成就
                achievements.addAll(GameificationManager.createStudyDaysAchievements(progress?.studyDays ?: 0))

                // 词汇量成就
                achievements.addAll(GameificationManager.createVocabularyAchievements(studiedWords))

                // 连续学习成就
                achievements.addAll(GameificationManager.createConsecutiveAchievements(progress?.consecutiveDays ?: 0))

                // 经验值成就
                achievements.addAll(GameificationManager.createExperienceAchievements(progress?.experiencePoints ?: 0))
                
                _achievements.value = achievements.sortedByDescending { it.isUnlocked }
                
            } catch (e: Exception) {
                _errorMessage.value = "加载学习成就失败: ${e.message}"
            }
        }
    }
    

    
    /**
     * 计算下一等级所需经验值
     */
    private fun calculateNextLevelExp(currentLevel: Int): Int {
        return currentLevel * 1000 // 简化计算，每级需要1000经验值
    }
    
    /**
     * 获取今天开始时间
     */
    private fun getTodayStartTime(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 获取本周开始时间
     */
    private fun getWeekStartTime(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 获取指定日期的开始时间
     */
    private fun getDayStartTime(date: Date): Long {
        val calendar = Calendar.getInstance()
        calendar.time = date
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 清除错误信息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}

// 数据类定义
data class StudyStatistics(
    val totalStudyDays: Int,
    val consecutiveStudyDays: Int,
    val totalStudyTime: Long,
    val studiedWordsCount: Int,
    val bookmarkedWordsCount: Int,
    val averageAccuracy: Float,
    val todayStudyCount: Int,
    val todayAccuracy: Int,
    val weekStudyCount: Int,
    val currentLevel: Int,
    val experiencePoints: Int,
    val nextLevelExp: Int
)

data class DailyStudyRecord(
    val date: Date,
    val studyCount: Int,
    val correctCount: Int,
    val accuracy: Int
)

data class VocabularyMastery(
    val totalWords: Int,
    val studiedWords: Int,
    val masteredWords: Int,
    val masteryRate: Int,
    val difficultyStats: Map<Int, DifficultyStats>
)

data class DifficultyStats(
    val level: Int,
    val totalWords: Int,
    val studiedWords: Int,
    val masteredWords: Int
)


