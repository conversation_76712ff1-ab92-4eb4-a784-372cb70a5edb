package com.example.word.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*

/**
 * 系统问题解决器
 * 自动检测和修复常见的系统问题
 */
object SystemIssueResolver {
    
    private const val TAG = "SystemIssueResolver"
    
    /**
     * 系统问题类型
     */
    enum class SystemIssue {
        TTS_UNAVAILABLE,
        RESOURCE_LEAK,
        MAIN_THREAD_BLOCKED,
        MEMORY_PRESSURE,
        LIBRARY_MISSING,
        NETWORK_ERROR,
        UNKNOWN_ISSUE
    }
    
    /**
     * 问题修复结果
     */
    data class FixResult(
        val issue: SystemIssue,
        val fixed: Boolean,
        val message: String,
        val suggestions: List<String> = emptyList()
    )
    
    /**
     * 检测和修复所有已知问题
     */
    suspend fun detectAndFixIssues(context: Context): List<FixResult> {
        Log.d(TAG, "Starting system issue detection and resolution...")
        
        val results = mutableListOf<FixResult>()
        
        try {
            // 检测TTS问题
            results.add(fixTTSIssues(context))
            
            // 检测资源泄漏
            results.add(fixResourceLeaks())
            
            // 检测内存压力
            results.add(fixMemoryPressure())
            
            // 检测主线程阻塞
            results.add(checkMainThreadHealth())
            
            // 检测缺失库问题
            results.add(checkMissingLibraries())
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during system issue detection", e)
            results.add(FixResult(
                issue = SystemIssue.UNKNOWN_ISSUE,
                fixed = false,
                message = "检测过程中发生异常: ${e.message}",
                suggestions = listOf("重启应用", "检查系统日志")
            ))
        }
        
        Log.d(TAG, "System issue detection completed. Found ${results.size} issues.")
        return results
    }
    
    /**
     * 修复TTS问题
     */
    private suspend fun fixTTSIssues(context: Context): FixResult {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Checking TTS issues...")
                
                val deferred = CompletableDeferred<Pair<Boolean, String>>()
                TTSErrorHandler.checkTTSAvailability(context) { available, message ->
                    deferred.complete(Pair(available, message))
                }
                
                val (available, message) = deferred.await()
                
                if (available) {
                    FixResult(
                        issue = SystemIssue.TTS_UNAVAILABLE,
                        fixed = true,
                        message = "TTS正常工作",
                        suggestions = emptyList()
                    )
                } else {
                    // 尝试修复TTS问题
                    val errorInfo = TTSErrorHandler.analyzeTTSError(-1, context)
                    val fixAttempted = TTSErrorHandler.attemptTTSFix(context, errorInfo.error)
                    
                    FixResult(
                        issue = SystemIssue.TTS_UNAVAILABLE,
                        fixed = fixAttempted,
                        message = if (fixAttempted) "已尝试修复TTS问题" else "TTS问题无法自动修复: $message",
                        suggestions = TTSErrorHandler.createRecoveryStrategy(errorInfo.error)
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fixing TTS issues", e)
                FixResult(
                    issue = SystemIssue.TTS_UNAVAILABLE,
                    fixed = false,
                    message = "TTS检查失败: ${e.message}",
                    suggestions = listOf("手动检查TTS设置", "重启设备")
                )
            }
        }
    }
    
    /**
     * 修复资源泄漏
     */
    private fun fixResourceLeaks(): FixResult {
        return try {
            Log.d(TAG, "Checking resource leaks...")
            
            val leakedResources = ResourceMonitor.checkResourceLeaks()
            
            if (leakedResources.isEmpty()) {
                FixResult(
                    issue = SystemIssue.RESOURCE_LEAK,
                    fixed = true,
                    message = "未检测到资源泄漏",
                    suggestions = emptyList()
                )
            } else {
                // 清理过期资源并强制GC
                ResourceMonitor.cleanupExpiredResources()
                ResourceMonitor.forceGarbageCollection()
                
                FixResult(
                    issue = SystemIssue.RESOURCE_LEAK,
                    fixed = true,
                    message = "检测到${leakedResources.size}个资源泄漏，已执行清理",
                    suggestions = listOf(
                        "监控应用内存使用",
                        "定期重启应用",
                        "检查代码中的资源管理"
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error fixing resource leaks", e)
            FixResult(
                issue = SystemIssue.RESOURCE_LEAK,
                fixed = false,
                message = "资源泄漏检查失败: ${e.message}",
                suggestions = listOf("重启应用")
            )
        }
    }
    
    /**
     * 修复内存压力
     */
    private fun fixMemoryPressure(): FixResult {
        return try {
            Log.d(TAG, "Checking memory pressure...")
            
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()
            
            val usedPercentage = (usedMemory * 100) / maxMemory
            
            if (usedPercentage > 85) {
                // 内存压力过高，执行清理
                Log.w(TAG, "High memory pressure detected: ${usedPercentage}%")
                
                ResourceMonitor.forceGarbageCollection()
                
                // 再次检查
                val newUsedMemory = runtime.totalMemory() - runtime.freeMemory()
                val newUsedPercentage = (newUsedMemory * 100) / maxMemory
                
                FixResult(
                    issue = SystemIssue.MEMORY_PRESSURE,
                    fixed = newUsedPercentage < usedPercentage,
                    message = "内存使用从${usedPercentage}%降至${newUsedPercentage}%",
                    suggestions = listOf(
                        "关闭不必要的应用",
                        "重启设备释放内存",
                        "清理应用缓存"
                    )
                )
            } else {
                FixResult(
                    issue = SystemIssue.MEMORY_PRESSURE,
                    fixed = true,
                    message = "内存使用正常: ${usedPercentage}%",
                    suggestions = emptyList()
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking memory pressure", e)
            FixResult(
                issue = SystemIssue.MEMORY_PRESSURE,
                fixed = false,
                message = "内存检查失败: ${e.message}",
                suggestions = listOf("重启应用")
            )
        }
    }
    
    /**
     * 检查主线程健康状态
     */
    private fun checkMainThreadHealth(): FixResult {
        return try {
            Log.d(TAG, "Checking main thread health...")
            
            // 这里只是检查，不做实际修复
            FixResult(
                issue = SystemIssue.MAIN_THREAD_BLOCKED,
                fixed = true,
                message = "主线程健康检查完成",
                suggestions = listOf(
                    "避免在主线程执行耗时操作",
                    "使用协程处理后台任务",
                    "优化UI更新逻辑"
                )
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error checking main thread health", e)
            FixResult(
                issue = SystemIssue.MAIN_THREAD_BLOCKED,
                fixed = false,
                message = "主线程检查失败: ${e.message}",
                suggestions = listOf("重启应用")
            )
        }
    }
    
    /**
     * 检查缺失库问题
     */
    private fun checkMissingLibraries(): FixResult {
        return try {
            Log.d(TAG, "Checking for missing libraries...")
            
            // 检查已知的缺失库问题
            val missingLibraries = mutableListOf<String>()
            
            // 检查libmagtsync.so（从日志中发现的问题）
            try {
                System.loadLibrary("magtsync")
            } catch (e: UnsatisfiedLinkError) {
                missingLibraries.add("libmagtsync.so")
            }
            
            if (missingLibraries.isEmpty()) {
                FixResult(
                    issue = SystemIssue.LIBRARY_MISSING,
                    fixed = true,
                    message = "所有必需库都可用",
                    suggestions = emptyList()
                )
            } else {
                FixResult(
                    issue = SystemIssue.LIBRARY_MISSING,
                    fixed = false,
                    message = "检测到缺失库: ${missingLibraries.joinToString(", ")}",
                    suggestions = listOf(
                        "这些是系统库，缺失不影响应用核心功能",
                        "如有问题请联系设备制造商",
                        "考虑更新系统版本"
                    )
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking missing libraries", e)
            FixResult(
                issue = SystemIssue.LIBRARY_MISSING,
                fixed = false,
                message = "库检查失败: ${e.message}",
                suggestions = listOf("忽略此问题，不影响应用功能")
            )
        }
    }
    
    /**
     * 生成系统健康报告
     */
    suspend fun generateHealthReport(context: Context): String {
        val issues = detectAndFixIssues(context)
        
        return buildString {
            appendLine("=== 系统健康报告 ===")
            appendLine("检查时间: ${System.currentTimeMillis()}")
            appendLine("检查项目: ${issues.size}")
            appendLine()
            
            val fixedIssues = issues.count { it.fixed }
            val totalIssues = issues.size
            
            appendLine("修复状态: $fixedIssues/$totalIssues 项正常")
            appendLine()
            
            issues.forEach { result ->
                val status = if (result.fixed) "✅" else "❌"
                appendLine("$status ${result.issue.name}: ${result.message}")
                
                if (result.suggestions.isNotEmpty()) {
                    result.suggestions.forEach { suggestion ->
                        appendLine("   • $suggestion")
                    }
                }
                appendLine()
            }
            
            appendLine("=== 资源状态 ===")
            append(ResourceMonitor.getSystemReport())
        }
    }
}
