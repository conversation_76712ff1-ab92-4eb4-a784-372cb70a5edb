package com.example.word.data.database;

/**
 * CET-4词汇学习应用数据库
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\'\u0018\u0000 \r2\u00020\u0001:\u0002\r\u000eB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&\u00a8\u0006\u000f"}, d2 = {"Lcom/example/word/data/database/WordDatabase;", "Landroidx/room/RoomDatabase;", "()V", "essayTemplateDao", "Lcom/example/word/data/dao/EssayTemplateDao;", "phraseDao", "Lcom/example/word/data/dao/PhraseDao;", "studySessionDao", "Lcom/example/word/data/dao/StudySessionDao;", "userProgressDao", "Lcom/example/word/data/dao/UserProgressDao;", "wordDao", "Lcom/example/word/data/dao/WordDao;", "Companion", "WordDatabaseCallback", "app_debug"})
@androidx.room.Database(entities = {com.example.word.data.entities.Word.class, com.example.word.data.entities.Phrase.class, com.example.word.data.entities.EssayTemplate.class, com.example.word.data.entities.StudySession.class, com.example.word.data.entities.UserProgress.class}, version = 1, exportSchema = false)
public abstract class WordDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "WordDatabase";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.word.data.database.WordDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.data.database.WordDatabase.Companion Companion = null;
    
    public WordDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.word.data.dao.WordDao wordDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.word.data.dao.PhraseDao phraseDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.word.data.dao.EssayTemplateDao essayTemplateDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.word.data.dao.StudySessionDao studySessionDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.word.data.dao.UserProgressDao userProgressDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000bR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/word/data/database/WordDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/example/word/data/database/WordDatabase;", "TAG", "", "clearInstance", "", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.data.database.WordDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
        
        /**
         * 清除数据库实例（用于测试或重置）
         */
        public final void clearInstance() {
        }
    }
    
    /**
     * 数据库回调，用于预填充数据
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\b"}, d2 = {"Lcom/example/word/data/database/WordDatabase$WordDatabaseCallback;", "Landroidx/room/RoomDatabase$Callback;", "()V", "onCreate", "", "db", "Landroidx/sqlite/db/SupportSQLiteDatabase;", "onOpen", "app_debug"})
    static final class WordDatabaseCallback extends androidx.room.RoomDatabase.Callback {
        
        public WordDatabaseCallback() {
            super();
        }
        
        @java.lang.Override()
        public void onCreate(@org.jetbrains.annotations.NotNull()
        androidx.sqlite.db.SupportSQLiteDatabase db) {
        }
        
        @java.lang.Override()
        public void onOpen(@org.jetbrains.annotations.NotNull()
        androidx.sqlite.db.SupportSQLiteDatabase db) {
        }
    }
}