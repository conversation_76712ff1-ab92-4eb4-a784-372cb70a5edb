package com.example.word.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.word.data.entities.UserProgress;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserProgressDao_Impl implements UserProgressDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserProgress> __insertionAdapterOfUserProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStudyStats;

  private final SharedSQLiteStatement __preparedStmtOfAddStudyTime;

  private final SharedSQLiteStatement __preparedStmtOfAddExperience;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLevelAndExperience;

  private final SharedSQLiteStatement __preparedStmtOfUpdateQuizStats;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAchievements;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLearningPreferences;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAppSettings;

  private final SharedSQLiteStatement __preparedStmtOfClearUserProgress;

  public UserProgressDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserProgress = new EntityInsertionAdapter<UserProgress>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_progress` (`id`,`totalWordsLearned`,`totalPhrasesLearned`,`totalStudyTime`,`studyDays`,`consecutiveDays`,`lastStudyDate`,`currentLevel`,`experiencePoints`,`totalExperience`,`totalQuizzes`,`correctAnswers`,`totalAnswers`,`averageAccuracy`,`unlockedAchievements`,`totalAchievements`,`dailyGoal`,`reminderEnabled`,`reminderTime`,`autoPlayPronunciation`,`slowPronunciation`,`themeMode`,`showDifficulty`,`showFrequency`,`showStreak`,`autoBackup`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserProgress entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTotalWordsLearned());
        statement.bindLong(3, entity.getTotalPhrasesLearned());
        statement.bindLong(4, entity.getTotalStudyTime());
        statement.bindLong(5, entity.getStudyDays());
        statement.bindLong(6, entity.getConsecutiveDays());
        statement.bindLong(7, entity.getLastStudyDate());
        statement.bindLong(8, entity.getCurrentLevel());
        statement.bindLong(9, entity.getExperiencePoints());
        statement.bindLong(10, entity.getTotalExperience());
        statement.bindLong(11, entity.getTotalQuizzes());
        statement.bindLong(12, entity.getCorrectAnswers());
        statement.bindLong(13, entity.getTotalAnswers());
        statement.bindDouble(14, entity.getAverageAccuracy());
        if (entity.getUnlockedAchievements() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getUnlockedAchievements());
        }
        statement.bindLong(16, entity.getTotalAchievements());
        statement.bindLong(17, entity.getDailyGoal());
        final int _tmp = entity.getReminderEnabled() ? 1 : 0;
        statement.bindLong(18, _tmp);
        if (entity.getReminderTime() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getReminderTime());
        }
        final int _tmp_1 = entity.getAutoPlayPronunciation() ? 1 : 0;
        statement.bindLong(20, _tmp_1);
        final int _tmp_2 = entity.getSlowPronunciation() ? 1 : 0;
        statement.bindLong(21, _tmp_2);
        if (entity.getThemeMode() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getThemeMode());
        }
        final int _tmp_3 = entity.getShowDifficulty() ? 1 : 0;
        statement.bindLong(23, _tmp_3);
        final int _tmp_4 = entity.getShowFrequency() ? 1 : 0;
        statement.bindLong(24, _tmp_4);
        final int _tmp_5 = entity.getShowStreak() ? 1 : 0;
        statement.bindLong(25, _tmp_5);
        final int _tmp_6 = entity.getAutoBackup() ? 1 : 0;
        statement.bindLong(26, _tmp_6);
        statement.bindLong(27, entity.getCreatedAt());
        statement.bindLong(28, entity.getUpdatedAt());
      }
    };
    this.__preparedStmtOfUpdateStudyStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress\n"
                + "        SET totalWordsLearned = ?,\n"
                + "            totalPhrasesLearned = ?,\n"
                + "            totalStudyTime = ?,\n"
                + "            studyDays = ?,\n"
                + "            consecutiveDays = ?,\n"
                + "            lastStudyDate = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfAddStudyTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress\n"
                + "        SET totalStudyTime = totalStudyTime + ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfAddExperience = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress\n"
                + "        SET experiencePoints = experiencePoints + ?,\n"
                + "            totalExperience = totalExperience + ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLevelAndExperience = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress \n"
                + "        SET currentLevel = ?,\n"
                + "            experiencePoints = ?,\n"
                + "            totalExperience = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateQuizStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress \n"
                + "        SET totalQuizzes = ?,\n"
                + "            correctAnswers = ?,\n"
                + "            totalAnswers = ?,\n"
                + "            averageAccuracy = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAchievements = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress \n"
                + "        SET unlockedAchievements = ?,\n"
                + "            totalAchievements = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLearningPreferences = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress \n"
                + "        SET dailyGoal = ?,\n"
                + "            reminderEnabled = ?,\n"
                + "            reminderTime = ?,\n"
                + "            autoPlayPronunciation = ?,\n"
                + "            slowPronunciation = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAppSettings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE user_progress \n"
                + "        SET themeMode = ?,\n"
                + "            showDifficulty = ?,\n"
                + "            showFrequency = ?,\n"
                + "            showStreak = ?,\n"
                + "            autoBackup = ?,\n"
                + "            updatedAt = ?\n"
                + "        WHERE id = 1\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfClearUserProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM user_progress";
        return _query;
      }
    };
  }

  @Override
  public Object insertOrUpdateUserProgress(final UserProgress userProgress,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserProgress.insert(userProgress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateStudyStats(final int wordsLearned, final int phrasesLearned,
      final long studyTime, final int studyDays, final int consecutiveDays,
      final long lastStudyDate, final long updatedAt, final Continuation<? super Unit> arg7) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStudyStats.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, wordsLearned);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, phrasesLearned);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, studyTime);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, studyDays);
        _argIndex = 5;
        _stmt.bindLong(_argIndex, consecutiveDays);
        _argIndex = 6;
        _stmt.bindLong(_argIndex, lastStudyDate);
        _argIndex = 7;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateStudyStats.release(_stmt);
        }
      }
    }, arg7);
  }

  @Override
  public Object addStudyTime(final long additionalTime, final long updatedAt,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddStudyTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, additionalTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddStudyTime.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object addExperience(final int experience, final long updatedAt,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddExperience.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, experience);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, experience);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddExperience.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object updateLevelAndExperience(final int level, final int experience,
      final int totalExperience, final long updatedAt, final Continuation<? super Unit> arg4) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLevelAndExperience.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, level);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, experience);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, totalExperience);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLevelAndExperience.release(_stmt);
        }
      }
    }, arg4);
  }

  @Override
  public Object updateQuizStats(final int totalQuizzes, final int correctAnswers,
      final int totalAnswers, final float averageAccuracy, final long updatedAt,
      final Continuation<? super Unit> arg5) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateQuizStats.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, totalQuizzes);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, correctAnswers);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, totalAnswers);
        _argIndex = 4;
        _stmt.bindDouble(_argIndex, averageAccuracy);
        _argIndex = 5;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateQuizStats.release(_stmt);
        }
      }
    }, arg5);
  }

  @Override
  public Object updateAchievements(final String achievements, final int totalAchievements,
      final long updatedAt, final Continuation<? super Unit> arg3) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAchievements.acquire();
        int _argIndex = 1;
        if (achievements == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, achievements);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, totalAchievements);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAchievements.release(_stmt);
        }
      }
    }, arg3);
  }

  @Override
  public Object updateLearningPreferences(final int dailyGoal, final boolean reminderEnabled,
      final String reminderTime, final boolean autoPlay, final boolean slowPronunciation,
      final long updatedAt, final Continuation<? super Unit> arg6) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLearningPreferences.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, dailyGoal);
        _argIndex = 2;
        final int _tmp = reminderEnabled ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 3;
        if (reminderTime == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, reminderTime);
        }
        _argIndex = 4;
        final int _tmp_1 = autoPlay ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp_1);
        _argIndex = 5;
        final int _tmp_2 = slowPronunciation ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp_2);
        _argIndex = 6;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLearningPreferences.release(_stmt);
        }
      }
    }, arg6);
  }

  @Override
  public Object updateAppSettings(final String themeMode, final boolean showDifficulty,
      final boolean showFrequency, final boolean showStreak, final boolean autoBackup,
      final long updatedAt, final Continuation<? super Unit> arg6) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAppSettings.acquire();
        int _argIndex = 1;
        if (themeMode == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, themeMode);
        }
        _argIndex = 2;
        final int _tmp = showDifficulty ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 3;
        final int _tmp_1 = showFrequency ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp_1);
        _argIndex = 4;
        final int _tmp_2 = showStreak ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp_2);
        _argIndex = 5;
        final int _tmp_3 = autoBackup ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp_3);
        _argIndex = 6;
        _stmt.bindLong(_argIndex, updatedAt);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAppSettings.release(_stmt);
        }
      }
    }, arg6);
  }

  @Override
  public Object clearUserProgress(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearUserProgress.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearUserProgress.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public LiveData<UserProgress> getUserProgress() {
    final String _sql = "SELECT * FROM user_progress WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"user_progress"}, false, new Callable<UserProgress>() {
      @Override
      @Nullable
      public UserProgress call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTotalWordsLearned = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWordsLearned");
          final int _cursorIndexOfTotalPhrasesLearned = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPhrasesLearned");
          final int _cursorIndexOfTotalStudyTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalStudyTime");
          final int _cursorIndexOfStudyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "studyDays");
          final int _cursorIndexOfConsecutiveDays = CursorUtil.getColumnIndexOrThrow(_cursor, "consecutiveDays");
          final int _cursorIndexOfLastStudyDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudyDate");
          final int _cursorIndexOfCurrentLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "currentLevel");
          final int _cursorIndexOfExperiencePoints = CursorUtil.getColumnIndexOrThrow(_cursor, "experiencePoints");
          final int _cursorIndexOfTotalExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExperience");
          final int _cursorIndexOfTotalQuizzes = CursorUtil.getColumnIndexOrThrow(_cursor, "totalQuizzes");
          final int _cursorIndexOfCorrectAnswers = CursorUtil.getColumnIndexOrThrow(_cursor, "correctAnswers");
          final int _cursorIndexOfTotalAnswers = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAnswers");
          final int _cursorIndexOfAverageAccuracy = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAccuracy");
          final int _cursorIndexOfUnlockedAchievements = CursorUtil.getColumnIndexOrThrow(_cursor, "unlockedAchievements");
          final int _cursorIndexOfTotalAchievements = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAchievements");
          final int _cursorIndexOfDailyGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "dailyGoal");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfAutoPlayPronunciation = CursorUtil.getColumnIndexOrThrow(_cursor, "autoPlayPronunciation");
          final int _cursorIndexOfSlowPronunciation = CursorUtil.getColumnIndexOrThrow(_cursor, "slowPronunciation");
          final int _cursorIndexOfThemeMode = CursorUtil.getColumnIndexOrThrow(_cursor, "themeMode");
          final int _cursorIndexOfShowDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "showDifficulty");
          final int _cursorIndexOfShowFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "showFrequency");
          final int _cursorIndexOfShowStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "showStreak");
          final int _cursorIndexOfAutoBackup = CursorUtil.getColumnIndexOrThrow(_cursor, "autoBackup");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final UserProgress _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpTotalWordsLearned;
            _tmpTotalWordsLearned = _cursor.getInt(_cursorIndexOfTotalWordsLearned);
            final int _tmpTotalPhrasesLearned;
            _tmpTotalPhrasesLearned = _cursor.getInt(_cursorIndexOfTotalPhrasesLearned);
            final long _tmpTotalStudyTime;
            _tmpTotalStudyTime = _cursor.getLong(_cursorIndexOfTotalStudyTime);
            final int _tmpStudyDays;
            _tmpStudyDays = _cursor.getInt(_cursorIndexOfStudyDays);
            final int _tmpConsecutiveDays;
            _tmpConsecutiveDays = _cursor.getInt(_cursorIndexOfConsecutiveDays);
            final long _tmpLastStudyDate;
            _tmpLastStudyDate = _cursor.getLong(_cursorIndexOfLastStudyDate);
            final int _tmpCurrentLevel;
            _tmpCurrentLevel = _cursor.getInt(_cursorIndexOfCurrentLevel);
            final int _tmpExperiencePoints;
            _tmpExperiencePoints = _cursor.getInt(_cursorIndexOfExperiencePoints);
            final int _tmpTotalExperience;
            _tmpTotalExperience = _cursor.getInt(_cursorIndexOfTotalExperience);
            final int _tmpTotalQuizzes;
            _tmpTotalQuizzes = _cursor.getInt(_cursorIndexOfTotalQuizzes);
            final int _tmpCorrectAnswers;
            _tmpCorrectAnswers = _cursor.getInt(_cursorIndexOfCorrectAnswers);
            final int _tmpTotalAnswers;
            _tmpTotalAnswers = _cursor.getInt(_cursorIndexOfTotalAnswers);
            final float _tmpAverageAccuracy;
            _tmpAverageAccuracy = _cursor.getFloat(_cursorIndexOfAverageAccuracy);
            final String _tmpUnlockedAchievements;
            if (_cursor.isNull(_cursorIndexOfUnlockedAchievements)) {
              _tmpUnlockedAchievements = null;
            } else {
              _tmpUnlockedAchievements = _cursor.getString(_cursorIndexOfUnlockedAchievements);
            }
            final int _tmpTotalAchievements;
            _tmpTotalAchievements = _cursor.getInt(_cursorIndexOfTotalAchievements);
            final int _tmpDailyGoal;
            _tmpDailyGoal = _cursor.getInt(_cursorIndexOfDailyGoal);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpAutoPlayPronunciation;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfAutoPlayPronunciation);
            _tmpAutoPlayPronunciation = _tmp_1 != 0;
            final boolean _tmpSlowPronunciation;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfSlowPronunciation);
            _tmpSlowPronunciation = _tmp_2 != 0;
            final String _tmpThemeMode;
            if (_cursor.isNull(_cursorIndexOfThemeMode)) {
              _tmpThemeMode = null;
            } else {
              _tmpThemeMode = _cursor.getString(_cursorIndexOfThemeMode);
            }
            final boolean _tmpShowDifficulty;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowDifficulty);
            _tmpShowDifficulty = _tmp_3 != 0;
            final boolean _tmpShowFrequency;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFrequency);
            _tmpShowFrequency = _tmp_4 != 0;
            final boolean _tmpShowStreak;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfShowStreak);
            _tmpShowStreak = _tmp_5 != 0;
            final boolean _tmpAutoBackup;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfAutoBackup);
            _tmpAutoBackup = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new UserProgress(_tmpId,_tmpTotalWordsLearned,_tmpTotalPhrasesLearned,_tmpTotalStudyTime,_tmpStudyDays,_tmpConsecutiveDays,_tmpLastStudyDate,_tmpCurrentLevel,_tmpExperiencePoints,_tmpTotalExperience,_tmpTotalQuizzes,_tmpCorrectAnswers,_tmpTotalAnswers,_tmpAverageAccuracy,_tmpUnlockedAchievements,_tmpTotalAchievements,_tmpDailyGoal,_tmpReminderEnabled,_tmpReminderTime,_tmpAutoPlayPronunciation,_tmpSlowPronunciation,_tmpThemeMode,_tmpShowDifficulty,_tmpShowFrequency,_tmpShowStreak,_tmpAutoBackup,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserProgressSync(final Continuation<? super UserProgress> arg0) {
    final String _sql = "SELECT * FROM user_progress WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserProgress>() {
      @Override
      @Nullable
      public UserProgress call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTotalWordsLearned = CursorUtil.getColumnIndexOrThrow(_cursor, "totalWordsLearned");
          final int _cursorIndexOfTotalPhrasesLearned = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPhrasesLearned");
          final int _cursorIndexOfTotalStudyTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalStudyTime");
          final int _cursorIndexOfStudyDays = CursorUtil.getColumnIndexOrThrow(_cursor, "studyDays");
          final int _cursorIndexOfConsecutiveDays = CursorUtil.getColumnIndexOrThrow(_cursor, "consecutiveDays");
          final int _cursorIndexOfLastStudyDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudyDate");
          final int _cursorIndexOfCurrentLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "currentLevel");
          final int _cursorIndexOfExperiencePoints = CursorUtil.getColumnIndexOrThrow(_cursor, "experiencePoints");
          final int _cursorIndexOfTotalExperience = CursorUtil.getColumnIndexOrThrow(_cursor, "totalExperience");
          final int _cursorIndexOfTotalQuizzes = CursorUtil.getColumnIndexOrThrow(_cursor, "totalQuizzes");
          final int _cursorIndexOfCorrectAnswers = CursorUtil.getColumnIndexOrThrow(_cursor, "correctAnswers");
          final int _cursorIndexOfTotalAnswers = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAnswers");
          final int _cursorIndexOfAverageAccuracy = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAccuracy");
          final int _cursorIndexOfUnlockedAchievements = CursorUtil.getColumnIndexOrThrow(_cursor, "unlockedAchievements");
          final int _cursorIndexOfTotalAchievements = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAchievements");
          final int _cursorIndexOfDailyGoal = CursorUtil.getColumnIndexOrThrow(_cursor, "dailyGoal");
          final int _cursorIndexOfReminderEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderEnabled");
          final int _cursorIndexOfReminderTime = CursorUtil.getColumnIndexOrThrow(_cursor, "reminderTime");
          final int _cursorIndexOfAutoPlayPronunciation = CursorUtil.getColumnIndexOrThrow(_cursor, "autoPlayPronunciation");
          final int _cursorIndexOfSlowPronunciation = CursorUtil.getColumnIndexOrThrow(_cursor, "slowPronunciation");
          final int _cursorIndexOfThemeMode = CursorUtil.getColumnIndexOrThrow(_cursor, "themeMode");
          final int _cursorIndexOfShowDifficulty = CursorUtil.getColumnIndexOrThrow(_cursor, "showDifficulty");
          final int _cursorIndexOfShowFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "showFrequency");
          final int _cursorIndexOfShowStreak = CursorUtil.getColumnIndexOrThrow(_cursor, "showStreak");
          final int _cursorIndexOfAutoBackup = CursorUtil.getColumnIndexOrThrow(_cursor, "autoBackup");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final UserProgress _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpTotalWordsLearned;
            _tmpTotalWordsLearned = _cursor.getInt(_cursorIndexOfTotalWordsLearned);
            final int _tmpTotalPhrasesLearned;
            _tmpTotalPhrasesLearned = _cursor.getInt(_cursorIndexOfTotalPhrasesLearned);
            final long _tmpTotalStudyTime;
            _tmpTotalStudyTime = _cursor.getLong(_cursorIndexOfTotalStudyTime);
            final int _tmpStudyDays;
            _tmpStudyDays = _cursor.getInt(_cursorIndexOfStudyDays);
            final int _tmpConsecutiveDays;
            _tmpConsecutiveDays = _cursor.getInt(_cursorIndexOfConsecutiveDays);
            final long _tmpLastStudyDate;
            _tmpLastStudyDate = _cursor.getLong(_cursorIndexOfLastStudyDate);
            final int _tmpCurrentLevel;
            _tmpCurrentLevel = _cursor.getInt(_cursorIndexOfCurrentLevel);
            final int _tmpExperiencePoints;
            _tmpExperiencePoints = _cursor.getInt(_cursorIndexOfExperiencePoints);
            final int _tmpTotalExperience;
            _tmpTotalExperience = _cursor.getInt(_cursorIndexOfTotalExperience);
            final int _tmpTotalQuizzes;
            _tmpTotalQuizzes = _cursor.getInt(_cursorIndexOfTotalQuizzes);
            final int _tmpCorrectAnswers;
            _tmpCorrectAnswers = _cursor.getInt(_cursorIndexOfCorrectAnswers);
            final int _tmpTotalAnswers;
            _tmpTotalAnswers = _cursor.getInt(_cursorIndexOfTotalAnswers);
            final float _tmpAverageAccuracy;
            _tmpAverageAccuracy = _cursor.getFloat(_cursorIndexOfAverageAccuracy);
            final String _tmpUnlockedAchievements;
            if (_cursor.isNull(_cursorIndexOfUnlockedAchievements)) {
              _tmpUnlockedAchievements = null;
            } else {
              _tmpUnlockedAchievements = _cursor.getString(_cursorIndexOfUnlockedAchievements);
            }
            final int _tmpTotalAchievements;
            _tmpTotalAchievements = _cursor.getInt(_cursorIndexOfTotalAchievements);
            final int _tmpDailyGoal;
            _tmpDailyGoal = _cursor.getInt(_cursorIndexOfDailyGoal);
            final boolean _tmpReminderEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfReminderEnabled);
            _tmpReminderEnabled = _tmp != 0;
            final String _tmpReminderTime;
            if (_cursor.isNull(_cursorIndexOfReminderTime)) {
              _tmpReminderTime = null;
            } else {
              _tmpReminderTime = _cursor.getString(_cursorIndexOfReminderTime);
            }
            final boolean _tmpAutoPlayPronunciation;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfAutoPlayPronunciation);
            _tmpAutoPlayPronunciation = _tmp_1 != 0;
            final boolean _tmpSlowPronunciation;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfSlowPronunciation);
            _tmpSlowPronunciation = _tmp_2 != 0;
            final String _tmpThemeMode;
            if (_cursor.isNull(_cursorIndexOfThemeMode)) {
              _tmpThemeMode = null;
            } else {
              _tmpThemeMode = _cursor.getString(_cursorIndexOfThemeMode);
            }
            final boolean _tmpShowDifficulty;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowDifficulty);
            _tmpShowDifficulty = _tmp_3 != 0;
            final boolean _tmpShowFrequency;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFrequency);
            _tmpShowFrequency = _tmp_4 != 0;
            final boolean _tmpShowStreak;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfShowStreak);
            _tmpShowStreak = _tmp_5 != 0;
            final boolean _tmpAutoBackup;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfAutoBackup);
            _tmpAutoBackup = _tmp_6 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new UserProgress(_tmpId,_tmpTotalWordsLearned,_tmpTotalPhrasesLearned,_tmpTotalStudyTime,_tmpStudyDays,_tmpConsecutiveDays,_tmpLastStudyDate,_tmpCurrentLevel,_tmpExperiencePoints,_tmpTotalExperience,_tmpTotalQuizzes,_tmpCorrectAnswers,_tmpTotalAnswers,_tmpAverageAccuracy,_tmpUnlockedAchievements,_tmpTotalAchievements,_tmpDailyGoal,_tmpReminderEnabled,_tmpReminderTime,_tmpAutoPlayPronunciation,_tmpSlowPronunciation,_tmpThemeMode,_tmpShowDifficulty,_tmpShowFrequency,_tmpShowStreak,_tmpAutoBackup,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
