package com.example.word.ui.settings

import android.app.TimePickerDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.fragment.app.Fragment
import com.example.word.databinding.FragmentSettingsBinding
import com.example.word.utils.StudyNotificationManager
import com.example.word.utils.OnlineTTSHelper
import java.util.*

/**
 * 设置页面Fragment
 */
class SettingsFragment : Fragment() {
    
    private var _binding: FragmentSettingsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        sharedPreferences = requireContext().getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        
        setupViews()
        loadSettings()
        setupClickListeners()

        // 创建通知渠道
        StudyNotificationManager.createNotificationChannel(requireContext())
    }
    
    /**
     * 设置视图
     */
    private fun setupViews() {
        // 设置每日目标选项
        val dailyGoalOptions = arrayOf("10个", "20个", "30个", "50个", "100个")
        binding.spinnerDailyGoal.adapter = android.widget.ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            dailyGoalOptions
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
        
        // 设置复习模式选项
        val reviewModeOptions = arrayOf("智能复习", "随机复习", "按难度复习", "按时间复习")
        binding.spinnerReviewMode.adapter = android.widget.ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            reviewModeOptions
        ).apply {
            setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        }
    }
    
    /**
     * 加载设置
     */
    private fun loadSettings() {
        // 每日目标
        val dailyGoal = sharedPreferences.getInt("daily_goal", 20)
        val goalIndex = when (dailyGoal) {
            10 -> 0
            20 -> 1
            30 -> 2
            50 -> 3
            100 -> 4
            else -> 1
        }
        binding.spinnerDailyGoal.setSelection(goalIndex)
        
        // 学习提醒
        binding.switchReminder.isChecked = sharedPreferences.getBoolean("reminder_enabled", true)
        
        // 提醒时间
        val reminderHour = sharedPreferences.getInt("reminder_hour", 20)
        val reminderMinute = sharedPreferences.getInt("reminder_minute", 0)
        binding.textViewReminderTime.text = String.format("%02d:%02d", reminderHour, reminderMinute)
        
        // 复习模式
        val reviewMode = sharedPreferences.getInt("review_mode", 0)
        binding.spinnerReviewMode.setSelection(reviewMode)
        
        // 发音设置
        binding.switchAutoPlay.isChecked = sharedPreferences.getBoolean("auto_play_pronunciation", false)
        binding.switchSlowSpeed.isChecked = sharedPreferences.getBoolean("slow_pronunciation", false)
        
        // 主题设置
        val nightMode = sharedPreferences.getInt("night_mode", AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM)
        when (nightMode) {
            AppCompatDelegate.MODE_NIGHT_NO -> binding.radioButtonLight.isChecked = true
            AppCompatDelegate.MODE_NIGHT_YES -> binding.radioButtonDark.isChecked = true
            else -> binding.radioButtonSystem.isChecked = true
        }
        
        // 学习统计
        binding.switchShowProgress.isChecked = sharedPreferences.getBoolean("show_progress", true)
        binding.switchShowStreak.isChecked = sharedPreferences.getBoolean("show_streak", true)
        
        // 数据管理
        binding.switchAutoBackup.isChecked = sharedPreferences.getBoolean("auto_backup", false)
    }
    
    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 每日目标
        binding.spinnerDailyGoal.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                val goal = when (position) {
                    0 -> 10
                    1 -> 20
                    2 -> 30
                    3 -> 50
                    4 -> 100
                    else -> 20
                }
                sharedPreferences.edit().putInt("daily_goal", goal).apply()
            }
            
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }
        
        // 学习提醒开关
        binding.switchReminder.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("reminder_enabled", isChecked).apply()
            binding.layoutReminderTime.visibility = if (isChecked) View.VISIBLE else View.GONE
        }
        
        // 提醒时间设置
        binding.layoutReminderTime.setOnClickListener {
            showTimePickerDialog()
        }
        
        // 复习模式
        binding.spinnerReviewMode.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                sharedPreferences.edit().putInt("review_mode", position).apply()
            }
            
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }
        
        // 发音设置
        binding.switchAutoPlay.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("auto_play_pronunciation", isChecked).apply()
        }
        
        binding.switchSlowSpeed.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("slow_pronunciation", isChecked).apply()
        }
        
        // 主题设置
        binding.radioGroupTheme.setOnCheckedChangeListener { _, checkedId ->
            val nightMode = when (checkedId) {
                binding.radioButtonLight.id -> AppCompatDelegate.MODE_NIGHT_NO
                binding.radioButtonDark.id -> AppCompatDelegate.MODE_NIGHT_YES
                else -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
            }
            sharedPreferences.edit().putInt("night_mode", nightMode).apply()
            AppCompatDelegate.setDefaultNightMode(nightMode)
        }
        
        // 学习统计
        binding.switchShowProgress.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("show_progress", isChecked).apply()
        }
        
        binding.switchShowStreak.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("show_streak", isChecked).apply()
        }
        
        // 数据管理
        binding.switchAutoBackup.setOnCheckedChangeListener { _, isChecked ->
            sharedPreferences.edit().putBoolean("auto_backup", isChecked).apply()
        }
        
        // 导出数据
        binding.buttonExportData.setOnClickListener {
            exportUserData()
        }
        
        // 导入数据
        binding.buttonImportData.setOnClickListener {
            importUserData()
        }
        
        // 清除数据
        binding.buttonClearData.setOnClickListener {
            showClearDataDialog()
        }
        
        // 关于应用
        binding.buttonAbout.setOnClickListener {
            showAboutDialog()
        }

        // 测试TTS API
        binding.buttonTestTTS.setOnClickListener {
            testTTSAPI()
        }
    }
    
    /**
     * 显示时间选择器
     */
    private fun showTimePickerDialog() {
        val hour = sharedPreferences.getInt("reminder_hour", 20)
        val minute = sharedPreferences.getInt("reminder_minute", 0)
        
        TimePickerDialog(
            requireContext(),
            { _, selectedHour, selectedMinute ->
                sharedPreferences.edit()
                    .putInt("reminder_hour", selectedHour)
                    .putInt("reminder_minute", selectedMinute)
                    .apply()
                
                binding.textViewReminderTime.text = String.format("%02d:%02d", selectedHour, selectedMinute)
                
                // TODO: 设置通知提醒
                scheduleNotification(selectedHour, selectedMinute)
            },
            hour,
            minute,
            true
        ).show()
    }
    
    /**
     * 设置通知提醒
     */
    private fun scheduleNotification(hour: Int, minute: Int) {
        if (sharedPreferences.getBoolean("reminder_enabled", true)) {
            StudyNotificationManager.scheduleStudyReminder(requireContext(), hour, minute)
            Toast.makeText(requireContext(), "提醒时间已设置为 ${String.format("%02d:%02d", hour, minute)}", Toast.LENGTH_SHORT).show()
        } else {
            StudyNotificationManager.cancelStudyReminder(requireContext())
            Toast.makeText(requireContext(), "学习提醒已关闭", Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 导出用户数据
     */
    private fun exportUserData() {
        // TODO: 实现数据导出功能
        Toast.makeText(requireContext(), "数据导出功能开发中", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 导入用户数据
     */
    private fun importUserData() {
        // TODO: 实现数据导入功能
        Toast.makeText(requireContext(), "数据导入功能开发中", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示清除数据对话框
     */
    private fun showClearDataDialog() {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("清除数据")
            .setMessage("确定要清除所有学习数据吗？此操作不可恢复。")
            .setPositiveButton("确定") { _, _ ->
                clearUserData()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 清除用户数据
     */
    private fun clearUserData() {
        // TODO: 实现清除数据功能
        Toast.makeText(requireContext(), "数据清除功能开发中", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示关于对话框
     */
    private fun showAboutDialog() {
        androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("关于应用")
            .setMessage("CET-4英语单词学习应用\n版本: 1.0.0\n\n一个专为CET-4考生设计的智能单词学习应用，采用科学的间隔重复算法，帮助您高效记忆4500个核心词汇。")
            .setPositiveButton("确定", null)
            .show()
    }

    /**
     * 测试TTS API
     */
    private fun testTTSAPI() {
        val progressDialog = androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("测试TTS API")
            .setMessage("正在测试API连接...")
            .setCancelable(false)
            .create()

        progressDialog.show()

        OnlineTTSHelper.simpleAPITest { result ->
            progressDialog.dismiss()

            androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("TTS API测试结果")
                .setMessage(result)
                .setPositiveButton("确定", null)
                .setNeutralButton("测试发音") { _, _ ->
                    testTTSAudio()
                }
                .show()
        }
    }

    /**
     * 测试TTS音频
     */
    private fun testTTSAudio() {
        val progressDialog = androidx.appcompat.app.AlertDialog.Builder(requireContext())
            .setTitle("测试TTS音频")
            .setMessage("正在获取测试音频...")
            .setCancelable(false)
            .create()

        progressDialog.show()

        OnlineTTSHelper.getTTSAudio("hello") { success, message, audioData ->
            progressDialog.dismiss()

            val resultMessage = if (success && audioData != null) {
                "音频获取成功！\n音频大小: ${audioData.size} 字节"
            } else {
                "音频获取失败: $message"
            }

            androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("TTS音频测试结果")
                .setMessage(resultMessage)
                .setPositiveButton("确定", null)
                .show()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
