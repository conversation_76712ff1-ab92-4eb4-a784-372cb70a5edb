<EMAIL>'com.example.word.ui.essay.EssayFragment.com.example.word.ui.essay.EssayTemplateAdapterAcom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolderCcom.example.word.ui.essay.EssayTemplateAdapter.TemplateDiffCallback(com.example.word.ui.essay.EssayViewModel%com.example.word.ui.home.HomeFragment)com.example.word.ui.phrases.PhraseAdapter:com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder<com.example.word.ui.phrases.PhraseAdapter.PhraseDiffCallback+com.example.word.ui.phrases.PhrasesFragment/com.example.word.ui.progress.AchievementAdapterEcom.example.word.ui.progress.AchievementAdapter.AchievementViewHolderGcom.example.word.ui.progress.AchievementAdapter.AchievementDiffCallback-com.example.word.ui.progress.ProgressFragment.com.example.word.ui.progress.ProgressViewModel%com.example.word.ui.quiz.QuizFragment&com.example.word.ui.quiz.QuizViewModel"com.example.word.ui.quiz.QuizState%com.example.word.ui.quiz.QuestionType#com.example.word.ui.quiz.QuizSource-com.example.word.ui.settings.SettingsFragment-com.example.word.ui.viewmodel.PhraseViewModel+com.example.word.ui.viewmodel.WordViewModel1com.example.word.ui.vocabulary.VocabularyFragment*com.example.word.ui.vocabulary.WordAdapter9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder;com.example.word.ui.vocabulary.WordAdapter.WordDiffCallback)com.example.word.utils.StudyProgressChart,com.example.word.utils.CircularProgressChart?com.example.word.utils.GameificationManager.AchievementCategory:com.example.word.utils.GameificationManager.ExperienceType,com.example.word.utils.StudyReminderReceiver&com.example.word.utils.DifficultyLevel!com.example.word.utils.TTSManager com.example.word.WordApplication6com.example.word.utils.SystemIssueResolver.SystemIssue/com.example.word.utils.TTSErrorHandler.TTSError                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          