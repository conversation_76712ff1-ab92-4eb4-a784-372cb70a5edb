package com.example.word.data.parser

import android.content.Context
import android.util.Log
import com.example.word.data.entities.Word
import com.example.word.data.entities.Phrase
import com.example.word.data.entities.EssayTemplate
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 文本数据解析器
 * 用于解析1.txt文件中的英语学习数据
 */
object TextDataParser {
    
    private const val TAG = "TextDataParser"
    
    /**
     * 解析1.txt文件中的单词数据
     */
    fun parseWordsFromText(context: Context): List<Word> {
        val words = mutableListOf<Word>()
        
        try {
            val inputStream = context.assets.open("1.txt")
            val reader = BufferedReader(InputStreamReader(inputStream, "UTF-8"))
            
            var line: String?
            var isInHighFreqSection = false
            var isInCET4Section = false
            var currentIndex = 1
            
            while (reader.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    when {
                        // 检测高频词汇部分
                        currentLine.contains("**第一部分：高频词汇列表**") -> {
                            isInHighFreqSection = true
                            Log.d(TAG, "Found high frequency vocabulary section")
                        }
                        
                        // 检测CET-4 1000高频词部分
                        currentLine.contains("**第六部分：英语四级 1000 高频词**") -> {
                            isInCET4Section = true
                            isInHighFreqSection = false
                            Log.d(TAG, "Found CET-4 1000 high frequency words section")
                        }
                        
                        // 检测其他部分开始，结束当前解析
                        currentLine.contains("**第二部分：") || 
                        currentLine.contains("**第三部分：") ||
                        currentLine.contains("**第四部分：") ||
                        currentLine.contains("**第五部分：") -> {
                            isInHighFreqSection = false
                            isInCET4Section = false
                        }
                        
                        // 解析高频词汇
                        isInHighFreqSection && parseHighFreqWord(currentLine)?.let { word ->
                            words.add(word.copy(id = currentIndex++.toLong()))
                        } != null -> {
                            // 词汇已添加
                        }

                        // 解析CET-4词汇
                        isInCET4Section && parseCET4Word(currentLine)?.let { word ->
                            words.add(word.copy(id = currentIndex++.toLong()))
                        } != null -> {
                            // 词汇已添加
                        }
                    }
                }
            }
            
            reader.close()
            Log.d(TAG, "Parsed ${words.size} words from text file")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing words from text file", e)
        }
        
        return words
    }
    
    /**
     * 解析高频词汇行
     * 格式: "1. available (414 次)a.可利用的，可得到"
     */
    private fun parseHighFreqWord(line: String): Word? {
        return try {
            // 匹配格式: 数字. 单词 (次数)词性.中文释义
            val regex = """(\d+)\.\s*([a-zA-Z]+)\s*\((\d+)\s*次\)\s*([a-z\.]+)\s*(.+)""".toRegex()
            val matchResult = regex.find(line.trim())
            
            matchResult?.let { match ->
                val (_, word, frequency, partOfSpeech, translation) = match.destructured
                
                Word(
                    id = 0L, // 将在调用处设置
                    word = word.trim(),
                    translation = translation.trim(),
                    phonetic = "", // 高频词汇部分没有音标
                    partOfSpeech = partOfSpeech.trim(),
                    exampleSentence = "",
                    exampleTranslation = "",
                    difficultyLevel = calculateDifficultyFromFrequency(frequency.toInt()),
                    frequencyRank = frequency.toInt(),
                    rootWord = "",
                    prefix = "",
                    suffix = "",
                    etymology = "",
                    memoryTip = "",
                    relatedWords = "",
                    synonyms = "",
                    antonyms = ""
                )
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse high freq word line: $line", e)
            null
        }
    }
    
    /**
     * 解析CET-4词汇行
     * 格式: "1. evaluate [ɪˈvæljueɪt]（考频 8 次）v.估值；评价"
     */
    private fun parseCET4Word(line: String): Word? {
        return try {
            // 匹配格式: 数字. 单词 [音标]（考频 次数 次）词性.中文释义
            val regex = """(\d+)\.\s*([a-zA-Z]+)\s*\[([^\]]+)\]\s*（考频\s*(\d+)\s*次）\s*([a-z\.]+)\s*(.+)""".toRegex()
            val matchResult = regex.find(line.trim())
            
            matchResult?.let { match ->
                val (_, word, phonetic, frequency, partOfSpeech, translation) = match.destructured
                
                Word(
                    id = 0L, // 将在调用处设置
                    word = word.trim(),
                    translation = translation.trim(),
                    phonetic = phonetic.trim(),
                    partOfSpeech = partOfSpeech.trim(),
                    exampleSentence = "",
                    exampleTranslation = "",
                    difficultyLevel = calculateDifficultyFromFrequency(frequency.toInt()),
                    frequencyRank = frequency.toInt(),
                    rootWord = "",
                    prefix = "",
                    suffix = "",
                    etymology = "",
                    memoryTip = "",
                    relatedWords = "",
                    synonyms = "",
                    antonyms = ""
                )
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse CET-4 word line: $line", e)
            null
        }
    }
    
    /**
     * 根据频率计算难度等级
     */
    private fun calculateDifficultyFromFrequency(frequency: Int): Int {
        return when {
            frequency >= 100 -> 1 // 高频词，简单
            frequency >= 50 -> 2  // 中高频词
            frequency >= 20 -> 3  // 中频词
            frequency >= 10 -> 4  // 中低频词
            else -> 5            // 低频词，困难
        }
    }

    /**
     * 解析短语数据
     */
    fun parsePhrasesFromText(context: Context): List<Phrase> {
        val phrases = mutableListOf<Phrase>()

        try {
            val inputStream = context.assets.open("1.txt")
            val reader = BufferedReader(InputStreamReader(inputStream, "UTF-8"))

            var line: String?
            var isInPhraseSection = false
            var currentIndex = 1

            while (reader.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    when {
                        // 检测短语部分
                        currentLine.contains("**第二部分：短语搭配列表**") -> {
                            isInPhraseSection = true
                            Log.d(TAG, "Found phrase section")
                        }

                        // 检测其他部分开始，结束当前解析
                        currentLine.contains("**第三部分：") -> {
                            isInPhraseSection = false
                        }

                        // 解析短语
                        isInPhraseSection && parsePhraseLine(currentLine)?.let { phrase ->
                            phrases.add(phrase.copy(id = currentIndex++.toLong()))
                        } != null -> {
                            // 短语已添加
                        }
                    }
                }
            }

            reader.close()
            Log.d(TAG, "Parsed ${phrases.size} phrases from text file")

        } catch (e: Exception) {
            Log.e(TAG, "Error parsing phrases from text file", e)
        }

        return phrases
    }

    /**
     * 解析短语行
     * 格式: "a series of 一系列,一连串"
     */
    private fun parsePhraseLine(line: String): Phrase? {
        return try {
            val trimmedLine = line.trim()

            // 跳过标题行和空行
            if (trimmedLine.isEmpty() ||
                trimmedLine.startsWith("**") ||
                trimmedLine.startsWith("---") ||
                trimmedLine.contains("第") && trimmedLine.contains("页")) {
                return null
            }

            // 查找第一个中文字符的位置
            val chineseIndex = trimmedLine.indexOfFirst { it.toString().matches(Regex("[\\u4e00-\\u9fa5]")) }

            if (chineseIndex > 0) {
                val englishPart = trimmedLine.substring(0, chineseIndex).trim()
                val chinesePart = trimmedLine.substring(chineseIndex).trim()

                if (englishPart.isNotEmpty() && chinesePart.isNotEmpty()) {
                    return Phrase(
                        id = 0L, // 将在调用处设置
                        phrase = englishPart,
                        translation = chinesePart,
                        type = "collocation", // 搭配类型
                        exampleSentence = "",
                        exampleTranslation = "",
                        frequency = 3, // 默认频率
                        difficultyLevel = 2,
                        category = "常用短语"
                    )
                }
            }

            null
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse phrase line: $line", e)
            null
        }
    }

    /**
     * 解析作文模板数据
     */
    fun parseEssayTemplatesFromText(context: Context): List<EssayTemplate> {
        val templates = mutableListOf<EssayTemplate>()

        try {
            val inputStream = context.assets.open("1.txt")
            val reader = BufferedReader(InputStreamReader(inputStream, "UTF-8"))

            var line: String?
            var isInEssaySection = false
            var currentIndex = 1

            while (reader.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    when {
                        // 检测作文部分
                        currentLine.contains("**第四部分：四级作文黄金句式**") ||
                        currentLine.contains("**第五部分：四六级作文首段模板**") -> {
                            isInEssaySection = true
                            Log.d(TAG, "Found essay template section")
                        }

                        // 检测其他部分开始，结束当前解析
                        currentLine.contains("**第六部分：") -> {
                            isInEssaySection = false
                        }

                        // 解析作文模板
                        isInEssaySection && parseEssayTemplateLine(currentLine)?.let { template ->
                            templates.add(template.copy(id = currentIndex++.toLong()))
                        } != null -> {
                            // 模板已添加
                        }
                    }
                }
            }

            reader.close()
            Log.d(TAG, "Parsed ${templates.size} essay templates from text file")

        } catch (e: Exception) {
            Log.e(TAG, "Error parsing essay templates from text file", e)
        }

        return templates
    }

    /**
     * 解析作文模板行
     */
    private fun parseEssayTemplateLine(line: String): EssayTemplate? {
        return try {
            val trimmedLine = line.trim()

            // 跳过标题行和空行
            if (trimmedLine.isEmpty() ||
                trimmedLine.startsWith("**") ||
                trimmedLine.startsWith("---") ||
                trimmedLine.contains("第") && trimmedLine.contains("页")) {
                return null
            }

            // 匹配编号开头的句式
            val numberRegex = """(\d+)\.\s*(.+)""".toRegex()
            val matchResult = numberRegex.find(trimmedLine)

            matchResult?.let { match ->
                val (_, content) = match.destructured

                // 查找中文翻译
                val chineseIndex = content.indexOfFirst { it.toString().matches(Regex("[\\u4e00-\\u9fa5]")) }

                if (chineseIndex > 0) {
                    val englishPart = content.substring(0, chineseIndex).trim()
                    val chinesePart = content.substring(chineseIndex).trim()

                    if (englishPart.isNotEmpty() && chinesePart.isNotEmpty()) {
                        return EssayTemplate(
                            id = 0L, // 将在调用处设置
                            title = "四级作文句式",
                            type = "sentence_pattern", // 句型类型
                            content = englishPart,
                            description = chinesePart,
                            usage = "用于四级作文写作",
                            difficultyLevel = 3,
                            category = "黄金句式"
                        )
                    }
                }
            }

            null
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse essay template line: $line", e)
            null
        }
    }
}
