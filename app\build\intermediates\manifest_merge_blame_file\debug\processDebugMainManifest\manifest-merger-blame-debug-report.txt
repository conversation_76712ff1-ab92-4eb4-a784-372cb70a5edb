1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.word"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
15-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:9:5-74
15-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:9:22-71
16
17    <permission
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:11:5-37:19
24        android:name="com.example.word.WordApplication"
24-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:12:9-40
25        android:allowBackup="true"
25-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:14:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:15:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:16:9-43
32        android:label="@string/app_name"
32-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:17:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:18:9-54
34        android:supportsRtl="true"
34-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:19:9-35
35        android:theme="@style/Theme.Word" >
35-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:20:9-42
36        <activity
36-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:22:9-30:20
37            android:name="com.example.word.MainActivity"
37-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:23:13-41
38            android:exported="true" >
38-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:24:13-36
39            <intent-filter>
39-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:25:13-29:29
40                <action android:name="android.intent.action.MAIN" />
40-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:17-69
40-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:28:17-77
42-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:28:27-74
43            </intent-filter>
44        </activity>
45
46        <!-- 学习提醒广播接收器 -->
47        <receiver
47-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:33:9-35:40
48            android:name="com.example.word.utils.StudyReminderReceiver"
48-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:33:19-62
49            android:enabled="true"
49-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:34:13-35
50            android:exported="false" />
50-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:35:13-37
51
52        <provider
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.word.androidx-startup"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <uses-library
67-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
68            android:name="androidx.window.extensions"
68-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
69            android:required="false" />
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
70        <uses-library
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
71            android:name="androidx.window.sidecar"
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
72            android:required="false" />
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
73
74        <service
74-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
75            android:name="androidx.room.MultiInstanceInvalidationService"
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
76            android:directBootAware="true"
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
77            android:exported="false" />
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98    </application>
99
100</manifest>
