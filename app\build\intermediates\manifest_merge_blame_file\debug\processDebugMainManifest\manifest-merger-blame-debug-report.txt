1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.word"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
15-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:9:5-74
15-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:9:22-71
16
17    <permission
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
18        android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
19        android:protectionLevel="signature" />
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
20
21    <uses-permission android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
22
23    <application
23-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:11:5-37:19
24        android:name="com.example.word.WordApplication"
24-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:12:9-40
25        android:allowBackup="true"
25-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:14:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:15:9-54
31        android:icon="@mipmap/ic_launcher"
31-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:16:9-43
32        android:label="@string/app_name"
32-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:17:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:18:9-54
34        android:supportsRtl="true"
34-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:19:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.Word" >
36-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:20:9-42
37        <activity
37-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:22:9-30:20
38            android:name="com.example.word.MainActivity"
38-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:23:13-41
39            android:exported="true" >
39-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:24:13-36
40            <intent-filter>
40-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:25:13-29:29
41                <action android:name="android.intent.action.MAIN" />
41-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:17-69
41-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:28:17-77
43-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:28:27-74
44            </intent-filter>
45        </activity>
46
47        <!-- 学习提醒广播接收器 -->
48        <receiver
48-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:33:9-35:40
49            android:name="com.example.word.utils.StudyReminderReceiver"
49-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:33:19-62
50            android:enabled="true"
50-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:34:13-35
51            android:exported="false" />
51-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:35:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.word.androidx-startup"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <uses-library
68-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
69            android:name="androidx.window.extensions"
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
70            android:required="false" />
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
71        <uses-library
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
72            android:name="androidx.window.sidecar"
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
73            android:required="false" />
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
74
75        <service
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
76            android:name="androidx.room.MultiInstanceInvalidationService"
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
77            android:directBootAware="true"
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
78            android:exported="false" />
78-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
79
80        <receiver
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
81            android:name="androidx.profileinstaller.ProfileInstallReceiver"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
82            android:directBootAware="false"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
83            android:enabled="true"
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
84            android:exported="true"
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
85            android:permission="android.permission.DUMP" >
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
87                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
90                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
93                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
96                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
96-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
97            </intent-filter>
98        </receiver>
99    </application>
100
101</manifest>
