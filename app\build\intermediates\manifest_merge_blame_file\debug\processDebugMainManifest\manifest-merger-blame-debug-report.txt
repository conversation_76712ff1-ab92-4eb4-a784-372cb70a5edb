1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.word"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:5-68
12-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:6:22-65
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:5-77
14-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:8:22-74
15
16    <permission
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.example.word.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:10:5-35:19
23        android:allowBackup="true"
23-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a1960ec168b53b22418913b64c576bd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.Word" >
34-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:18:9-42
35        <activity
35-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:20:9-28:20
36            android:name="com.example.word.MainActivity"
36-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:21:13-41
37            android:exported="true" >
37-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:22:13-36
38            <intent-filter>
38-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:24:17-69
39-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:17-77
41-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44
45        <!-- 学习提醒广播接收器 -->
46        <receiver
46-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:31:9-33:40
47            android:name="com.example.word.utils.StudyReminderReceiver"
47-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:31:19-62
48            android:enabled="true"
48-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:32:13-35
49            android:exported="false" />
49-->D:\AndroidStudioProjects\word\app\src\main\AndroidManifest.xml:33:13-37
50
51        <provider
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
52            android:name="androidx.startup.InitializationProvider"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
53            android:authorities="com.example.word.androidx-startup"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
54            android:exported="false" >
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
55            <meta-data
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.emoji2.text.EmojiCompatInitializer"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
57                android:value="androidx.startup" />
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\001d0c4ad8f6f8ca40a04e62cf52b8bd\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69aa19f04a01566d92f27f811956c9d8\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
63                android:value="androidx.startup" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
64        </provider>
65
66        <uses-library
66-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
67            android:name="androidx.window.extensions"
67-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
68            android:required="false" />
68-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
69        <uses-library
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
70            android:name="androidx.window.sidecar"
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
71            android:required="false" />
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f6bf402dbfa8df7b6016c302f99d03c7\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
72
73        <service
73-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
74            android:name="androidx.room.MultiInstanceInvalidationService"
74-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
75            android:directBootAware="true"
75-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
76            android:exported="false" />
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6a7eeda7e1f1b70463061d1d0d88986\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
77
78        <receiver
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
79            android:name="androidx.profileinstaller.ProfileInstallReceiver"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
80            android:directBootAware="false"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
81            android:enabled="true"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
82            android:exported="true"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
83            android:permission="android.permission.DUMP" >
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
85                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
88                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
91                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
94                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab42fcb6cd1e6e910c7950d9126ad91d\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
95            </intent-filter>
96        </receiver>
97    </application>
98
99</manifest>
