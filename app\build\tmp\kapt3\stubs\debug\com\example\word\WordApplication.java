package com.example.word;

/**
 * 应用程序类
 * 处理全局初始化和异常处理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0003\n\u0002\b\u0004\u0018\u0000 \t2\u00020\u0001:\u0001\tB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\b\u0010\u0007\u001a\u00020\u0004H\u0016J\b\u0010\b\u001a\u00020\u0004H\u0002\u00a8\u0006\n"}, d2 = {"Lcom/example/word/WordApplication;", "Landroid/app/Application;", "()V", "logCrashInfo", "", "exception", "", "onCreate", "setupGlobalExceptionHandler", "Companion", "app_debug"})
public final class WordApplication extends android.app.Application {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "WordApplication";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.WordApplication.Companion Companion = null;
    
    public WordApplication() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    /**
     * 设置全局异常处理器
     */
    private final void setupGlobalExceptionHandler() {
    }
    
    /**
     * 记录崩溃信息
     */
    private final void logCrashInfo(java.lang.Throwable exception) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/word/WordApplication$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}