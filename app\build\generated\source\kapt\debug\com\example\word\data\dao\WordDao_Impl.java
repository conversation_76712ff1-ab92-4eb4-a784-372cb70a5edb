package com.example.word.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.word.data.entities.Word;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WordDao_Impl implements WordDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Word> __insertionAdapterOfWord;

  private final EntityDeletionOrUpdateAdapter<Word> __deletionAdapterOfWord;

  private final EntityDeletionOrUpdateAdapter<Word> __updateAdapterOfWord;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBookmarkStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStudyStats;

  private final SharedSQLiteStatement __preparedStmtOfUpdateReviewInfo;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllWords;

  public WordDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWord = new EntityInsertionAdapter<Word>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `words` (`id`,`word`,`translation`,`phonetic`,`partOfSpeech`,`exampleSentence`,`exampleTranslation`,`difficultyLevel`,`frequencyRank`,`wordLength`,`isBookmarked`,`studyCount`,`accuracyRate`,`lastStudiedTime`,`nextReviewTime`,`reviewInterval`,`memoryStrength`,`rootWord`,`prefix`,`suffix`,`etymology`,`memoryTip`,`relatedWords`,`synonyms`,`antonyms`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Word entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getWord() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getWord());
        }
        if (entity.getTranslation() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTranslation());
        }
        if (entity.getPhonetic() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhonetic());
        }
        if (entity.getPartOfSpeech() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getPartOfSpeech());
        }
        if (entity.getExampleSentence() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getExampleSentence());
        }
        if (entity.getExampleTranslation() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getExampleTranslation());
        }
        statement.bindLong(8, entity.getDifficultyLevel());
        statement.bindLong(9, entity.getFrequencyRank());
        statement.bindLong(10, entity.getWordLength());
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(11, _tmp);
        statement.bindLong(12, entity.getStudyCount());
        statement.bindLong(13, entity.getAccuracyRate());
        statement.bindLong(14, entity.getLastStudiedTime());
        statement.bindLong(15, entity.getNextReviewTime());
        statement.bindLong(16, entity.getReviewInterval());
        statement.bindDouble(17, entity.getMemoryStrength());
        if (entity.getRootWord() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getRootWord());
        }
        if (entity.getPrefix() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getPrefix());
        }
        if (entity.getSuffix() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getSuffix());
        }
        if (entity.getEtymology() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getEtymology());
        }
        if (entity.getMemoryTip() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getMemoryTip());
        }
        if (entity.getRelatedWords() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getRelatedWords());
        }
        if (entity.getSynonyms() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getSynonyms());
        }
        if (entity.getAntonyms() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getAntonyms());
        }
      }
    };
    this.__deletionAdapterOfWord = new EntityDeletionOrUpdateAdapter<Word>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `words` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Word entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfWord = new EntityDeletionOrUpdateAdapter<Word>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `words` SET `id` = ?,`word` = ?,`translation` = ?,`phonetic` = ?,`partOfSpeech` = ?,`exampleSentence` = ?,`exampleTranslation` = ?,`difficultyLevel` = ?,`frequencyRank` = ?,`wordLength` = ?,`isBookmarked` = ?,`studyCount` = ?,`accuracyRate` = ?,`lastStudiedTime` = ?,`nextReviewTime` = ?,`reviewInterval` = ?,`memoryStrength` = ?,`rootWord` = ?,`prefix` = ?,`suffix` = ?,`etymology` = ?,`memoryTip` = ?,`relatedWords` = ?,`synonyms` = ?,`antonyms` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Word entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getWord() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getWord());
        }
        if (entity.getTranslation() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTranslation());
        }
        if (entity.getPhonetic() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhonetic());
        }
        if (entity.getPartOfSpeech() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getPartOfSpeech());
        }
        if (entity.getExampleSentence() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getExampleSentence());
        }
        if (entity.getExampleTranslation() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getExampleTranslation());
        }
        statement.bindLong(8, entity.getDifficultyLevel());
        statement.bindLong(9, entity.getFrequencyRank());
        statement.bindLong(10, entity.getWordLength());
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(11, _tmp);
        statement.bindLong(12, entity.getStudyCount());
        statement.bindLong(13, entity.getAccuracyRate());
        statement.bindLong(14, entity.getLastStudiedTime());
        statement.bindLong(15, entity.getNextReviewTime());
        statement.bindLong(16, entity.getReviewInterval());
        statement.bindDouble(17, entity.getMemoryStrength());
        if (entity.getRootWord() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getRootWord());
        }
        if (entity.getPrefix() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getPrefix());
        }
        if (entity.getSuffix() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getSuffix());
        }
        if (entity.getEtymology() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getEtymology());
        }
        if (entity.getMemoryTip() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getMemoryTip());
        }
        if (entity.getRelatedWords() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getRelatedWords());
        }
        if (entity.getSynonyms() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getSynonyms());
        }
        if (entity.getAntonyms() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getAntonyms());
        }
        statement.bindLong(26, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateBookmarkStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE words SET isBookmarked = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateStudyStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE words SET studyCount = studyCount + 1, lastStudiedTime = ?, accuracyRate = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateReviewInfo = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE words SET nextReviewTime = ?, reviewInterval = ?, memoryStrength = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllWords = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM words";
        return _query;
      }
    };
  }

  @Override
  public Object insertWord(final Word word, final Continuation<? super Long> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfWord.insertAndReturnId(word);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object insertWords(final List<Word> words, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfWord.insert(words);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteWord(final Word word, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWord.handle(word);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateWord(final Word word, final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWord.handle(word);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateBookmarkStatus(final long id, final boolean isBookmarked,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBookmarkStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isBookmarked ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBookmarkStatus.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object updateStudyStats(final long id, final long time, final int accuracy,
      final Continuation<? super Unit> arg3) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStudyStats.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, time);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, accuracy);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateStudyStats.release(_stmt);
        }
      }
    }, arg3);
  }

  @Override
  public Object updateReviewInfo(final long id, final long nextTime, final int interval,
      final float strength, final Continuation<? super Unit> arg4) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateReviewInfo.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, nextTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, interval);
        _argIndex = 3;
        _stmt.bindDouble(_argIndex, strength);
        _argIndex = 4;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateReviewInfo.release(_stmt);
        }
      }
    }, arg4);
  }

  @Override
  public Object deleteAllWords(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllWords.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllWords.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public LiveData<List<Word>> getAllWords() {
    final String _sql = "SELECT * FROM words ORDER BY frequencyRank ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWordById(final long id, final Continuation<? super Word> arg1) {
    final String _sql = "SELECT * FROM words WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Word>() {
      @Override
      @Nullable
      public Word call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final Word _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _result = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public LiveData<List<Word>> searchWords(final String query) {
    final String _sql = "SELECT * FROM words WHERE word LIKE '%' || ? || '%' OR translation LIKE '%' || ? || '%' ORDER BY frequencyRank ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Word>> getWordsByDifficulty(final int level) {
    final String _sql = "SELECT * FROM words WHERE difficultyLevel = ? ORDER BY frequencyRank ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, level);
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Word>> getWordsByPartOfSpeech(final String pos) {
    final String _sql = "SELECT * FROM words WHERE partOfSpeech = ? ORDER BY frequencyRank ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (pos == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, pos);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Word>> getBookmarkedWords() {
    final String _sql = "SELECT * FROM words WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Word>> getWordsForReview(final long currentTime) {
    final String _sql = "SELECT * FROM words WHERE nextReviewTime <= ? AND studyCount > 0 ORDER BY nextReviewTime ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, currentTime);
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRandomWords(final int count, final Continuation<? super List<Word>> arg1) {
    final String _sql = "SELECT * FROM words ORDER BY RANDOM() LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, count);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Word>>() {
      @Override
      @NonNull
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public LiveData<List<Word>> getWordsByFrequencyRange(final int startRank, final int endRank) {
    final String _sql = "SELECT * FROM words WHERE frequencyRank BETWEEN ? AND ? ORDER BY frequencyRank ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startRank);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endRank);
    return __db.getInvalidationTracker().createLiveData(new String[] {"words"}, false, new Callable<List<Word>>() {
      @Override
      @Nullable
      public List<Word> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWord = CursorUtil.getColumnIndexOrThrow(_cursor, "word");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfPhonetic = CursorUtil.getColumnIndexOrThrow(_cursor, "phonetic");
          final int _cursorIndexOfPartOfSpeech = CursorUtil.getColumnIndexOrThrow(_cursor, "partOfSpeech");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfFrequencyRank = CursorUtil.getColumnIndexOrThrow(_cursor, "frequencyRank");
          final int _cursorIndexOfWordLength = CursorUtil.getColumnIndexOrThrow(_cursor, "wordLength");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfAccuracyRate = CursorUtil.getColumnIndexOrThrow(_cursor, "accuracyRate");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final int _cursorIndexOfNextReviewTime = CursorUtil.getColumnIndexOrThrow(_cursor, "nextReviewTime");
          final int _cursorIndexOfReviewInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "reviewInterval");
          final int _cursorIndexOfMemoryStrength = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryStrength");
          final int _cursorIndexOfRootWord = CursorUtil.getColumnIndexOrThrow(_cursor, "rootWord");
          final int _cursorIndexOfPrefix = CursorUtil.getColumnIndexOrThrow(_cursor, "prefix");
          final int _cursorIndexOfSuffix = CursorUtil.getColumnIndexOrThrow(_cursor, "suffix");
          final int _cursorIndexOfEtymology = CursorUtil.getColumnIndexOrThrow(_cursor, "etymology");
          final int _cursorIndexOfMemoryTip = CursorUtil.getColumnIndexOrThrow(_cursor, "memoryTip");
          final int _cursorIndexOfRelatedWords = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedWords");
          final int _cursorIndexOfSynonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "synonyms");
          final int _cursorIndexOfAntonyms = CursorUtil.getColumnIndexOrThrow(_cursor, "antonyms");
          final List<Word> _result = new ArrayList<Word>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Word _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWord;
            if (_cursor.isNull(_cursorIndexOfWord)) {
              _tmpWord = null;
            } else {
              _tmpWord = _cursor.getString(_cursorIndexOfWord);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpPhonetic;
            if (_cursor.isNull(_cursorIndexOfPhonetic)) {
              _tmpPhonetic = null;
            } else {
              _tmpPhonetic = _cursor.getString(_cursorIndexOfPhonetic);
            }
            final String _tmpPartOfSpeech;
            if (_cursor.isNull(_cursorIndexOfPartOfSpeech)) {
              _tmpPartOfSpeech = null;
            } else {
              _tmpPartOfSpeech = _cursor.getString(_cursorIndexOfPartOfSpeech);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final int _tmpFrequencyRank;
            _tmpFrequencyRank = _cursor.getInt(_cursorIndexOfFrequencyRank);
            final int _tmpWordLength;
            _tmpWordLength = _cursor.getInt(_cursorIndexOfWordLength);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final int _tmpAccuracyRate;
            _tmpAccuracyRate = _cursor.getInt(_cursorIndexOfAccuracyRate);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            final long _tmpNextReviewTime;
            _tmpNextReviewTime = _cursor.getLong(_cursorIndexOfNextReviewTime);
            final int _tmpReviewInterval;
            _tmpReviewInterval = _cursor.getInt(_cursorIndexOfReviewInterval);
            final float _tmpMemoryStrength;
            _tmpMemoryStrength = _cursor.getFloat(_cursorIndexOfMemoryStrength);
            final String _tmpRootWord;
            if (_cursor.isNull(_cursorIndexOfRootWord)) {
              _tmpRootWord = null;
            } else {
              _tmpRootWord = _cursor.getString(_cursorIndexOfRootWord);
            }
            final String _tmpPrefix;
            if (_cursor.isNull(_cursorIndexOfPrefix)) {
              _tmpPrefix = null;
            } else {
              _tmpPrefix = _cursor.getString(_cursorIndexOfPrefix);
            }
            final String _tmpSuffix;
            if (_cursor.isNull(_cursorIndexOfSuffix)) {
              _tmpSuffix = null;
            } else {
              _tmpSuffix = _cursor.getString(_cursorIndexOfSuffix);
            }
            final String _tmpEtymology;
            if (_cursor.isNull(_cursorIndexOfEtymology)) {
              _tmpEtymology = null;
            } else {
              _tmpEtymology = _cursor.getString(_cursorIndexOfEtymology);
            }
            final String _tmpMemoryTip;
            if (_cursor.isNull(_cursorIndexOfMemoryTip)) {
              _tmpMemoryTip = null;
            } else {
              _tmpMemoryTip = _cursor.getString(_cursorIndexOfMemoryTip);
            }
            final String _tmpRelatedWords;
            if (_cursor.isNull(_cursorIndexOfRelatedWords)) {
              _tmpRelatedWords = null;
            } else {
              _tmpRelatedWords = _cursor.getString(_cursorIndexOfRelatedWords);
            }
            final String _tmpSynonyms;
            if (_cursor.isNull(_cursorIndexOfSynonyms)) {
              _tmpSynonyms = null;
            } else {
              _tmpSynonyms = _cursor.getString(_cursorIndexOfSynonyms);
            }
            final String _tmpAntonyms;
            if (_cursor.isNull(_cursorIndexOfAntonyms)) {
              _tmpAntonyms = null;
            } else {
              _tmpAntonyms = _cursor.getString(_cursorIndexOfAntonyms);
            }
            _item = new Word(_tmpId,_tmpWord,_tmpTranslation,_tmpPhonetic,_tmpPartOfSpeech,_tmpExampleSentence,_tmpExampleTranslation,_tmpDifficultyLevel,_tmpFrequencyRank,_tmpWordLength,_tmpIsBookmarked,_tmpStudyCount,_tmpAccuracyRate,_tmpLastStudiedTime,_tmpNextReviewTime,_tmpReviewInterval,_tmpMemoryStrength,_tmpRootWord,_tmpPrefix,_tmpSuffix,_tmpEtymology,_tmpMemoryTip,_tmpRelatedWords,_tmpSynonyms,_tmpAntonyms);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getStudiedWordsCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM words WHERE studyCount > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getBookmarkedWordsCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM words WHERE isBookmarked = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getAverageAccuracy(final Continuation<? super Float> arg0) {
    final String _sql = "SELECT AVG(accuracyRate) FROM words WHERE studyCount > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Float>() {
      @Override
      @NonNull
      public Float call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Float _result;
          if (_cursor.moveToFirst()) {
            final Float _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getFloat(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
