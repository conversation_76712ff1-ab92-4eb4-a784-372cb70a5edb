package com.example.word.utils;

/**
 * TTS备用管理器
 * 当系统TTS不可用时提供备用方案
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u000e\n\u0002\b\b\u0018\u0000 \u00112\u00020\u0001:\u0001\u0011B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J \u0010\u0005\u001a\u00020\u00062\u0018\u0010\u0007\u001a\u0014\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00060\bJ\u0006\u0010\u000b\u001a\u00020\nJ\u0006\u0010\f\u001a\u00020\tJ\b\u0010\r\u001a\u00020\u0006H\u0002J\b\u0010\u000e\u001a\u00020\u0006H\u0002J\u000e\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/word/utils/TTSFallbackManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "checkAndProvideSolution", "", "onResult", "Lkotlin/Function2;", "", "", "getTTSStatusInfo", "openTTSSettings", "showLanguagePackSolution", "showTTSInstallSolution", "showTextAsFallback", "text", "Companion", "app_debug"})
public final class TTSFallbackManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "TTSFallbackManager";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.TTSFallbackManager.Companion Companion = null;
    
    public TTSFallbackManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 检查TTS可用性并提供解决方案
     */
    public final void checkAndProvideSolution(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> onResult) {
    }
    
    /**
     * 显示TTS安装解决方案
     */
    private final void showTTSInstallSolution() {
    }
    
    /**
     * 显示语言包解决方案
     */
    private final void showLanguagePackSolution() {
    }
    
    /**
     * 尝试打开TTS设置
     */
    public final boolean openTTSSettings() {
        return false;
    }
    
    /**
     * 提供文本显示作为语音的替代方案
     */
    public final void showTextAsFallback(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    /**
     * 获取TTS状态信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTTSStatusInfo() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/word/utils/TTSFallbackManager$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}