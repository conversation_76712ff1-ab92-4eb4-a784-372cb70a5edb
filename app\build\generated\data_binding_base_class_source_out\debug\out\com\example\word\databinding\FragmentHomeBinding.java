// Generated by view binder compiler. Do not edit!
package com.example.word.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.word.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentHomeBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button buttonEssays;

  @NonNull
  public final Button buttonPhrases;

  @NonNull
  public final Button buttonStartQuiz;

  @NonNull
  public final Button buttonStartReview;

  @NonNull
  public final Button buttonStartStudy;

  @NonNull
  public final Button buttonViewProgress;

  @NonNull
  public final ProgressBar progressBarDaily;

  @NonNull
  public final ProgressBar progressBarExperience;

  @NonNull
  public final TextView textViewDailyGoal;

  @NonNull
  public final TextView textViewDate;

  @NonNull
  public final TextView textViewExperience;

  @NonNull
  public final TextView textViewLevel;

  @NonNull
  public final TextView textViewLevelName;

  @NonNull
  public final TextView textViewReviewCount;

  @NonNull
  public final TextView textViewTotalWords;

  @NonNull
  public final TextView textViewWelcome;

  private FragmentHomeBinding(@NonNull ScrollView rootView, @NonNull Button buttonEssays,
      @NonNull Button buttonPhrases, @NonNull Button buttonStartQuiz,
      @NonNull Button buttonStartReview, @NonNull Button buttonStartStudy,
      @NonNull Button buttonViewProgress, @NonNull ProgressBar progressBarDaily,
      @NonNull ProgressBar progressBarExperience, @NonNull TextView textViewDailyGoal,
      @NonNull TextView textViewDate, @NonNull TextView textViewExperience,
      @NonNull TextView textViewLevel, @NonNull TextView textViewLevelName,
      @NonNull TextView textViewReviewCount, @NonNull TextView textViewTotalWords,
      @NonNull TextView textViewWelcome) {
    this.rootView = rootView;
    this.buttonEssays = buttonEssays;
    this.buttonPhrases = buttonPhrases;
    this.buttonStartQuiz = buttonStartQuiz;
    this.buttonStartReview = buttonStartReview;
    this.buttonStartStudy = buttonStartStudy;
    this.buttonViewProgress = buttonViewProgress;
    this.progressBarDaily = progressBarDaily;
    this.progressBarExperience = progressBarExperience;
    this.textViewDailyGoal = textViewDailyGoal;
    this.textViewDate = textViewDate;
    this.textViewExperience = textViewExperience;
    this.textViewLevel = textViewLevel;
    this.textViewLevelName = textViewLevelName;
    this.textViewReviewCount = textViewReviewCount;
    this.textViewTotalWords = textViewTotalWords;
    this.textViewWelcome = textViewWelcome;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentHomeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_home, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentHomeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonEssays;
      Button buttonEssays = ViewBindings.findChildViewById(rootView, id);
      if (buttonEssays == null) {
        break missingId;
      }

      id = R.id.buttonPhrases;
      Button buttonPhrases = ViewBindings.findChildViewById(rootView, id);
      if (buttonPhrases == null) {
        break missingId;
      }

      id = R.id.buttonStartQuiz;
      Button buttonStartQuiz = ViewBindings.findChildViewById(rootView, id);
      if (buttonStartQuiz == null) {
        break missingId;
      }

      id = R.id.buttonStartReview;
      Button buttonStartReview = ViewBindings.findChildViewById(rootView, id);
      if (buttonStartReview == null) {
        break missingId;
      }

      id = R.id.buttonStartStudy;
      Button buttonStartStudy = ViewBindings.findChildViewById(rootView, id);
      if (buttonStartStudy == null) {
        break missingId;
      }

      id = R.id.buttonViewProgress;
      Button buttonViewProgress = ViewBindings.findChildViewById(rootView, id);
      if (buttonViewProgress == null) {
        break missingId;
      }

      id = R.id.progressBarDaily;
      ProgressBar progressBarDaily = ViewBindings.findChildViewById(rootView, id);
      if (progressBarDaily == null) {
        break missingId;
      }

      id = R.id.progressBarExperience;
      ProgressBar progressBarExperience = ViewBindings.findChildViewById(rootView, id);
      if (progressBarExperience == null) {
        break missingId;
      }

      id = R.id.textViewDailyGoal;
      TextView textViewDailyGoal = ViewBindings.findChildViewById(rootView, id);
      if (textViewDailyGoal == null) {
        break missingId;
      }

      id = R.id.textViewDate;
      TextView textViewDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDate == null) {
        break missingId;
      }

      id = R.id.textViewExperience;
      TextView textViewExperience = ViewBindings.findChildViewById(rootView, id);
      if (textViewExperience == null) {
        break missingId;
      }

      id = R.id.textViewLevel;
      TextView textViewLevel = ViewBindings.findChildViewById(rootView, id);
      if (textViewLevel == null) {
        break missingId;
      }

      id = R.id.textViewLevelName;
      TextView textViewLevelName = ViewBindings.findChildViewById(rootView, id);
      if (textViewLevelName == null) {
        break missingId;
      }

      id = R.id.textViewReviewCount;
      TextView textViewReviewCount = ViewBindings.findChildViewById(rootView, id);
      if (textViewReviewCount == null) {
        break missingId;
      }

      id = R.id.textViewTotalWords;
      TextView textViewTotalWords = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalWords == null) {
        break missingId;
      }

      id = R.id.textViewWelcome;
      TextView textViewWelcome = ViewBindings.findChildViewById(rootView, id);
      if (textViewWelcome == null) {
        break missingId;
      }

      return new FragmentHomeBinding((ScrollView) rootView, buttonEssays, buttonPhrases,
          buttonStartQuiz, buttonStartReview, buttonStartStudy, buttonViewProgress,
          progressBarDaily, progressBarExperience, textViewDailyGoal, textViewDate,
          textViewExperience, textViewLevel, textViewLevelName, textViewReviewCount,
          textViewTotalWords, textViewWelcome);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
