package com.example.word.data.database

import android.content.Context
import android.util.Log
import com.example.word.data.entities.Word
import com.example.word.data.entities.Phrase
import com.example.word.data.entities.EssayTemplate
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException

/**
 * 词汇JSON数据加载器
 * 从assets文件夹中加载CET-4词汇数据
 */
object VocabularyJsonLoader {

    private const val TAG = "VocabularyJsonLoader"

    /**
     * 从JSON文件加载词汇数据
     */
    fun loadWordsFromJson(context: Context): List<Word> {
        return try {
            Log.d(TAG, "Loading words from JSON")
            val jsonString = loadJsonFromAssets(context, "cet4_vocabulary.json")

            if (jsonString.isEmpty()) {
                Log.w(TAG, "JSON string is empty, using default data")
                return VocabularyDataProvider.getCET4Words()
            }

            val jsonObject = JSONObject(jsonString)

            if (!jsonObject.has("vocabulary")) {
                Log.w(TAG, "JSON does not contain vocabulary object, using default data")
                return VocabularyDataProvider.getCET4Words()
            }

            val vocabularyObject = jsonObject.getJSONObject("vocabulary")

            if (!vocabularyObject.has("words")) {
                Log.w(TAG, "Vocabulary object does not contain words array, using default data")
                return VocabularyDataProvider.getCET4Words()
            }

            val wordsArray = vocabularyObject.getJSONArray("words")
            val words = mutableListOf<Word>()

            for (i in 0 until wordsArray.length()) {
                try {
                    val wordObject = wordsArray.getJSONObject(i)
                    val word = Word(
                        word = wordObject.optString("word", ""),
                        translation = wordObject.optString("translation", ""),
                        phonetic = wordObject.optString("phonetic", ""),
                        partOfSpeech = wordObject.optString("partOfSpeech", ""),
                        exampleSentence = wordObject.optString("exampleSentence", ""),
                        exampleTranslation = wordObject.optString("exampleTranslation", ""),
                        difficultyLevel = wordObject.optInt("difficultyLevel", 1),
                        frequencyRank = wordObject.optInt("frequencyRank", 1),
                        rootWord = wordObject.optString("rootWord", ""),
                        prefix = wordObject.optString("prefix", ""),
                        suffix = wordObject.optString("suffix", ""),
                        etymology = wordObject.optString("etymology", ""),
                        memoryTip = wordObject.optString("memoryTip", ""),
                        relatedWords = wordObject.optString("relatedWords", ""),
                        synonyms = wordObject.optString("synonyms", ""),
                        antonyms = wordObject.optString("antonyms", "")
                    )

                    // 验证必要字段
                    if (word.word.isNotEmpty() && word.translation.isNotEmpty()) {
                        words.add(word)
                    } else {
                        Log.w(TAG, "Skipping invalid word at index $i")
                    }
                } catch (e: JSONException) {
                    Log.e(TAG, "Error parsing word at index $i", e)
                    // 继续处理下一个单词
                }
            }

            Log.d(TAG, "Loaded ${words.size} words from JSON")
            words.ifEmpty { VocabularyDataProvider.getCET4Words() }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to load words from JSON", e)
            // 如果JSON加载失败，返回默认数据
            VocabularyDataProvider.getCET4Words()
        }
    }
    
    /**
     * 从JSON文件加载短语数据
     */
    fun loadPhrasesFromJson(context: Context): List<Phrase> {
        return try {
            val jsonString = loadJsonFromAssets(context, "cet4_vocabulary.json")
            val jsonObject = JSONObject(jsonString)
            val vocabularyObject = jsonObject.getJSONObject("vocabulary")
            val phrasesArray = vocabularyObject.getJSONArray("phrases")
            
            val phrases = mutableListOf<Phrase>()
            for (i in 0 until phrasesArray.length()) {
                val phraseObject = phrasesArray.getJSONObject(i)
                val phrase = Phrase(
                    phrase = phraseObject.getString("phrase"),
                    translation = phraseObject.getString("translation"),
                    type = phraseObject.getString("type"),
                    exampleSentence = phraseObject.getString("exampleSentence"),
                    exampleTranslation = phraseObject.getString("exampleTranslation"),
                    frequency = phraseObject.getInt("frequency"),
                    difficultyLevel = phraseObject.getInt("difficultyLevel"),
                    category = phraseObject.getString("category")
                )
                phrases.add(phrase)
            }
            
            phrases
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果JSON加载失败，返回默认数据
            VocabularyDataProvider.getCET4Phrases()
        }
    }
    
    /**
     * 从JSON文件加载作文模板数据
     */
    fun loadEssayTemplatesFromJson(context: Context): List<EssayTemplate> {
        return try {
            val jsonString = loadJsonFromAssets(context, "cet4_vocabulary.json")
            val jsonObject = JSONObject(jsonString)
            val vocabularyObject = jsonObject.getJSONObject("vocabulary")
            val templatesArray = vocabularyObject.getJSONArray("essayTemplates")
            
            val templates = mutableListOf<EssayTemplate>()
            for (i in 0 until templatesArray.length()) {
                val templateObject = templatesArray.getJSONObject(i)
                val template = EssayTemplate(
                    title = templateObject.getString("title"),
                    type = templateObject.getString("type"),
                    content = templateObject.getString("content"),
                    description = templateObject.getString("description"),
                    usage = templateObject.getString("usage"),
                    difficultyLevel = templateObject.getInt("difficultyLevel"),
                    category = templateObject.getString("category"),
                    example = templateObject.optString("example", "")
                )
                templates.add(template)
            }
            
            templates
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果JSON加载失败，返回默认数据
            VocabularyDataProvider.getEssayTemplates()
        }
    }
    
    /**
     * 从assets文件夹加载JSON文件
     */
    private fun loadJsonFromAssets(context: Context, fileName: String): String {
        return try {
            Log.d(TAG, "Loading JSON file: $fileName")

            context.assets.open(fileName).use { inputStream ->
                val size = inputStream.available()
                if (size <= 0) {
                    Log.w(TAG, "JSON file $fileName is empty or not available")
                    return ""
                }

                val buffer = ByteArray(size)
                val bytesRead = inputStream.read(buffer)

                if (bytesRead != size) {
                    Log.w(TAG, "Could not read complete file. Expected: $size, Read: $bytesRead")
                }

                val content = String(buffer, Charsets.UTF_8)
                Log.d(TAG, "Successfully loaded JSON file: $fileName (${content.length} characters)")
                content
            }
        } catch (ex: IOException) {
            Log.e(TAG, "Failed to load JSON file: $fileName", ex)
            ""
        } catch (ex: Exception) {
            Log.e(TAG, "Unexpected error loading JSON file: $fileName", ex)
            ""
        }
    }
    
    /**
     * 获取词汇数据元信息
     */
    fun getVocabularyMetadata(context: Context): VocabularyMetadata? {
        return try {
            val jsonString = loadJsonFromAssets(context, "cet4_vocabulary.json")
            val jsonObject = JSONObject(jsonString)
            val metadataObject = jsonObject.getJSONObject("metadata")
            
            VocabularyMetadata(
                version = metadataObject.getString("version"),
                totalWords = metadataObject.getInt("totalWords"),
                totalPhrases = metadataObject.getInt("totalPhrases"),
                totalTemplates = metadataObject.getInt("totalTemplates"),
                lastUpdated = metadataObject.getString("lastUpdated"),
                description = metadataObject.getString("description")
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}

/**
 * 词汇数据元信息
 */
data class VocabularyMetadata(
    val version: String,
    val totalWords: Int,
    val totalPhrases: Int,
    val totalTemplates: Int,
    val lastUpdated: String,
    val description: String
)
