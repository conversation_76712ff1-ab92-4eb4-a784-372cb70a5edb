package com.example.word.utils

import android.content.Context
import android.content.Intent
import android.speech.tts.TextToSpeech
import android.util.Log
import kotlinx.coroutines.*

/**
 * TTS错误处理和恢复机制
 */
object TTSErrorHandler {
    
    private const val TAG = "TTSErrorHandler"
    
    /**
     * TTS错误类型
     */
    enum class TTSError {
        INITIALIZATION_FAILED,
        ENGINE_NOT_AVAILABLE,
        LANGUAGE_NOT_SUPPORTED,
        NETWORK_ERROR,
        UNKNOWN_ERROR
    }
    
    /**
     * TTS错误信息
     */
    data class TTSErrorInfo(
        val error: TTSError,
        val message: String,
        val canRetry: Boolean = true,
        val suggestedAction: String? = null
    )
    
    /**
     * 分析TTS错误
     */
    fun analyzeTTSError(status: Int, context: Context): TTSErrorInfo {
        return when (status) {
            TextToSpeech.ERROR -> TTSErrorInfo(
                error = TTSError.UNKNOWN_ERROR,
                message = "TTS通用错误",
                canRetry = true,
                suggestedAction = "请重试或检查TTS设置"
            )
            
            TextToSpeech.ERROR_INVALID_REQUEST -> TTSErrorInfo(
                error = TTSError.INITIALIZATION_FAILED,
                message = "TTS请求无效",
                canRetry = false,
                suggestedAction = "请检查TTS参数"
            )
            
            TextToSpeech.ERROR_NETWORK -> TTSErrorInfo(
                error = TTSError.NETWORK_ERROR,
                message = "TTS网络错误",
                canRetry = true,
                suggestedAction = "请检查网络连接"
            )
            
            TextToSpeech.ERROR_NETWORK_TIMEOUT -> TTSErrorInfo(
                error = TTSError.NETWORK_ERROR,
                message = "TTS网络超时",
                canRetry = true,
                suggestedAction = "请检查网络连接并重试"
            )
            
            TextToSpeech.ERROR_NOT_INSTALLED_YET -> TTSErrorInfo(
                error = TTSError.ENGINE_NOT_AVAILABLE,
                message = "TTS引擎未安装",
                canRetry = false,
                suggestedAction = "请安装TTS引擎"
            )
            
            TextToSpeech.ERROR_SERVICE -> TTSErrorInfo(
                error = TTSError.ENGINE_NOT_AVAILABLE,
                message = "TTS服务错误",
                canRetry = true,
                suggestedAction = "请重启应用或检查TTS服务"
            )
            
            -1 -> TTSErrorInfo(
                error = TTSError.INITIALIZATION_FAILED,
                message = "TTS初始化失败",
                canRetry = true,
                suggestedAction = "设备可能没有TTS引擎，将使用备用方案"
            )
            
            else -> TTSErrorInfo(
                error = TTSError.UNKNOWN_ERROR,
                message = "未知TTS错误 (状态码: $status)",
                canRetry = true,
                suggestedAction = "请重试"
            )
        }
    }
    
    /**
     * 检查TTS可用性
     */
    fun checkTTSAvailability(context: Context, callback: (Boolean, String) -> Unit) {
        Log.d(TAG, "Checking TTS availability...")
        
        try {
            var ttsInstance: TextToSpeech? = null
            ttsInstance = TextToSpeech(context) { status ->
                try {
                    val errorInfo = analyzeTTSError(status, context)

                    if (status == TextToSpeech.SUCCESS) {
                        Log.d(TAG, "TTS is available")
                        callback(true, "TTS可用")
                    } else {
                        Log.w(TAG, "TTS not available: ${errorInfo.message}")
                        callback(false, errorInfo.message)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in TTS availability check", e)
                    callback(false, "TTS检查异常: ${e.message}")
                } finally {
                    try {
                        ttsInstance?.shutdown()
                    } catch (e: Exception) {
                        Log.w(TAG, "Error shutting down TTS during availability check", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception during TTS availability check", e)
            callback(false, "TTS检查异常: ${e.message}")
        }
    }
    
    /**
     * 尝试修复TTS问题
     */
    fun attemptTTSFix(context: Context, error: TTSError): Boolean {
        Log.d(TAG, "Attempting to fix TTS error: $error")
        
        return try {
            when (error) {
                TTSError.ENGINE_NOT_AVAILABLE -> {
                    // 尝试打开TTS设置
                    openTTSSettings(context)
                    true
                }
                
                TTSError.LANGUAGE_NOT_SUPPORTED -> {
                    // 尝试设置默认语言
                    Log.d(TAG, "Language not supported, using fallback")
                    true
                }
                
                TTSError.NETWORK_ERROR -> {
                    // 网络错误，建议使用离线TTS
                    Log.d(TAG, "Network error, suggesting offline TTS")
                    true
                }
                
                else -> {
                    Log.d(TAG, "No automatic fix available for error: $error")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error attempting TTS fix", e)
            false
        }
    }
    
    /**
     * 打开TTS设置
     */
    private fun openTTSSettings(context: Context): Boolean {
        return try {
            val intent = Intent("com.android.settings.TTS_SETTINGS")
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
            Log.d(TAG, "Opened TTS settings")
            true
        } catch (e: Exception) {
            Log.w(TAG, "Could not open TTS settings", e)
            
            // 尝试通用设置
            try {
                val intent = Intent(android.provider.Settings.ACTION_SETTINGS)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                context.startActivity(intent)
                Log.d(TAG, "Opened general settings")
                true
            } catch (e2: Exception) {
                Log.e(TAG, "Could not open any settings", e2)
                false
            }
        }
    }
    
    /**
     * 获取TTS状态报告
     */
    fun getTTSStatusReport(context: Context, callback: (String) -> Unit) {
        Log.d(TAG, "Generating TTS status report...")
        
        CoroutineScope(Dispatchers.IO).launch {
            val report = buildString {
                appendLine("=== TTS状态报告 ===")
                appendLine("时间: ${System.currentTimeMillis()}")
                appendLine()
                
                // 检查TTS可用性
                val deferred = CompletableDeferred<Pair<Boolean, String>>()
                checkTTSAvailability(context) { available, message ->
                    deferred.complete(Pair(available, message))
                }
                
                try {
                    val (available, message) = deferred.await()
                    appendLine("TTS可用性: $available")
                    appendLine("状态消息: $message")
                    appendLine()
                    
                    if (!available) {
                        appendLine("建议解决方案:")
                        appendLine("1. 检查设备是否安装了TTS引擎")
                        appendLine("2. 前往设置 > 辅助功能 > 文字转语音")
                        appendLine("3. 安装Google文字转语音引擎")
                        appendLine("4. 重启应用")
                        appendLine()
                    }
                    
                    appendLine("备用方案:")
                    appendLine("- 在线TTS (如果网络可用)")
                    appendLine("- 文本显示")
                    appendLine("- 视觉反馈")
                    
                } catch (e: Exception) {
                    appendLine("报告生成失败: ${e.message}")
                }
            }
            
            withContext(Dispatchers.Main) {
                callback(report)
            }
        }
    }
    
    /**
     * 创建TTS错误恢复策略
     */
    fun createRecoveryStrategy(error: TTSError): List<String> {
        return when (error) {
            TTSError.INITIALIZATION_FAILED -> listOf(
                "重新初始化TTS",
                "使用在线TTS",
                "使用文本显示"
            )
            
            TTSError.ENGINE_NOT_AVAILABLE -> listOf(
                "打开TTS设置",
                "安装TTS引擎",
                "使用在线TTS",
                "使用文本显示"
            )
            
            TTSError.LANGUAGE_NOT_SUPPORTED -> listOf(
                "切换到支持的语言",
                "使用在线TTS",
                "使用文本显示"
            )
            
            TTSError.NETWORK_ERROR -> listOf(
                "检查网络连接",
                "使用离线TTS",
                "使用文本显示"
            )
            
            TTSError.UNKNOWN_ERROR -> listOf(
                "重启应用",
                "重新初始化TTS",
                "使用备用方案"
            )
        }
    }
}
