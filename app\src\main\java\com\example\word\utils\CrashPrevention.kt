package com.example.word.utils

import android.content.Context
import android.util.Log
import androidx.fragment.app.Fragment
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController

/**
 * 崩溃预防工具类
 * 提供安全的操作方法，避免常见的崩溃问题
 */
object CrashPrevention {
    
    private const val TAG = "CrashPrevention"
    
    /**
     * 安全地执行可能抛出异常的操作
     */
    inline fun <T> safeExecute(
        operation: () -> T,
        onError: (Exception) -> Unit = { Log.e(TAG, "Safe execute failed", it) },
        defaultValue: T? = null
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            onError(e)
            defaultValue
        }
    }
    
    /**
     * 安全地执行导航操作
     */
    fun Fragment.safeNavigate(
        actionId: Int,
        onError: (Exception) -> Unit = { Log.e(TAG, "Navigation failed", it) }
    ): Boolean {
        return safeExecute(
            operation = {
                if (isAdded && !isDetached && !isRemoving) {
                    findNavController().navigate(actionId)
                    true
                } else {
                    Log.w(TAG, "Fragment not in valid state for navigation")
                    false
                }
            },
            onError = onError,
            defaultValue = false
        ) ?: false
    }
    
    /**
     * 安全地获取NavController
     */
    fun Fragment.safeNavController(): NavController? {
        return safeExecute(
            operation = {
                if (isAdded && !isDetached) {
                    findNavController()
                } else {
                    null
                }
            },
            defaultValue = null
        )
    }
    
    /**
     * 安全地执行UI操作
     */
    fun Fragment.safeUIOperation(operation: () -> Unit) {
        safeExecute(
            operation = {
                if (isAdded && !isDetached && view != null) {
                    operation()
                }
            }
        )
    }
    
    /**
     * 安全地获取Context
     */
    fun Fragment.safeContext(): Context? {
        return safeExecute(
            operation = {
                if (isAdded && !isDetached) {
                    context
                } else {
                    null
                }
            },
            defaultValue = null
        )
    }
    
    /**
     * 检查Fragment是否处于安全状态
     */
    fun Fragment.isSafeState(): Boolean {
        return isAdded && !isDetached && !isRemoving && view != null
    }
    
    /**
     * 安全地执行需要Context的操作
     */
    inline fun <T> Fragment.withSafeContext(
        operation: (Context) -> T,
        onError: (Exception) -> Unit = { Log.e(TAG, "Context operation failed", it) }
    ): T? {
        val context = safeContext()
        return if (context != null) {
            safeExecute(
                operation = { operation(context) },
                onError = onError
            )
        } else {
            Log.w(TAG, "Context not available")
            null
        }
    }
    
    /**
     * 安全地执行数据库操作
     */
    suspend inline fun <T> safeDatabaseOperation(
        operation: suspend () -> T,
        onError: (Exception) -> Unit = { Log.e(TAG, "Database operation failed", it) },
        defaultValue: T? = null
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            onError(e)
            defaultValue
        }
    }
    
    /**
     * 安全地执行网络操作
     */
    suspend inline fun <T> safeNetworkOperation(
        operation: suspend () -> T,
        onError: (Exception) -> Unit = { Log.e(TAG, "Network operation failed", it) },
        defaultValue: T? = null
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            onError(e)
            defaultValue
        }
    }
    
    /**
     * 检查对象是否为null并记录警告
     */
    fun <T> T?.checkNotNull(name: String): T? {
        if (this == null) {
            Log.w(TAG, "$name is null")
        }
        return this
    }
    
    /**
     * 安全地转换类型
     */
    inline fun <reified T> Any?.safeCast(): T? {
        return safeExecute(
            operation = { this as? T },
            defaultValue = null
        )
    }
}
