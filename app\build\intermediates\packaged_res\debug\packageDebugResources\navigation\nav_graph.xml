<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.example.word.ui.home.HomeFragment"
        android:label="首页"
        tools:layout="@layout/fragment_home">

        <action
            android:id="@+id/action_home_to_vocabulary"
            app:destination="@id/navigation_vocabulary" />

        <action
            android:id="@+id/action_home_to_quiz"
            app:destination="@id/navigation_quiz" />

        <action
            android:id="@+id/action_home_to_progress"
            app:destination="@id/navigation_progress" />

        <action
            android:id="@+id/action_home_to_phrases"
            app:destination="@id/navigation_phrases" />

        <action
            android:id="@+id/action_home_to_essays"
            app:destination="@id/navigation_essay" />
    </fragment>

    <fragment
        android:id="@+id/navigation_vocabulary"
        android:name="com.example.word.ui.vocabulary.VocabularyFragment"
        android:label="@string/vocabulary"
        tools:layout="@layout/fragment_vocabulary" />

    <fragment
        android:id="@+id/navigation_phrases"
        android:name="com.example.word.ui.phrases.PhrasesFragment"
        android:label="@string/phrases"
        tools:layout="@layout/fragment_phrases" />

    <fragment
        android:id="@+id/navigation_essay"
        android:name="com.example.word.ui.essay.EssayFragment"
        android:label="@string/essay"
        tools:layout="@layout/fragment_essay" />

    <fragment
        android:id="@+id/navigation_quiz"
        android:name="com.example.word.ui.quiz.QuizFragment"
        android:label="@string/quiz"
        tools:layout="@layout/fragment_quiz" />

    <fragment
        android:id="@+id/navigation_progress"
        android:name="com.example.word.ui.progress.ProgressFragment"
        android:label="@string/progress"
        tools:layout="@layout/fragment_progress" />

</navigation>
