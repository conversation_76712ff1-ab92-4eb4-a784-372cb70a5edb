  black android.R.color  darker_gray android.R.color  holo_blue_bright android.R.color  simple_spinner_dropdown_item android.R.layout  simple_spinner_item android.R.layout  AlarmManager android.app  Application android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  TimePickerDialog android.app  ActivityMainBinding android.app.Activity  BottomNavigationView android.app.Activity  DatabaseInitializer android.app.Activity  	Exception android.app.Activity  NavHostFragment android.app.Activity  R android.app.Activity  initializeDatabase android.app.Activity  launch android.app.Activity  layoutInflater android.app.Activity  lifecycleScope android.app.Activity  onCreate android.app.Activity  setupWithNavController android.app.Activity  INTERVAL_DAY android.app.AlarmManager  
RTC_WAKEUP android.app.AlarmManager  cancel android.app.AlarmManager  setRepeating android.app.AlarmManager  show android.app.Dialog  apply android.app.NotificationChannel  description android.app.NotificationChannel  IMPORTANCE_DEFAULT android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  getBroadcast android.app.PendingIntent  OnTimeSetListener android.app.TimePickerDialog  show android.app.TimePickerDialog  <SAM-CONSTRUCTOR> .android.app.TimePickerDialog.OnTimeSetListener  BroadcastReceiver android.content  Context android.content  Intent android.content  SharedPreferences android.content  StudyNotificationManager !android.content.BroadcastReceiver  showStudyReminderNotification !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  ActivityMainBinding android.content.Context  BottomNavigationView android.content.Context  DatabaseInitializer android.content.Context  	Exception android.content.Context  MODE_PRIVATE android.content.Context  NOTIFICATION_SERVICE android.content.Context  NavHostFragment android.content.Context  R android.content.Context  applicationContext android.content.Context  assets android.content.Context  cacheDir android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  initializeDatabase android.content.Context  launch android.content.Context  lifecycleScope android.content.Context  setupWithNavController android.content.Context  ActivityMainBinding android.content.ContextWrapper  BottomNavigationView android.content.ContextWrapper  DatabaseInitializer android.content.ContextWrapper  	Exception android.content.ContextWrapper  NavHostFragment android.content.ContextWrapper  R android.content.ContextWrapper  initializeDatabase android.content.ContextWrapper  launch android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  setupWithNavController android.content.ContextWrapper  OnClickListener android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  apply android.content.Intent  flags android.content.Intent  putExtra android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  open  android.content.res.AssetManager  AttributeSet android.graphics  Canvas android.graphics  Context android.graphics  
ContextCompat android.graphics  Float android.graphics  Int android.graphics  JvmOverloads android.graphics  List android.graphics  Paint android.graphics  Path android.graphics  RectF android.graphics  String android.graphics  StudyDataPoint android.graphics  View android.graphics  android android.graphics  apply android.graphics  context android.graphics  	emptyList android.graphics  format android.graphics  indices android.graphics  
isNotEmpty android.graphics  max android.graphics  maxOf android.graphics  min android.graphics  minOf android.graphics  drawArc android.graphics.Canvas  
drawCircle android.graphics.Canvas  drawLine android.graphics.Canvas  drawPath android.graphics.Canvas  drawText android.graphics.Canvas  ANTI_ALIAS_FLAG android.graphics.Paint  Align android.graphics.Paint  Cap android.graphics.Paint  
ContextCompat android.graphics.Paint  Join android.graphics.Paint  Paint android.graphics.Paint  Style android.graphics.Paint  alpha android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  context android.graphics.Paint  	strokeCap android.graphics.Paint  
strokeJoin android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  	textAlign android.graphics.Paint  textSize android.graphics.Paint  CENTER android.graphics.Paint.Align  RIGHT android.graphics.Paint.Align  ROUND android.graphics.Paint.Cap  ROUND android.graphics.Paint.Join  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  lineTo android.graphics.Path  moveTo android.graphics.Path  AudioAttributes 
android.media  MediaPlayer 
android.media  Builder android.media.AudioAttributes  CONTENT_TYPE_SPEECH android.media.AudioAttributes  USAGE_MEDIA android.media.AudioAttributes  build %android.media.AudioAttributes.Builder  setContentType %android.media.AudioAttributes.Builder  setUsage %android.media.AudioAttributes.Builder  AudioAttributes android.media.MediaPlayer  Log android.media.MediaPlayer  OnCompletionListener android.media.MediaPlayer  OnErrorListener android.media.MediaPlayer  OnPreparedListener android.media.MediaPlayer  TAG android.media.MediaPlayer  apply android.media.MediaPlayer  prepareAsync android.media.MediaPlayer  release android.media.MediaPlayer  setAudioAttributes android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  setOnErrorListener android.media.MediaPlayer  setOnPreparedListener android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  <SAM-CONSTRUCTOR> )android.media.MediaPlayer.OnErrorListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  TextToSpeech android.speech.tts  LANG_MISSING_DATA android.speech.tts.TextToSpeech  LANG_NOT_SUPPORTED android.speech.tts.TextToSpeech  OnInitListener android.speech.tts.TextToSpeech  QUEUE_FLUSH android.speech.tts.TextToSpeech  SUCCESS android.speech.tts.TextToSpeech  let android.speech.tts.TextToSpeech  setLanguage android.speech.tts.TextToSpeech  
setSpeechRate android.speech.tts.TextToSpeech  shutdown android.speech.tts.TextToSpeech  speak android.speech.tts.TextToSpeech  stop android.speech.tts.TextToSpeech  Editable android.text  clear android.text.Editable  toString android.text.Editable  AttributeSet android.util  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  	ViewGroup android.view  ActivityMainBinding  android.view.ContextThemeWrapper  BottomNavigationView  android.view.ContextThemeWrapper  DatabaseInitializer  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  NavHostFragment  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  initializeDatabase  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  setupWithNavController  android.view.ContextThemeWrapper  from android.view.LayoutInflater  
ContextCompat android.view.View  GONE android.view.View  OnClickListener android.view.View  Paint android.view.View  Path android.view.View  RectF android.view.View  String android.view.View  VISIBLE android.view.View  alpha android.view.View  android android.view.View  apply android.view.View  context android.view.View  	emptyList android.view.View  format android.view.View  height android.view.View  id android.view.View  indices android.view.View  
invalidate android.view.View  	isEnabled android.view.View  
isNotEmpty android.view.View  max android.view.View  maxOf android.view.View  min android.view.View  minOf android.view.View  onDraw android.view.View  setBackgroundResource android.view.View  setOnClickListener android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  context android.view.ViewGroup  AdapterView android.widget  ArrayAdapter android.widget  Button android.widget  	ImageView android.widget  LinearLayout android.widget  ProgressBar android.widget  RadioButton android.widget  
RadioGroup android.widget  
ScrollView android.widget  Spinner android.widget  TextView android.widget  Toast android.widget  adapter android.widget.AbsSpinner  setSelection android.widget.AbsSpinner  OnItemSelectedListener android.widget.AdapterView  onItemSelectedListener android.widget.AdapterView  android android.widget.ArrayAdapter  apply android.widget.ArrayAdapter  setDropDownViewResource android.widget.ArrayAdapter  	isEnabled android.widget.Button  setOnClickListener android.widget.Button  text android.widget.Button  OnCheckedChangeListener android.widget.CompoundButton  	isChecked android.widget.CompoundButton  setOnCheckedChangeListener android.widget.CompoundButton  <SAM-CONSTRUCTOR> 5android.widget.CompoundButton.OnCheckedChangeListener  alpha android.widget.ImageView  setImageResource android.widget.ImageView  setOnClickListener android.widget.ImageView  alpha android.widget.LinearLayout  setOnClickListener android.widget.LinearLayout  
visibility android.widget.LinearLayout  max android.widget.ProgressBar  progress android.widget.ProgressBar  
visibility android.widget.ProgressBar  id android.widget.RadioButton  	isChecked android.widget.RadioButton  OnCheckedChangeListener android.widget.RadioGroup  setOnCheckedChangeListener android.widget.RadioGroup  <SAM-CONSTRUCTOR> 1android.widget.RadioGroup.OnCheckedChangeListener  adapter android.widget.Spinner  onItemSelectedListener android.widget.Spinner  setSelection android.widget.Spinner  alpha android.widget.TextView  setBackgroundResource android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  BottomNavigationView #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  DatabaseInitializer #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  NavHostFragment #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  initializeDatabase #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  setupWithNavController #androidx.activity.ComponentActivity  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  DatabaseInitializer -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  initializeDatabase -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  lifecycleScope -androidx.activity.ComponentActivity.Companion  setupWithNavController -androidx.activity.ComponentActivity.Companion  AlertDialog androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  AppCompatDelegate androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  dismiss "androidx.appcompat.app.AlertDialog  show "androidx.appcompat.app.AlertDialog  create *androidx.appcompat.app.AlertDialog.Builder  
setCancelable *androidx.appcompat.app.AlertDialog.Builder  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setNegativeButton *androidx.appcompat.app.AlertDialog.Builder  setNeutralButton *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  BottomNavigationView (androidx.appcompat.app.AppCompatActivity  DatabaseInitializer (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  NavHostFragment (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  initializeDatabase (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  setupWithNavController (androidx.appcompat.app.AppCompatActivity  MODE_NIGHT_FOLLOW_SYSTEM (androidx.appcompat.app.AppCompatDelegate  
MODE_NIGHT_NO (androidx.appcompat.app.AppCompatDelegate  MODE_NIGHT_YES (androidx.appcompat.app.AppCompatDelegate  setDefaultNightMode (androidx.appcompat.app.AppCompatDelegate  
SearchView androidx.appcompat.widget  text +androidx.appcompat.widget.AppCompatEditText  OnQueryTextListener $androidx.appcompat.widget.SearchView  query $androidx.appcompat.widget.SearchView  setOnQueryTextListener $androidx.appcompat.widget.SearchView  ConstraintLayout  androidx.constraintlayout.widget  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  ActivityMainBinding #androidx.core.app.ComponentActivity  BottomNavigationView #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DatabaseInitializer #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  NavHostFragment #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  initializeDatabase #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  setupWithNavController #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_DEFAULT $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  getColor #androidx.core.content.ContextCompat  NestedScrollView androidx.core.widget  Fragment androidx.fragment.app  FragmentManager androidx.fragment.app  
viewModels androidx.fragment.app  AchievementAdapter androidx.fragment.app.Fragment  AppCompatDelegate androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Calendar androidx.fragment.app.Fragment  Context androidx.fragment.app.Fragment  EssayTemplateAdapter androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  FragmentEssayBinding androidx.fragment.app.Fragment  FragmentHomeBinding androidx.fragment.app.Fragment  FragmentPhrasesBinding androidx.fragment.app.Fragment  FragmentProgressBinding androidx.fragment.app.Fragment  FragmentQuizBinding androidx.fragment.app.Fragment  FragmentSettingsBinding androidx.fragment.app.Fragment  FragmentVocabularyBinding androidx.fragment.app.Fragment  GameificationManager androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  LinearLayoutManager androidx.fragment.app.Fragment  Locale androidx.fragment.app.Fragment  Long androidx.fragment.app.Fragment  OnlineTTSHelper androidx.fragment.app.Fragment  
PhraseAdapter androidx.fragment.app.Fragment  QuestionType androidx.fragment.app.Fragment  
QuizConfig androidx.fragment.app.Fragment  
QuizSource androidx.fragment.app.Fragment  	QuizState androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  SimpleDateFormat androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  StudyDataPoint androidx.fragment.app.Fragment  StudyNotificationManager androidx.fragment.app.Fragment  
TTSManager androidx.fragment.app.Fragment  TimePickerDialog androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  WordAdapter androidx.fragment.app.Fragment  achievementAdapter androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  androidx androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  arrayOf androidx.fragment.app.Fragment  cancelStudyReminder androidx.fragment.app.Fragment  createNotificationChannel androidx.fragment.app.Fragment  downTo androidx.fragment.app.Fragment  essayTemplateAdapter androidx.fragment.app.Fragment  essayViewModel androidx.fragment.app.Fragment  findNavController androidx.fragment.app.Fragment  format androidx.fragment.app.Fragment  formatStudyTime androidx.fragment.app.Fragment  gamificationManager androidx.fragment.app.Fragment  getTTSAudio androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  
isInitialized androidx.fragment.app.Fragment  
isNotEmpty androidx.fragment.app.Fragment  
isNullOrBlank androidx.fragment.app.Fragment  java androidx.fragment.app.Fragment  launch androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  lifecycleScope androidx.fragment.app.Fragment  
mutableListOf androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  
phraseAdapter androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  random androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  reversed androidx.fragment.app.Fragment  run androidx.fragment.app.Fragment  scheduleStudyReminder androidx.fragment.app.Fragment  sharedPreferences androidx.fragment.app.Fragment  
simpleAPITest androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  updateDailyGoal androidx.fragment.app.Fragment  updateDifficultyStats androidx.fragment.app.Fragment  updateLevelInfo androidx.fragment.app.Fragment  viewLifecycleOwner androidx.fragment.app.Fragment  
viewModels androidx.fragment.app.Fragment  wordAdapter androidx.fragment.app.Fragment  
wordViewModel androidx.fragment.app.Fragment  widget &androidx.fragment.app.Fragment.android  AdapterView -androidx.fragment.app.Fragment.android.widget  OnItemSelectedListener 9androidx.fragment.app.Fragment.android.widget.AdapterView  	appcompat 'androidx.fragment.app.Fragment.androidx  widget 1androidx.fragment.app.Fragment.androidx.appcompat  
SearchView 8androidx.fragment.app.Fragment.androidx.appcompat.widget  OnQueryTextListener Candroidx.fragment.app.Fragment.androidx.appcompat.widget.SearchView  ActivityMainBinding &androidx.fragment.app.FragmentActivity  BottomNavigationView &androidx.fragment.app.FragmentActivity  DatabaseInitializer &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  NavHostFragment &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  initializeDatabase &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  setupWithNavController &androidx.fragment.app.FragmentActivity  supportFragmentManager &androidx.fragment.app.FragmentActivity  findFragmentById %androidx.fragment.app.FragmentManager  AndroidViewModel androidx.lifecycle  LifecycleCoroutineScope androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  launch *androidx.lifecycle.LifecycleCoroutineScope  observe androidx.lifecycle.LiveData  observeForever androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  Boolean androidx.lifecycle.ViewModel  Calendar androidx.lifecycle.ViewModel  DailyStudyRecord androidx.lifecycle.ViewModel  DifficultyStats androidx.lifecycle.ViewModel  
EssayTemplate androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  GameificationManager androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Math androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  Phrase androidx.lifecycle.ViewModel  QuestionType androidx.lifecycle.ViewModel  QuizProgress androidx.lifecycle.ViewModel  QuizQuestion androidx.lifecycle.ViewModel  
QuizResult androidx.lifecycle.ViewModel  
QuizSource androidx.lifecycle.ViewModel  	QuizState androidx.lifecycle.ViewModel  Random androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  StudySession androidx.lifecycle.ViewModel  StudyStatistics androidx.lifecycle.ViewModel  System androidx.lifecycle.ViewModel  VocabularyMastery androidx.lifecycle.ViewModel  Word androidx.lifecycle.ViewModel  WordDatabase androidx.lifecycle.ViewModel  WordRepository androidx.lifecycle.ViewModel  
_achievements androidx.lifecycle.ViewModel  _currentQuizIndex androidx.lifecycle.ViewModel  _currentWord androidx.lifecycle.ViewModel  
_errorMessage androidx.lifecycle.ViewModel  _filteredPhrases androidx.lifecycle.ViewModel  _filteredTemplates androidx.lifecycle.ViewModel  
_isLoading androidx.lifecycle.ViewModel  _phraseCategories androidx.lifecycle.ViewModel  _phraseTypes androidx.lifecycle.ViewModel  
_quizProgress androidx.lifecycle.ViewModel  
_quizScore androidx.lifecycle.ViewModel  
_quizState androidx.lifecycle.ViewModel  
_quizWords androidx.lifecycle.ViewModel  _searchResults androidx.lifecycle.ViewModel  _selectedCategory androidx.lifecycle.ViewModel  _selectedDifficulty androidx.lifecycle.ViewModel  
_selectedType androidx.lifecycle.ViewModel  
_studyHistory androidx.lifecycle.ViewModel  
_studyMode androidx.lifecycle.ViewModel  _studyStatistics androidx.lifecycle.ViewModel  _templateCategories androidx.lifecycle.ViewModel  _templateTypes androidx.lifecycle.ViewModel  _vocabularyMastery androidx.lifecycle.ViewModel  _wordsForReview androidx.lifecycle.ViewModel  
allPhrases androidx.lifecycle.ViewModel  allTemplates androidx.lifecycle.ViewModel  any androidx.lifecycle.ViewModel  calculateNewAccuracy androidx.lifecycle.ViewModel  calculateNextLevelExp androidx.lifecycle.ViewModel  
coerceAtLeast androidx.lifecycle.ViewModel  coerceAtMost androidx.lifecycle.ViewModel  coerceIn androidx.lifecycle.ViewModel  correctAnswers androidx.lifecycle.ViewModel  createConsecutiveAchievements androidx.lifecycle.ViewModel  createExperienceAchievements androidx.lifecycle.ViewModel  createStudyDaysAchievements androidx.lifecycle.ViewModel  createVocabularyAchievements androidx.lifecycle.ViewModel  currentQuestionIndex androidx.lifecycle.ViewModel  distinct androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  equals androidx.lifecycle.ViewModel  filter androidx.lifecycle.ViewModel  first androidx.lifecycle.ViewModel  generateNextQuestion androidx.lifecycle.ViewModel  getDatabase androidx.lifecycle.ViewModel  getDayStartTime androidx.lifecycle.ViewModel  getTodayStartTime androidx.lifecycle.ViewModel  getWeekStartTime androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  
isNotEmpty androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  map androidx.lifecycle.ViewModel  maxOf androidx.lifecycle.ViewModel  
mutableListOf androidx.lifecycle.ViewModel  mutableMapOf androidx.lifecycle.ViewModel  nextBoolean androidx.lifecycle.ViewModel  nextInt androidx.lifecycle.ViewModel  plus androidx.lifecycle.ViewModel  	quizWords androidx.lifecycle.ViewModel  recordStudySession androidx.lifecycle.ViewModel  replace androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  reversed androidx.lifecycle.ViewModel  set androidx.lifecycle.ViewModel  shuffled androidx.lifecycle.ViewModel  sortedByDescending androidx.lifecycle.ViewModel  split androidx.lifecycle.ViewModel  	startTime androidx.lifecycle.ViewModel  take androidx.lifecycle.ViewModel  trim androidx.lifecycle.ViewModel  until androidx.lifecycle.ViewModel  updateSpacedRepetition androidx.lifecycle.ViewModel  updateUserProgress androidx.lifecycle.ViewModel  	uppercase androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  Achievement 1androidx.lifecycle.ViewModel.GameificationManager  
NavController androidx.navigation  navigate !androidx.navigation.NavController  NavHostFragment androidx.navigation.fragment  findNavController androidx.navigation.fragment  
navController ,androidx.navigation.fragment.NavHostFragment  setupWithNavController androidx.navigation.ui  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  ItemAchievementBinding (androidx.recyclerview.widget.ListAdapter  ItemEssayTemplateBinding (androidx.recyclerview.widget.ListAdapter  ItemPhraseBinding (androidx.recyclerview.widget.ListAdapter  ItemWordBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  android (androidx.recyclerview.widget.ListAdapter  apply (androidx.recyclerview.widget.ListAdapter  getTypeBackground (androidx.recyclerview.widget.ListAdapter  
isNotEmpty (androidx.recyclerview.widget.ListAdapter  onBookmarkClick (androidx.recyclerview.widget.ListAdapter  
onPhraseClick (androidx.recyclerview.widget.ListAdapter  onSpeakClick (androidx.recyclerview.widget.ListAdapter  onTemplateClick (androidx.recyclerview.widget.ListAdapter  onWordClick (androidx.recyclerview.widget.ListAdapter  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  achievementAdapter )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  essayTemplateAdapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
phraseAdapter )androidx.recyclerview.widget.RecyclerView  requireContext )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  wordAdapter )androidx.recyclerview.widget.RecyclerView  ItemAchievementBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemEssayTemplateBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemPhraseBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemWordBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  getTypeBackground 1androidx.recyclerview.widget.RecyclerView.Adapter  
isNotEmpty 1androidx.recyclerview.widget.RecyclerView.Adapter  onBookmarkClick 1androidx.recyclerview.widget.RecyclerView.Adapter  
onPhraseClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onSpeakClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onTemplateClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onWordClick 1androidx.recyclerview.widget.RecyclerView.Adapter  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  android 4androidx.recyclerview.widget.RecyclerView.ViewHolder  apply 4androidx.recyclerview.widget.RecyclerView.ViewHolder  getTypeBackground 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
isNotEmpty 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onBookmarkClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onPhraseClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onSpeakClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onTemplateClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onWordClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Boolean 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
EssayTemplate 
androidx.room  Float 
androidx.room  Insert 
androidx.room  Int 
androidx.room  List 
androidx.room  LiveData 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  Phrase 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  String 
androidx.room  StudySession 
androidx.room  System 
androidx.room  Update 
androidx.room  UserProgress 
androidx.room  Word 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  Callback androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  EssayTemplateDao androidx.room.RoomDatabase  	PhraseDao androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  RoomDatabase androidx.room.RoomDatabase  StudySessionDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  UserProgressDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  WordDao androidx.room.RoomDatabase  WordDatabase androidx.room.RoomDatabase  WordDatabaseCallback androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  addCallback "androidx.room.RoomDatabase.Builder  build "androidx.room.RoomDatabase.Builder  onCreate #androidx.room.RoomDatabase.Callback  Room $androidx.room.RoomDatabase.Companion  WordDatabase $androidx.room.RoomDatabase.Companion  WordDatabaseCallback $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  Callback 'androidx.room.RoomDatabase.RoomDatabase  SupportSQLiteDatabase androidx.sqlite.db  ActivityMainBinding com.example.word  AppCompatActivity com.example.word  BottomNavigationView com.example.word  Bundle com.example.word  DatabaseInitializer com.example.word  	Exception com.example.word  MainActivity com.example.word  NavHostFragment com.example.word  R com.example.word  initializeDatabase com.example.word  launch com.example.word  setupWithNavController com.example.word  ActivityMainBinding com.example.word.MainActivity  DatabaseInitializer com.example.word.MainActivity  R com.example.word.MainActivity  binding com.example.word.MainActivity  findViewById com.example.word.MainActivity  initializeDatabase com.example.word.MainActivity  launch com.example.word.MainActivity  layoutInflater com.example.word.MainActivity  lifecycleScope com.example.word.MainActivity  setContentView com.example.word.MainActivity  setupNavigation com.example.word.MainActivity  setupWithNavController com.example.word.MainActivity  supportFragmentManager com.example.word.MainActivity  type_argumentative com.example.word.R.color  type_default com.example.word.R.color  type_descriptive com.example.word.R.color  type_narrative com.example.word.R.color  type_sentence_pattern com.example.word.R.color  type_transition com.example.word.R.color  ic_achievement_locked com.example.word.R.drawable  ic_achievement_unlocked com.example.word.R.drawable  ic_bookmark_border com.example.word.R.drawable  ic_bookmark_filled com.example.word.R.drawable  
ic_level_1 com.example.word.R.drawable  
ic_level_2 com.example.word.R.drawable  
ic_level_3 com.example.word.R.drawable  
ic_level_4 com.example.word.R.drawable  
ic_level_5 com.example.word.R.drawable  ic_level_master com.example.word.R.drawable  ic_notification com.example.word.R.drawable  	ic_streak com.example.word.R.drawable  part_of_speech_background com.example.word.R.drawable  type_argumentative_background com.example.word.R.drawable  type_descriptive_background com.example.word.R.drawable  type_narrative_background com.example.word.R.drawable   type_sentence_pattern_background com.example.word.R.drawable  type_transition_background com.example.word.R.drawable  action_home_to_essays com.example.word.R.id  action_home_to_phrases com.example.word.R.id  action_home_to_progress com.example.word.R.id  action_home_to_quiz com.example.word.R.id  action_home_to_vocabulary com.example.word.R.id  bottom_navigation com.example.word.R.id  nav_host_fragment com.example.word.R.id  Boolean com.example.word.data.dao  Dao com.example.word.data.dao  Delete com.example.word.data.dao  
EssayTemplate com.example.word.data.dao  EssayTemplateDao com.example.word.data.dao  Float com.example.word.data.dao  Insert com.example.word.data.dao  Int com.example.word.data.dao  List com.example.word.data.dao  LiveData com.example.word.data.dao  Long com.example.word.data.dao  OnConflictStrategy com.example.word.data.dao  Phrase com.example.word.data.dao  	PhraseDao com.example.word.data.dao  Query com.example.word.data.dao  String com.example.word.data.dao  StudySession com.example.word.data.dao  StudySessionDao com.example.word.data.dao  System com.example.word.data.dao  Update com.example.word.data.dao  UserProgress com.example.word.data.dao  UserProgressDao com.example.word.data.dao  Word com.example.word.data.dao  WordDao com.example.word.data.dao  OnConflictStrategy *com.example.word.data.dao.EssayTemplateDao  deleteAllTemplates *com.example.word.data.dao.EssayTemplateDao  getAllCategories *com.example.word.data.dao.EssayTemplateDao  getAllTemplateTypes *com.example.word.data.dao.EssayTemplateDao  getAllTemplates *com.example.word.data.dao.EssayTemplateDao  getBookmarkedTemplates *com.example.word.data.dao.EssayTemplateDao  getMostUsedTemplates *com.example.word.data.dao.EssayTemplateDao  getTemplateById *com.example.word.data.dao.EssayTemplateDao  getTemplatesByCategory *com.example.word.data.dao.EssayTemplateDao  getTemplatesByDifficulty *com.example.word.data.dao.EssayTemplateDao  getTemplatesByType *com.example.word.data.dao.EssayTemplateDao  incrementUsageCount *com.example.word.data.dao.EssayTemplateDao  insertTemplate *com.example.word.data.dao.EssayTemplateDao  insertTemplates *com.example.word.data.dao.EssayTemplateDao  searchTemplates *com.example.word.data.dao.EssayTemplateDao  updateBookmarkStatus *com.example.word.data.dao.EssayTemplateDao  updateTemplate *com.example.word.data.dao.EssayTemplateDao  OnConflictStrategy #com.example.word.data.dao.PhraseDao  deleteAllPhrases #com.example.word.data.dao.PhraseDao  getAllCategories #com.example.word.data.dao.PhraseDao  getAllPhraseTypes #com.example.word.data.dao.PhraseDao  
getAllPhrases #com.example.word.data.dao.PhraseDao  getBookmarkedPhrases #com.example.word.data.dao.PhraseDao  
getPhraseById #com.example.word.data.dao.PhraseDao  getPhrasesByCategory #com.example.word.data.dao.PhraseDao  getPhrasesByDifficulty #com.example.word.data.dao.PhraseDao  getPhrasesByType #com.example.word.data.dao.PhraseDao  getRandomPhrases #com.example.word.data.dao.PhraseDao  insertPhrase #com.example.word.data.dao.PhraseDao  
insertPhrases #com.example.word.data.dao.PhraseDao  
searchPhrases #com.example.word.data.dao.PhraseDao  updateBookmarkStatus #com.example.word.data.dao.PhraseDao  updatePhrase #com.example.word.data.dao.PhraseDao  updateStudyStats #com.example.word.data.dao.PhraseDao  getAllStudySessions )com.example.word.data.dao.StudySessionDao  getAverageResponseTime )com.example.word.data.dao.StudySessionDao  getCorrectAnswersCount )com.example.word.data.dao.StudySessionDao  getStudyCountByDateRange )com.example.word.data.dao.StudySessionDao  getStudySessionsByDateRange )com.example.word.data.dao.StudySessionDao  getStudySessionsByType )com.example.word.data.dao.StudySessionDao  insertStudySession )com.example.word.data.dao.StudySessionDao  OnConflictStrategy )com.example.word.data.dao.UserProgressDao  System )com.example.word.data.dao.UserProgressDao  
addExperience )com.example.word.data.dao.UserProgressDao  addStudyTime )com.example.word.data.dao.UserProgressDao  getUserProgress )com.example.word.data.dao.UserProgressDao  getUserProgressSync )com.example.word.data.dao.UserProgressDao  insertOrUpdateUserProgress )com.example.word.data.dao.UserProgressDao  OnConflictStrategy !com.example.word.data.dao.WordDao  deleteAllWords !com.example.word.data.dao.WordDao  getAllWords !com.example.word.data.dao.WordDao  getAverageAccuracy !com.example.word.data.dao.WordDao  getBookmarkedWords !com.example.word.data.dao.WordDao  getBookmarkedWordsCount !com.example.word.data.dao.WordDao  getRandomWords !com.example.word.data.dao.WordDao  getStudiedWordsCount !com.example.word.data.dao.WordDao  getWordById !com.example.word.data.dao.WordDao  getWordsByDifficulty !com.example.word.data.dao.WordDao  getWordsByFrequencyRange !com.example.word.data.dao.WordDao  getWordsByPartOfSpeech !com.example.word.data.dao.WordDao  getWordsForReview !com.example.word.data.dao.WordDao  
insertWord !com.example.word.data.dao.WordDao  insertWords !com.example.word.data.dao.WordDao  searchWords !com.example.word.data.dao.WordDao  updateBookmarkStatus !com.example.word.data.dao.WordDao  updateReviewInfo !com.example.word.data.dao.WordDao  updateStudyStats !com.example.word.data.dao.WordDao  
updateWord !com.example.word.data.dao.WordDao  Boolean com.example.word.data.database  	ByteArray com.example.word.data.database  Charsets com.example.word.data.database  Context com.example.word.data.database  Database com.example.word.data.database  DatabaseInitializer com.example.word.data.database  Dispatchers com.example.word.data.database  
EssayTemplate com.example.word.data.database  EssayTemplateDao com.example.word.data.database  	Exception com.example.word.data.database  IOException com.example.word.data.database  Int com.example.word.data.database  
JSONObject com.example.word.data.database  KEY_INITIALIZED com.example.word.data.database  KEY_VERSION com.example.word.data.database  List com.example.word.data.database  
PREFS_NAME com.example.word.data.database  Phrase com.example.word.data.database  	PhraseDao com.example.word.data.database  Room com.example.word.data.database  RoomDatabase com.example.word.data.database  String com.example.word.data.database  StudySession com.example.word.data.database  StudySessionDao com.example.word.data.database  SupportSQLiteDatabase com.example.word.data.database  UserProgress com.example.word.data.database  UserProgressDao com.example.word.data.database  VocabularyDataProvider com.example.word.data.database  VocabularyJsonLoader com.example.word.data.database  VocabularyMetadata com.example.word.data.database  Volatile com.example.word.data.database  Word com.example.word.data.database  WordDao com.example.word.data.database  WordDatabase com.example.word.data.database  WordDatabaseCallback com.example.word.data.database  clearOldData com.example.word.data.database  com com.example.word.data.database  databaseBuilder com.example.word.data.database  getCET4Phrases com.example.word.data.database  getCET4Words com.example.word.data.database  getDatabase com.example.word.data.database  getEssayTemplates com.example.word.data.database  getVocabularyMetadata com.example.word.data.database  invoke com.example.word.data.database  
isNotEmpty com.example.word.data.database  java com.example.word.data.database  listOf com.example.word.data.database  loadEssayTemplatesFromJson com.example.word.data.database  loadPhrasesFromJson com.example.word.data.database  loadVocabularyData com.example.word.data.database  loadWordsFromJson com.example.word.data.database  
mutableListOf com.example.word.data.database  plus com.example.word.data.database  synchronized com.example.word.data.database  until com.example.word.data.database  withContext com.example.word.data.database  Context 2com.example.word.data.database.DatabaseInitializer  Dispatchers 2com.example.word.data.database.DatabaseInitializer  KEY_INITIALIZED 2com.example.word.data.database.DatabaseInitializer  KEY_VERSION 2com.example.word.data.database.DatabaseInitializer  
PREFS_NAME 2com.example.word.data.database.DatabaseInitializer  VocabularyDataProvider 2com.example.word.data.database.DatabaseInitializer  VocabularyJsonLoader 2com.example.word.data.database.DatabaseInitializer  WordDatabase 2com.example.word.data.database.DatabaseInitializer  clearOldData 2com.example.word.data.database.DatabaseInitializer  com 2com.example.word.data.database.DatabaseInitializer  getCET4Phrases 2com.example.word.data.database.DatabaseInitializer  getCET4Words 2com.example.word.data.database.DatabaseInitializer  getDatabase 2com.example.word.data.database.DatabaseInitializer  getEssayTemplates 2com.example.word.data.database.DatabaseInitializer  getVocabularyMetadata 2com.example.word.data.database.DatabaseInitializer  initializeDatabase 2com.example.word.data.database.DatabaseInitializer  
isNotEmpty 2com.example.word.data.database.DatabaseInitializer  loadEssayTemplatesFromJson 2com.example.word.data.database.DatabaseInitializer  loadPhrasesFromJson 2com.example.word.data.database.DatabaseInitializer  loadVocabularyData 2com.example.word.data.database.DatabaseInitializer  loadWordsFromJson 2com.example.word.data.database.DatabaseInitializer  withContext 2com.example.word.data.database.DatabaseInitializer  Callback +com.example.word.data.database.RoomDatabase  
EssayTemplate 5com.example.word.data.database.VocabularyDataProvider  Phrase 5com.example.word.data.database.VocabularyDataProvider  Word 5com.example.word.data.database.VocabularyDataProvider  getAcademicWords 5com.example.word.data.database.VocabularyDataProvider  getAdvancedWords 5com.example.word.data.database.VocabularyDataProvider  
getBasicWords 5com.example.word.data.database.VocabularyDataProvider  getCET4Phrases 5com.example.word.data.database.VocabularyDataProvider  getCET4Words 5com.example.word.data.database.VocabularyDataProvider  getCommonWords 5com.example.word.data.database.VocabularyDataProvider  getEssayTemplates 5com.example.word.data.database.VocabularyDataProvider  getHighFrequencyWords 5com.example.word.data.database.VocabularyDataProvider  getIntermediateWords 5com.example.word.data.database.VocabularyDataProvider  listOf 5com.example.word.data.database.VocabularyDataProvider  plus 5com.example.word.data.database.VocabularyDataProvider  	ByteArray 3com.example.word.data.database.VocabularyJsonLoader  Charsets 3com.example.word.data.database.VocabularyJsonLoader  
EssayTemplate 3com.example.word.data.database.VocabularyJsonLoader  
JSONObject 3com.example.word.data.database.VocabularyJsonLoader  Phrase 3com.example.word.data.database.VocabularyJsonLoader  String 3com.example.word.data.database.VocabularyJsonLoader  VocabularyDataProvider 3com.example.word.data.database.VocabularyJsonLoader  VocabularyMetadata 3com.example.word.data.database.VocabularyJsonLoader  Word 3com.example.word.data.database.VocabularyJsonLoader  getCET4Phrases 3com.example.word.data.database.VocabularyJsonLoader  getCET4Words 3com.example.word.data.database.VocabularyJsonLoader  getEssayTemplates 3com.example.word.data.database.VocabularyJsonLoader  getVocabularyMetadata 3com.example.word.data.database.VocabularyJsonLoader  invoke 3com.example.word.data.database.VocabularyJsonLoader  loadEssayTemplatesFromJson 3com.example.word.data.database.VocabularyJsonLoader  loadJsonFromAssets 3com.example.word.data.database.VocabularyJsonLoader  loadPhrasesFromJson 3com.example.word.data.database.VocabularyJsonLoader  loadWordsFromJson 3com.example.word.data.database.VocabularyJsonLoader  
mutableListOf 3com.example.word.data.database.VocabularyJsonLoader  until 3com.example.word.data.database.VocabularyJsonLoader  version 1com.example.word.data.database.VocabularyMetadata  	Companion +com.example.word.data.database.WordDatabase  Context +com.example.word.data.database.WordDatabase  EssayTemplateDao +com.example.word.data.database.WordDatabase  INSTANCE +com.example.word.data.database.WordDatabase  	PhraseDao +com.example.word.data.database.WordDatabase  Room +com.example.word.data.database.WordDatabase  RoomDatabase +com.example.word.data.database.WordDatabase  StudySessionDao +com.example.word.data.database.WordDatabase  SupportSQLiteDatabase +com.example.word.data.database.WordDatabase  UserProgressDao +com.example.word.data.database.WordDatabase  Volatile +com.example.word.data.database.WordDatabase  WordDao +com.example.word.data.database.WordDatabase  WordDatabase +com.example.word.data.database.WordDatabase  WordDatabaseCallback +com.example.word.data.database.WordDatabase  databaseBuilder +com.example.word.data.database.WordDatabase  essayTemplateDao +com.example.word.data.database.WordDatabase  getDatabase +com.example.word.data.database.WordDatabase  java +com.example.word.data.database.WordDatabase  	phraseDao +com.example.word.data.database.WordDatabase  studySessionDao +com.example.word.data.database.WordDatabase  synchronized +com.example.word.data.database.WordDatabase  userProgressDao +com.example.word.data.database.WordDatabase  wordDao +com.example.word.data.database.WordDatabase  INSTANCE 5com.example.word.data.database.WordDatabase.Companion  Room 5com.example.word.data.database.WordDatabase.Companion  WordDatabase 5com.example.word.data.database.WordDatabase.Companion  WordDatabaseCallback 5com.example.word.data.database.WordDatabase.Companion  databaseBuilder 5com.example.word.data.database.WordDatabase.Companion  getDatabase 5com.example.word.data.database.WordDatabase.Companion  java 5com.example.word.data.database.WordDatabase.Companion  synchronized 5com.example.word.data.database.WordDatabase.Companion  Callback 8com.example.word.data.database.WordDatabase.RoomDatabase  Achievement com.example.word.data.entities  AchievementCategory com.example.word.data.entities  Boolean com.example.word.data.entities  Entity com.example.word.data.entities  
EssayTemplate com.example.word.data.entities  EssayTemplateDao com.example.word.data.entities  ExperienceType com.example.word.data.entities  Float com.example.word.data.entities  Int com.example.word.data.entities  List com.example.word.data.entities  LiveData com.example.word.data.entities  Long com.example.word.data.entities  Phrase com.example.word.data.entities  	PhraseDao com.example.word.data.entities  
PrimaryKey com.example.word.data.entities  R com.example.word.data.entities  String com.example.word.data.entities  StudySession com.example.word.data.entities  StudySessionDao com.example.word.data.entities  
StudyStats com.example.word.data.entities  System com.example.word.data.entities  	UserLevel com.example.word.data.entities  UserProgress com.example.word.data.entities  UserProgressDao com.example.word.data.entities  Word com.example.word.data.entities  WordDao com.example.word.data.entities  
coerceAtLeast com.example.word.data.entities  coerceAtMost com.example.word.data.entities  filter com.example.word.data.entities  listOf com.example.word.data.entities  map com.example.word.data.entities  minOf com.example.word.data.entities  
mutableListOf com.example.word.data.entities  coerceAtMost *com.example.word.data.entities.Achievement  currentValue *com.example.word.data.entities.Achievement  targetValue *com.example.word.data.entities.Achievement  category ,com.example.word.data.entities.EssayTemplate  content ,com.example.word.data.entities.EssayTemplate  description ,com.example.word.data.entities.EssayTemplate  difficultyLevel ,com.example.word.data.entities.EssayTemplate  example ,com.example.word.data.entities.EssayTemplate  id ,com.example.word.data.entities.EssayTemplate  isBookmarked ,com.example.word.data.entities.EssayTemplate  title ,com.example.word.data.entities.EssayTemplate  type ,com.example.word.data.entities.EssayTemplate  usage ,com.example.word.data.entities.EssayTemplate  
usageCount ,com.example.word.data.entities.EssayTemplate  category %com.example.word.data.entities.Phrase  difficultyLevel %com.example.word.data.entities.Phrase  	frequency %com.example.word.data.entities.Phrase  id %com.example.word.data.entities.Phrase  isBookmarked %com.example.word.data.entities.Phrase  phrase %com.example.word.data.entities.Phrase  translation %com.example.word.data.entities.Phrase  type %com.example.word.data.entities.Phrase  
coerceAtLeast (com.example.word.data.entities.UserLevel  coerceAtMost (com.example.word.data.entities.UserLevel  currentExperience (com.example.word.data.entities.UserLevel  experienceRequired (com.example.word.data.entities.UserLevel  consecutiveDays +com.example.word.data.entities.UserProgress  currentLevel +com.example.word.data.entities.UserProgress  experiencePoints +com.example.word.data.entities.UserProgress  	studyDays +com.example.word.data.entities.UserProgress  totalStudyTime +com.example.word.data.entities.UserProgress  accuracyRate #com.example.word.data.entities.Word  difficultyLevel #com.example.word.data.entities.Word  exampleSentence #com.example.word.data.entities.Word  
frequencyRank #com.example.word.data.entities.Word  id #com.example.word.data.entities.Word  isBookmarked #com.example.word.data.entities.Word  memoryStrength #com.example.word.data.entities.Word  nextReviewTime #com.example.word.data.entities.Word  partOfSpeech #com.example.word.data.entities.Word  phonetic #com.example.word.data.entities.Word  reviewInterval #com.example.word.data.entities.Word  
studyCount #com.example.word.data.entities.Word  translation #com.example.word.data.entities.Word  word #com.example.word.data.entities.Word  Boolean  com.example.word.data.repository  
EssayTemplate  com.example.word.data.repository  EssayTemplateDao  com.example.word.data.repository  Float  com.example.word.data.repository  Int  com.example.word.data.repository  List  com.example.word.data.repository  LiveData  com.example.word.data.repository  Long  com.example.word.data.repository  Phrase  com.example.word.data.repository  	PhraseDao  com.example.word.data.repository  String  com.example.word.data.repository  StudySession  com.example.word.data.repository  StudySessionDao  com.example.word.data.repository  UserProgress  com.example.word.data.repository  UserProgressDao  com.example.word.data.repository  Word  com.example.word.data.repository  WordDao  com.example.word.data.repository  WordRepository  com.example.word.data.repository  addExperiencePoints /com.example.word.data.repository.WordRepository  essayTemplateDao /com.example.word.data.repository.WordRepository  getAllPhraseCategories /com.example.word.data.repository.WordRepository  getAllPhraseTypes /com.example.word.data.repository.WordRepository  
getAllPhrases /com.example.word.data.repository.WordRepository  getAllTemplateCategories /com.example.word.data.repository.WordRepository  getAllTemplateTypes /com.example.word.data.repository.WordRepository  getAllTemplates /com.example.word.data.repository.WordRepository  getAllWords /com.example.word.data.repository.WordRepository  getBookmarkedPhrases /com.example.word.data.repository.WordRepository  getBookmarkedTemplates /com.example.word.data.repository.WordRepository  getBookmarkedWords /com.example.word.data.repository.WordRepository  getBookmarkedWordsCount /com.example.word.data.repository.WordRepository  getCorrectAnswersCount /com.example.word.data.repository.WordRepository  getMostUsedTemplates /com.example.word.data.repository.WordRepository  getPhrasesByCategory /com.example.word.data.repository.WordRepository  getPhrasesByDifficulty /com.example.word.data.repository.WordRepository  getPhrasesByType /com.example.word.data.repository.WordRepository  getRandomPhrases /com.example.word.data.repository.WordRepository  getRandomWords /com.example.word.data.repository.WordRepository  getStudiedWordsCount /com.example.word.data.repository.WordRepository  getStudyCountByDateRange /com.example.word.data.repository.WordRepository  getTemplatesByCategory /com.example.word.data.repository.WordRepository  getTemplatesByDifficulty /com.example.word.data.repository.WordRepository  getTemplatesByType /com.example.word.data.repository.WordRepository  getUserProgress /com.example.word.data.repository.WordRepository  getUserProgressSync /com.example.word.data.repository.WordRepository  getWordsAverageAccuracy /com.example.word.data.repository.WordRepository  getWordsByDifficulty /com.example.word.data.repository.WordRepository  getWordsByPartOfSpeech /com.example.word.data.repository.WordRepository  getWordsForReview /com.example.word.data.repository.WordRepository  incrementTemplateUsageCount /com.example.word.data.repository.WordRepository  insertStudySession /com.example.word.data.repository.WordRepository  	phraseDao /com.example.word.data.repository.WordRepository  
searchPhrases /com.example.word.data.repository.WordRepository  searchTemplates /com.example.word.data.repository.WordRepository  searchWords /com.example.word.data.repository.WordRepository  studySessionDao /com.example.word.data.repository.WordRepository  updatePhraseBookmarkStatus /com.example.word.data.repository.WordRepository  updatePhraseStudyStats /com.example.word.data.repository.WordRepository  updateStudyTime /com.example.word.data.repository.WordRepository  updateTemplateBookmarkStatus /com.example.word.data.repository.WordRepository  updateWordBookmarkStatus /com.example.word.data.repository.WordRepository  updateWordReviewInfo /com.example.word.data.repository.WordRepository  updateWordStudyStats /com.example.word.data.repository.WordRepository  userProgressDao /com.example.word.data.repository.WordRepository  wordDao /com.example.word.data.repository.WordRepository  ActivityMainBinding com.example.word.databinding  FragmentEssayBinding com.example.word.databinding  FragmentHomeBinding com.example.word.databinding  FragmentPhrasesBinding com.example.word.databinding  FragmentProgressBinding com.example.word.databinding  FragmentQuizBinding com.example.word.databinding  FragmentSettingsBinding com.example.word.databinding  FragmentVocabularyBinding com.example.word.databinding  ItemAchievementBinding com.example.word.databinding  ItemEssayTemplateBinding com.example.word.databinding  ItemPhraseBinding com.example.word.databinding  ItemWordBinding com.example.word.databinding  inflate 0com.example.word.databinding.ActivityMainBinding  root 0com.example.word.databinding.ActivityMainBinding  buttonBookmarks 1com.example.word.databinding.FragmentEssayBinding  buttonCategories 1com.example.word.databinding.FragmentEssayBinding  buttonFilter 1com.example.word.databinding.FragmentEssayBinding  inflate 1com.example.word.databinding.FragmentEssayBinding  progressBar 1com.example.word.databinding.FragmentEssayBinding  recyclerViewTemplates 1com.example.word.databinding.FragmentEssayBinding  root 1com.example.word.databinding.FragmentEssayBinding  
searchView 1com.example.word.databinding.FragmentEssayBinding  
textViewEmpty 1com.example.word.databinding.FragmentEssayBinding  apply 0com.example.word.databinding.FragmentHomeBinding  buttonEssays 0com.example.word.databinding.FragmentHomeBinding  
buttonPhrases 0com.example.word.databinding.FragmentHomeBinding  buttonStartQuiz 0com.example.word.databinding.FragmentHomeBinding  buttonStartReview 0com.example.word.databinding.FragmentHomeBinding  buttonStartStudy 0com.example.word.databinding.FragmentHomeBinding  buttonViewProgress 0com.example.word.databinding.FragmentHomeBinding  inflate 0com.example.word.databinding.FragmentHomeBinding  progressBarDaily 0com.example.word.databinding.FragmentHomeBinding  progressBarExperience 0com.example.word.databinding.FragmentHomeBinding  root 0com.example.word.databinding.FragmentHomeBinding  textViewDailyGoal 0com.example.word.databinding.FragmentHomeBinding  textViewDate 0com.example.word.databinding.FragmentHomeBinding  textViewExperience 0com.example.word.databinding.FragmentHomeBinding  
textViewLevel 0com.example.word.databinding.FragmentHomeBinding  textViewLevelName 0com.example.word.databinding.FragmentHomeBinding  textViewReviewCount 0com.example.word.databinding.FragmentHomeBinding  textViewTotalWords 0com.example.word.databinding.FragmentHomeBinding  textViewWelcome 0com.example.word.databinding.FragmentHomeBinding  buttonBookmarks 3com.example.word.databinding.FragmentPhrasesBinding  buttonFilter 3com.example.word.databinding.FragmentPhrasesBinding  buttonRandomStudy 3com.example.word.databinding.FragmentPhrasesBinding  inflate 3com.example.word.databinding.FragmentPhrasesBinding  progressBar 3com.example.word.databinding.FragmentPhrasesBinding  recyclerViewPhrases 3com.example.word.databinding.FragmentPhrasesBinding  root 3com.example.word.databinding.FragmentPhrasesBinding  
textViewEmpty 3com.example.word.databinding.FragmentPhrasesBinding  apply 4com.example.word.databinding.FragmentProgressBinding  chartLevelProgress 4com.example.word.databinding.FragmentProgressBinding  chartStudyProgress 4com.example.word.databinding.FragmentProgressBinding  chartVocabularyMastery 4com.example.word.databinding.FragmentProgressBinding  formatStudyTime 4com.example.word.databinding.FragmentProgressBinding  inflate 4com.example.word.databinding.FragmentProgressBinding  progressBar 4com.example.word.databinding.FragmentProgressBinding  progressBarAccuracy 4com.example.word.databinding.FragmentProgressBinding  progressBarLevel 4com.example.word.databinding.FragmentProgressBinding  progressBarMastery 4com.example.word.databinding.FragmentProgressBinding  recyclerViewAchievements 4com.example.word.databinding.FragmentProgressBinding  root 4com.example.word.databinding.FragmentProgressBinding  textViewAverageAccuracy 4com.example.word.databinding.FragmentProgressBinding  textViewBookmarkedWords 4com.example.word.databinding.FragmentProgressBinding  textViewConsecutiveDays 4com.example.word.databinding.FragmentProgressBinding  textViewCurrentLevel 4com.example.word.databinding.FragmentProgressBinding  textViewExpProgress 4com.example.word.databinding.FragmentProgressBinding  textViewExperiencePoints 4com.example.word.databinding.FragmentProgressBinding  textViewMasteredVocabulary 4com.example.word.databinding.FragmentProgressBinding  textViewMasteryRate 4com.example.word.databinding.FragmentProgressBinding  textViewStudiedVocabulary 4com.example.word.databinding.FragmentProgressBinding  textViewStudiedWords 4com.example.word.databinding.FragmentProgressBinding  textViewTodayAccuracy 4com.example.word.databinding.FragmentProgressBinding  textViewTodayStudyCount 4com.example.word.databinding.FragmentProgressBinding  textViewTotalStudyDays 4com.example.word.databinding.FragmentProgressBinding  textViewTotalStudyTime 4com.example.word.databinding.FragmentProgressBinding  textViewTotalVocabulary 4com.example.word.databinding.FragmentProgressBinding  textViewWeekStudyCount 4com.example.word.databinding.FragmentProgressBinding  updateDifficultyStats 4com.example.word.databinding.FragmentProgressBinding  
buttonOption1 0com.example.word.databinding.FragmentQuizBinding  
buttonOption2 0com.example.word.databinding.FragmentQuizBinding  
buttonOption3 0com.example.word.databinding.FragmentQuizBinding  
buttonOption4 0com.example.word.databinding.FragmentQuizBinding  
buttonRestart 0com.example.word.databinding.FragmentQuizBinding  buttonStartQuiz 0com.example.word.databinding.FragmentQuizBinding  buttonSubmitAnswer 0com.example.word.databinding.FragmentQuizBinding  buttonViewDetails 0com.example.word.databinding.FragmentQuizBinding  editTextAnswer 0com.example.word.databinding.FragmentQuizBinding  inflate 0com.example.word.databinding.FragmentQuizBinding  layoutMultipleChoice 0com.example.word.databinding.FragmentQuizBinding  layoutQuizInProgress 0com.example.word.databinding.FragmentQuizBinding  layoutQuizResult 0com.example.word.databinding.FragmentQuizBinding  layoutQuizSetup 0com.example.word.databinding.FragmentQuizBinding  layoutTextInput 0com.example.word.databinding.FragmentQuizBinding  progressBar 0com.example.word.databinding.FragmentQuizBinding  progressBarQuiz 0com.example.word.databinding.FragmentQuizBinding  root 0com.example.word.databinding.FragmentQuizBinding  textViewAccuracy 0com.example.word.databinding.FragmentQuizBinding  textViewAverageTime 0com.example.word.databinding.FragmentQuizBinding  textViewEvaluation 0com.example.word.databinding.FragmentQuizBinding  textViewFinalScore 0com.example.word.databinding.FragmentQuizBinding  textViewHint 0com.example.word.databinding.FragmentQuizBinding  textViewProgress 0com.example.word.databinding.FragmentQuizBinding  textViewQuestion 0com.example.word.databinding.FragmentQuizBinding  
textViewScore 0com.example.word.databinding.FragmentQuizBinding  textViewTotalTime 0com.example.word.databinding.FragmentQuizBinding  buttonAbout 4com.example.word.databinding.FragmentSettingsBinding  buttonClearData 4com.example.word.databinding.FragmentSettingsBinding  buttonExportData 4com.example.word.databinding.FragmentSettingsBinding  buttonImportData 4com.example.word.databinding.FragmentSettingsBinding  
buttonTestTTS 4com.example.word.databinding.FragmentSettingsBinding  inflate 4com.example.word.databinding.FragmentSettingsBinding  layoutReminderTime 4com.example.word.databinding.FragmentSettingsBinding  radioButtonDark 4com.example.word.databinding.FragmentSettingsBinding  radioButtonLight 4com.example.word.databinding.FragmentSettingsBinding  radioButtonSystem 4com.example.word.databinding.FragmentSettingsBinding  radioGroupTheme 4com.example.word.databinding.FragmentSettingsBinding  root 4com.example.word.databinding.FragmentSettingsBinding  spinnerDailyGoal 4com.example.word.databinding.FragmentSettingsBinding  spinnerReviewMode 4com.example.word.databinding.FragmentSettingsBinding  switchAutoBackup 4com.example.word.databinding.FragmentSettingsBinding  switchAutoPlay 4com.example.word.databinding.FragmentSettingsBinding  switchReminder 4com.example.word.databinding.FragmentSettingsBinding  switchShowProgress 4com.example.word.databinding.FragmentSettingsBinding  switchShowStreak 4com.example.word.databinding.FragmentSettingsBinding  switchSlowSpeed 4com.example.word.databinding.FragmentSettingsBinding  textViewReminderTime 4com.example.word.databinding.FragmentSettingsBinding  buttonBookmarks 6com.example.word.databinding.FragmentVocabularyBinding  buttonFilter 6com.example.word.databinding.FragmentVocabularyBinding  buttonRandomStudy 6com.example.word.databinding.FragmentVocabularyBinding  buttonReview 6com.example.word.databinding.FragmentVocabularyBinding  inflate 6com.example.word.databinding.FragmentVocabularyBinding  progressBar 6com.example.word.databinding.FragmentVocabularyBinding  recyclerViewWords 6com.example.word.databinding.FragmentVocabularyBinding  root 6com.example.word.databinding.FragmentVocabularyBinding  
searchView 6com.example.word.databinding.FragmentVocabularyBinding  
textViewEmpty 6com.example.word.databinding.FragmentVocabularyBinding  R 3com.example.word.databinding.ItemAchievementBinding  apply 3com.example.word.databinding.ItemAchievementBinding  
imageViewIcon 3com.example.word.databinding.ItemAchievementBinding  inflate 3com.example.word.databinding.ItemAchievementBinding  progressBarAchievement 3com.example.word.databinding.ItemAchievementBinding  root 3com.example.word.databinding.ItemAchievementBinding  textViewDescription 3com.example.word.databinding.ItemAchievementBinding  textViewProgress 3com.example.word.databinding.ItemAchievementBinding  
textViewTitle 3com.example.word.databinding.ItemAchievementBinding  R 5com.example.word.databinding.ItemEssayTemplateBinding  android 5com.example.word.databinding.ItemEssayTemplateBinding  apply 5com.example.word.databinding.ItemEssayTemplateBinding  getTypeBackground 5com.example.word.databinding.ItemEssayTemplateBinding  imageViewBookmark 5com.example.word.databinding.ItemEssayTemplateBinding  inflate 5com.example.word.databinding.ItemEssayTemplateBinding  
isNotEmpty 5com.example.word.databinding.ItemEssayTemplateBinding  onBookmarkClick 5com.example.word.databinding.ItemEssayTemplateBinding  onTemplateClick 5com.example.word.databinding.ItemEssayTemplateBinding  root 5com.example.word.databinding.ItemEssayTemplateBinding  textViewCategory 5com.example.word.databinding.ItemEssayTemplateBinding  textViewContent 5com.example.word.databinding.ItemEssayTemplateBinding  textViewDescription 5com.example.word.databinding.ItemEssayTemplateBinding  textViewDifficulty 5com.example.word.databinding.ItemEssayTemplateBinding  textViewExample 5com.example.word.databinding.ItemEssayTemplateBinding  
textViewTitle 5com.example.word.databinding.ItemEssayTemplateBinding  textViewType 5com.example.word.databinding.ItemEssayTemplateBinding  
textViewUsage 5com.example.word.databinding.ItemEssayTemplateBinding  textViewUsageCount 5com.example.word.databinding.ItemEssayTemplateBinding  R .com.example.word.databinding.ItemPhraseBinding  apply .com.example.word.databinding.ItemPhraseBinding  imageViewBookmark .com.example.word.databinding.ItemPhraseBinding  inflate .com.example.word.databinding.ItemPhraseBinding  onBookmarkClick .com.example.word.databinding.ItemPhraseBinding  
onPhraseClick .com.example.word.databinding.ItemPhraseBinding  root .com.example.word.databinding.ItemPhraseBinding  textViewCategory .com.example.word.databinding.ItemPhraseBinding  textViewDifficulty .com.example.word.databinding.ItemPhraseBinding  textViewFrequency .com.example.word.databinding.ItemPhraseBinding  textViewPhrase .com.example.word.databinding.ItemPhraseBinding  textViewTranslation .com.example.word.databinding.ItemPhraseBinding  textViewType .com.example.word.databinding.ItemPhraseBinding  R ,com.example.word.databinding.ItemWordBinding  android ,com.example.word.databinding.ItemWordBinding  apply ,com.example.word.databinding.ItemWordBinding  imageViewBookmark ,com.example.word.databinding.ItemWordBinding  imageViewSpeak ,com.example.word.databinding.ItemWordBinding  inflate ,com.example.word.databinding.ItemWordBinding  onBookmarkClick ,com.example.word.databinding.ItemWordBinding  onSpeakClick ,com.example.word.databinding.ItemWordBinding  onWordClick ,com.example.word.databinding.ItemWordBinding  root ,com.example.word.databinding.ItemWordBinding  textViewDifficulty ,com.example.word.databinding.ItemWordBinding  textViewFrequency ,com.example.word.databinding.ItemWordBinding  textViewPartOfSpeech ,com.example.word.databinding.ItemWordBinding  textViewPhonetic ,com.example.word.databinding.ItemWordBinding  textViewStudyStats ,com.example.word.databinding.ItemWordBinding  textViewTranslation ,com.example.word.databinding.ItemWordBinding  textViewWord ,com.example.word.databinding.ItemWordBinding  AndroidViewModel com.example.word.ui.essay  Application com.example.word.ui.essay  Boolean com.example.word.ui.essay  Bundle com.example.word.ui.essay  DiffUtil com.example.word.ui.essay  
EssayFragment com.example.word.ui.essay  
EssayTemplate com.example.word.ui.essay  EssayTemplateAdapter com.example.word.ui.essay  EssayViewModel com.example.word.ui.essay  	Exception com.example.word.ui.essay  Fragment com.example.word.ui.essay  FragmentEssayBinding com.example.word.ui.essay  Int com.example.word.ui.essay  ItemEssayTemplateBinding com.example.word.ui.essay  LayoutInflater com.example.word.ui.essay  LinearLayoutManager com.example.word.ui.essay  List com.example.word.ui.essay  ListAdapter com.example.word.ui.essay  LiveData com.example.word.ui.essay  MutableLiveData com.example.word.ui.essay  R com.example.word.ui.essay  RecyclerView com.example.word.ui.essay  String com.example.word.ui.essay  TemplateViewHolder com.example.word.ui.essay  Toast com.example.word.ui.essay  Unit com.example.word.ui.essay  View com.example.word.ui.essay  	ViewGroup com.example.word.ui.essay  WordDatabase com.example.word.ui.essay  WordRepository com.example.word.ui.essay  
_errorMessage com.example.word.ui.essay  _filteredTemplates com.example.word.ui.essay  
_isLoading com.example.word.ui.essay  _searchResults com.example.word.ui.essay  _selectedCategory com.example.word.ui.essay  _selectedDifficulty com.example.word.ui.essay  
_selectedType com.example.word.ui.essay  _templateCategories com.example.word.ui.essay  _templateTypes com.example.word.ui.essay  allTemplates com.example.word.ui.essay  android com.example.word.ui.essay  androidx com.example.word.ui.essay  apply com.example.word.ui.essay  com com.example.word.ui.essay  	emptyList com.example.word.ui.essay  essayTemplateAdapter com.example.word.ui.essay  essayViewModel com.example.word.ui.essay  filter com.example.word.ui.essay  getDatabase com.example.word.ui.essay  getTypeBackground com.example.word.ui.essay  getValue com.example.word.ui.essay  isBlank com.example.word.ui.essay  
isNotEmpty com.example.word.ui.essay  
isNullOrBlank com.example.word.ui.essay  launch com.example.word.ui.essay  let com.example.word.ui.essay  onBookmarkClick com.example.word.ui.essay  onTemplateClick com.example.word.ui.essay  provideDelegate com.example.word.ui.essay  
repository com.example.word.ui.essay  requireContext com.example.word.ui.essay  ItemCallback "com.example.word.ui.essay.DiffUtil  EssayTemplateAdapter 'com.example.word.ui.essay.EssayFragment  FragmentEssayBinding 'com.example.word.ui.essay.EssayFragment  LinearLayoutManager 'com.example.word.ui.essay.EssayFragment  Toast 'com.example.word.ui.essay.EssayFragment  View 'com.example.word.ui.essay.EssayFragment  _binding 'com.example.word.ui.essay.EssayFragment  apply 'com.example.word.ui.essay.EssayFragment  binding 'com.example.word.ui.essay.EssayFragment  essayTemplateAdapter 'com.example.word.ui.essay.EssayFragment  essayViewModel 'com.example.word.ui.essay.EssayFragment  getValue 'com.example.word.ui.essay.EssayFragment  
isNullOrBlank 'com.example.word.ui.essay.EssayFragment  let 'com.example.word.ui.essay.EssayFragment  provideDelegate 'com.example.word.ui.essay.EssayFragment  requireContext 'com.example.word.ui.essay.EssayFragment  setupClickListeners 'com.example.word.ui.essay.EssayFragment  setupObservers 'com.example.word.ui.essay.EssayFragment  setupRecyclerView 'com.example.word.ui.essay.EssayFragment  showBookmarkedTemplates 'com.example.word.ui.essay.EssayFragment  showCategoriesDialog 'com.example.word.ui.essay.EssayFragment  showFilterDialog 'com.example.word.ui.essay.EssayFragment  showTemplateDetail 'com.example.word.ui.essay.EssayFragment  updateEmptyState 'com.example.word.ui.essay.EssayFragment  viewLifecycleOwner 'com.example.word.ui.essay.EssayFragment  
viewModels 'com.example.word.ui.essay.EssayFragment  Boolean .com.example.word.ui.essay.EssayTemplateAdapter  DiffUtil .com.example.word.ui.essay.EssayTemplateAdapter  
EssayTemplate .com.example.word.ui.essay.EssayTemplateAdapter  Int .com.example.word.ui.essay.EssayTemplateAdapter  ItemEssayTemplateBinding .com.example.word.ui.essay.EssayTemplateAdapter  LayoutInflater .com.example.word.ui.essay.EssayTemplateAdapter  R .com.example.word.ui.essay.EssayTemplateAdapter  RecyclerView .com.example.word.ui.essay.EssayTemplateAdapter  String .com.example.word.ui.essay.EssayTemplateAdapter  TemplateDiffCallback .com.example.word.ui.essay.EssayTemplateAdapter  TemplateViewHolder .com.example.word.ui.essay.EssayTemplateAdapter  Unit .com.example.word.ui.essay.EssayTemplateAdapter  	ViewGroup .com.example.word.ui.essay.EssayTemplateAdapter  android .com.example.word.ui.essay.EssayTemplateAdapter  apply .com.example.word.ui.essay.EssayTemplateAdapter  getItem .com.example.word.ui.essay.EssayTemplateAdapter  getTypeBackground .com.example.word.ui.essay.EssayTemplateAdapter  
isNotEmpty .com.example.word.ui.essay.EssayTemplateAdapter  onBookmarkClick .com.example.word.ui.essay.EssayTemplateAdapter  onTemplateClick .com.example.word.ui.essay.EssayTemplateAdapter  
submitList .com.example.word.ui.essay.EssayTemplateAdapter  ItemCallback 7com.example.word.ui.essay.EssayTemplateAdapter.DiffUtil  
ViewHolder ;com.example.word.ui.essay.EssayTemplateAdapter.RecyclerView  R Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  android Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  apply Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  bind Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  binding Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  getTypeBackground Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  
isNotEmpty Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  onBookmarkClick Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  onTemplateClick Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  MutableLiveData (com.example.word.ui.essay.EssayViewModel  WordDatabase (com.example.word.ui.essay.EssayViewModel  WordRepository (com.example.word.ui.essay.EssayViewModel  _currentTemplate (com.example.word.ui.essay.EssayViewModel  
_errorMessage (com.example.word.ui.essay.EssayViewModel  _filteredTemplates (com.example.word.ui.essay.EssayViewModel  
_isLoading (com.example.word.ui.essay.EssayViewModel  _searchResults (com.example.word.ui.essay.EssayViewModel  _selectedCategory (com.example.word.ui.essay.EssayViewModel  _selectedDifficulty (com.example.word.ui.essay.EssayViewModel  
_selectedType (com.example.word.ui.essay.EssayViewModel  _templateCategories (com.example.word.ui.essay.EssayViewModel  _templateTypes (com.example.word.ui.essay.EssayViewModel  allTemplates (com.example.word.ui.essay.EssayViewModel  applyFilters (com.example.word.ui.essay.EssayViewModel  bookmarkedTemplates (com.example.word.ui.essay.EssayViewModel  clearErrorMessage (com.example.word.ui.essay.EssayViewModel  clearFilters (com.example.word.ui.essay.EssayViewModel  	emptyList (com.example.word.ui.essay.EssayViewModel  errorMessage (com.example.word.ui.essay.EssayViewModel  filter (com.example.word.ui.essay.EssayViewModel  filteredTemplates (com.example.word.ui.essay.EssayViewModel  getBookmarkedTemplates (com.example.word.ui.essay.EssayViewModel  getDatabase (com.example.word.ui.essay.EssayViewModel  isBlank (com.example.word.ui.essay.EssayViewModel  	isLoading (com.example.word.ui.essay.EssayViewModel  launch (com.example.word.ui.essay.EssayViewModel  loadTemplateCategories (com.example.word.ui.essay.EssayViewModel  loadTemplateTypes (com.example.word.ui.essay.EssayViewModel  
repository (com.example.word.ui.essay.EssayViewModel  
searchResults (com.example.word.ui.essay.EssayViewModel  searchTemplates (com.example.word.ui.essay.EssayViewModel  setCurrentTemplate (com.example.word.ui.essay.EssayViewModel  toggleBookmark (com.example.word.ui.essay.EssayViewModel  viewModelScope (com.example.word.ui.essay.EssayViewModel  
ViewHolder &com.example.word.ui.essay.RecyclerView  	appcompat "com.example.word.ui.essay.androidx  widget ,com.example.word.ui.essay.androidx.appcompat  
SearchView 3com.example.word.ui.essay.androidx.appcompat.widget  OnQueryTextListener >com.example.word.ui.essay.androidx.appcompat.widget.SearchView  example com.example.word.ui.essay.com  word %com.example.word.ui.essay.com.example  data *com.example.word.ui.essay.com.example.word  entities /com.example.word.ui.essay.com.example.word.data  
EssayTemplate 8com.example.word.ui.essay.com.example.word.data.entities  Bundle com.example.word.ui.home  	Exception com.example.word.ui.home  Fragment com.example.word.ui.home  FragmentHomeBinding com.example.word.ui.home  GameificationManager com.example.word.ui.home  HomeFragment com.example.word.ui.home  Int com.example.word.ui.home  LayoutInflater com.example.word.ui.home  R com.example.word.ui.home  String com.example.word.ui.home  View com.example.word.ui.home  	ViewGroup com.example.word.ui.home  
WordViewModel com.example.word.ui.home  apply com.example.word.ui.home  arrayOf com.example.word.ui.home  gamificationManager com.example.word.ui.home  getValue com.example.word.ui.home  java com.example.word.ui.home  launch com.example.word.ui.home  provideDelegate com.example.word.ui.home  updateDailyGoal com.example.word.ui.home  updateLevelInfo com.example.word.ui.home  	UserLevel -com.example.word.ui.home.GameificationManager  FragmentHomeBinding %com.example.word.ui.home.HomeFragment  GameificationManager %com.example.word.ui.home.HomeFragment  R %com.example.word.ui.home.HomeFragment  _binding %com.example.word.ui.home.HomeFragment  apply %com.example.word.ui.home.HomeFragment  arrayOf %com.example.word.ui.home.HomeFragment  binding %com.example.word.ui.home.HomeFragment  findNavController %com.example.word.ui.home.HomeFragment  gamificationManager %com.example.word.ui.home.HomeFragment  getCurrentDateString %com.example.word.ui.home.HomeFragment  getValue %com.example.word.ui.home.HomeFragment  getWelcomeMessage %com.example.word.ui.home.HomeFragment  java %com.example.word.ui.home.HomeFragment  launch %com.example.word.ui.home.HomeFragment  lifecycleScope %com.example.word.ui.home.HomeFragment  loadData %com.example.word.ui.home.HomeFragment  provideDelegate %com.example.word.ui.home.HomeFragment  setupClickListeners %com.example.word.ui.home.HomeFragment  setupObservers %com.example.word.ui.home.HomeFragment  setupUI %com.example.word.ui.home.HomeFragment  updateDailyGoal %com.example.word.ui.home.HomeFragment  updateLevelInfo %com.example.word.ui.home.HomeFragment  updateReviewStats %com.example.word.ui.home.HomeFragment  updateWordStats %com.example.word.ui.home.HomeFragment  viewLifecycleOwner %com.example.word.ui.home.HomeFragment  
viewModels %com.example.word.ui.home.HomeFragment  
wordViewModel %com.example.word.ui.home.HomeFragment  Boolean com.example.word.ui.phrases  Bundle com.example.word.ui.phrases  DiffUtil com.example.word.ui.phrases  Fragment com.example.word.ui.phrases  FragmentPhrasesBinding com.example.word.ui.phrases  Int com.example.word.ui.phrases  ItemPhraseBinding com.example.word.ui.phrases  LayoutInflater com.example.word.ui.phrases  LinearLayoutManager com.example.word.ui.phrases  ListAdapter com.example.word.ui.phrases  Phrase com.example.word.ui.phrases  
PhraseAdapter com.example.word.ui.phrases  PhraseViewHolder com.example.word.ui.phrases  PhraseViewModel com.example.word.ui.phrases  PhrasesFragment com.example.word.ui.phrases  R com.example.word.ui.phrases  RecyclerView com.example.word.ui.phrases  Toast com.example.word.ui.phrases  Unit com.example.word.ui.phrases  View com.example.word.ui.phrases  	ViewGroup com.example.word.ui.phrases  apply com.example.word.ui.phrases  getValue com.example.word.ui.phrases  let com.example.word.ui.phrases  onBookmarkClick com.example.word.ui.phrases  
onPhraseClick com.example.word.ui.phrases  
phraseAdapter com.example.word.ui.phrases  provideDelegate com.example.word.ui.phrases  requireContext com.example.word.ui.phrases  ItemCallback $com.example.word.ui.phrases.DiffUtil  Boolean )com.example.word.ui.phrases.PhraseAdapter  DiffUtil )com.example.word.ui.phrases.PhraseAdapter  Int )com.example.word.ui.phrases.PhraseAdapter  ItemPhraseBinding )com.example.word.ui.phrases.PhraseAdapter  LayoutInflater )com.example.word.ui.phrases.PhraseAdapter  Phrase )com.example.word.ui.phrases.PhraseAdapter  PhraseDiffCallback )com.example.word.ui.phrases.PhraseAdapter  PhraseViewHolder )com.example.word.ui.phrases.PhraseAdapter  R )com.example.word.ui.phrases.PhraseAdapter  RecyclerView )com.example.word.ui.phrases.PhraseAdapter  Unit )com.example.word.ui.phrases.PhraseAdapter  	ViewGroup )com.example.word.ui.phrases.PhraseAdapter  apply )com.example.word.ui.phrases.PhraseAdapter  getItem )com.example.word.ui.phrases.PhraseAdapter  onBookmarkClick )com.example.word.ui.phrases.PhraseAdapter  
onPhraseClick )com.example.word.ui.phrases.PhraseAdapter  
submitList )com.example.word.ui.phrases.PhraseAdapter  ItemCallback 2com.example.word.ui.phrases.PhraseAdapter.DiffUtil  R :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  apply :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  bind :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  binding :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  onBookmarkClick :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  
onPhraseClick :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  
ViewHolder 6com.example.word.ui.phrases.PhraseAdapter.RecyclerView  FragmentPhrasesBinding +com.example.word.ui.phrases.PhrasesFragment  LinearLayoutManager +com.example.word.ui.phrases.PhrasesFragment  
PhraseAdapter +com.example.word.ui.phrases.PhrasesFragment  Toast +com.example.word.ui.phrases.PhrasesFragment  View +com.example.word.ui.phrases.PhrasesFragment  _binding +com.example.word.ui.phrases.PhrasesFragment  apply +com.example.word.ui.phrases.PhrasesFragment  binding +com.example.word.ui.phrases.PhrasesFragment  getValue +com.example.word.ui.phrases.PhrasesFragment  let +com.example.word.ui.phrases.PhrasesFragment  
phraseAdapter +com.example.word.ui.phrases.PhrasesFragment  phraseViewModel +com.example.word.ui.phrases.PhrasesFragment  provideDelegate +com.example.word.ui.phrases.PhrasesFragment  requireContext +com.example.word.ui.phrases.PhrasesFragment  setupClickListeners +com.example.word.ui.phrases.PhrasesFragment  setupObservers +com.example.word.ui.phrases.PhrasesFragment  setupRecyclerView +com.example.word.ui.phrases.PhrasesFragment  showBookmarkedPhrases +com.example.word.ui.phrases.PhrasesFragment  showFilterDialog +com.example.word.ui.phrases.PhrasesFragment  updateEmptyState +com.example.word.ui.phrases.PhrasesFragment  viewLifecycleOwner +com.example.word.ui.phrases.PhrasesFragment  
viewModels +com.example.word.ui.phrases.PhrasesFragment  
ViewHolder (com.example.word.ui.phrases.RecyclerView  AchievementAdapter com.example.word.ui.progress  AchievementViewHolder com.example.word.ui.progress  AndroidViewModel com.example.word.ui.progress  Application com.example.word.ui.progress  Boolean com.example.word.ui.progress  Bundle com.example.word.ui.progress  Calendar com.example.word.ui.progress  DailyStudyRecord com.example.word.ui.progress  Date com.example.word.ui.progress  DiffUtil com.example.word.ui.progress  DifficultyStats com.example.word.ui.progress  	Exception com.example.word.ui.progress  Float com.example.word.ui.progress  Fragment com.example.word.ui.progress  FragmentProgressBinding com.example.word.ui.progress  GameificationManager com.example.word.ui.progress  Int com.example.word.ui.progress  ItemAchievementBinding com.example.word.ui.progress  LayoutInflater com.example.word.ui.progress  LinearLayoutManager com.example.word.ui.progress  List com.example.word.ui.progress  ListAdapter com.example.word.ui.progress  LiveData com.example.word.ui.progress  Locale com.example.word.ui.progress  Long com.example.word.ui.progress  Map com.example.word.ui.progress  MutableLiveData com.example.word.ui.progress  ProgressFragment com.example.word.ui.progress  ProgressViewModel com.example.word.ui.progress  R com.example.word.ui.progress  RecyclerView com.example.word.ui.progress  SimpleDateFormat com.example.word.ui.progress  String com.example.word.ui.progress  StudyDataPoint com.example.word.ui.progress  StudyStatistics com.example.word.ui.progress  System com.example.word.ui.progress  Toast com.example.word.ui.progress  UserProgress com.example.word.ui.progress  View com.example.word.ui.progress  	ViewGroup com.example.word.ui.progress  VocabularyMastery com.example.word.ui.progress  WordDatabase com.example.word.ui.progress  WordRepository com.example.word.ui.progress  
_achievements com.example.word.ui.progress  
_errorMessage com.example.word.ui.progress  
_isLoading com.example.word.ui.progress  
_studyHistory com.example.word.ui.progress  _studyStatistics com.example.word.ui.progress  _vocabularyMastery com.example.word.ui.progress  achievementAdapter com.example.word.ui.progress  apply com.example.word.ui.progress  calculateNextLevelExp com.example.word.ui.progress  com com.example.word.ui.progress  createConsecutiveAchievements com.example.word.ui.progress  createExperienceAchievements com.example.word.ui.progress  createStudyDaysAchievements com.example.word.ui.progress  createVocabularyAchievements com.example.word.ui.progress  downTo com.example.word.ui.progress  formatStudyTime com.example.word.ui.progress  getDatabase com.example.word.ui.progress  getDayStartTime com.example.word.ui.progress  getTodayStartTime com.example.word.ui.progress  getValue com.example.word.ui.progress  getWeekStartTime com.example.word.ui.progress  launch com.example.word.ui.progress  let com.example.word.ui.progress  
mutableListOf com.example.word.ui.progress  mutableMapOf com.example.word.ui.progress  provideDelegate com.example.word.ui.progress  random com.example.word.ui.progress  
repository com.example.word.ui.progress  requireContext com.example.word.ui.progress  reversed com.example.word.ui.progress  set com.example.word.ui.progress  sortedByDescending com.example.word.ui.progress  until com.example.word.ui.progress  updateDifficultyStats com.example.word.ui.progress  AchievementDiffCallback /com.example.word.ui.progress.AchievementAdapter  AchievementViewHolder /com.example.word.ui.progress.AchievementAdapter  Boolean /com.example.word.ui.progress.AchievementAdapter  DiffUtil /com.example.word.ui.progress.AchievementAdapter  GameificationManager /com.example.word.ui.progress.AchievementAdapter  Int /com.example.word.ui.progress.AchievementAdapter  ItemAchievementBinding /com.example.word.ui.progress.AchievementAdapter  LayoutInflater /com.example.word.ui.progress.AchievementAdapter  R /com.example.word.ui.progress.AchievementAdapter  RecyclerView /com.example.word.ui.progress.AchievementAdapter  	ViewGroup /com.example.word.ui.progress.AchievementAdapter  apply /com.example.word.ui.progress.AchievementAdapter  getItem /com.example.word.ui.progress.AchievementAdapter  
submitList /com.example.word.ui.progress.AchievementAdapter  R Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  apply Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  bind Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  binding Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  ItemCallback 8com.example.word.ui.progress.AchievementAdapter.DiffUtil  Achievement Dcom.example.word.ui.progress.AchievementAdapter.GameificationManager  
ViewHolder <com.example.word.ui.progress.AchievementAdapter.RecyclerView  ItemCallback %com.example.word.ui.progress.DiffUtil  Achievement 1com.example.word.ui.progress.GameificationManager  AchievementAdapter -com.example.word.ui.progress.ProgressFragment  Calendar -com.example.word.ui.progress.ProgressFragment  FragmentProgressBinding -com.example.word.ui.progress.ProgressFragment  LinearLayoutManager -com.example.word.ui.progress.ProgressFragment  Locale -com.example.word.ui.progress.ProgressFragment  SimpleDateFormat -com.example.word.ui.progress.ProgressFragment  StudyDataPoint -com.example.word.ui.progress.ProgressFragment  Toast -com.example.word.ui.progress.ProgressFragment  View -com.example.word.ui.progress.ProgressFragment  _binding -com.example.word.ui.progress.ProgressFragment  achievementAdapter -com.example.word.ui.progress.ProgressFragment  apply -com.example.word.ui.progress.ProgressFragment  binding -com.example.word.ui.progress.ProgressFragment  downTo -com.example.word.ui.progress.ProgressFragment  formatStudyTime -com.example.word.ui.progress.ProgressFragment  generateStudyProgressData -com.example.word.ui.progress.ProgressFragment  getValue -com.example.word.ui.progress.ProgressFragment  let -com.example.word.ui.progress.ProgressFragment  
mutableListOf -com.example.word.ui.progress.ProgressFragment  progressViewModel -com.example.word.ui.progress.ProgressFragment  provideDelegate -com.example.word.ui.progress.ProgressFragment  random -com.example.word.ui.progress.ProgressFragment  requireContext -com.example.word.ui.progress.ProgressFragment  reversed -com.example.word.ui.progress.ProgressFragment  setupCharts -com.example.word.ui.progress.ProgressFragment  setupObservers -com.example.word.ui.progress.ProgressFragment  setupRecyclerView -com.example.word.ui.progress.ProgressFragment  updateDifficultyStats -com.example.word.ui.progress.ProgressFragment  updateStudyStatistics -com.example.word.ui.progress.ProgressFragment  updateUserProgress -com.example.word.ui.progress.ProgressFragment  updateVocabularyMastery -com.example.word.ui.progress.ProgressFragment  viewLifecycleOwner -com.example.word.ui.progress.ProgressFragment  
viewModels -com.example.word.ui.progress.ProgressFragment  Calendar .com.example.word.ui.progress.ProgressViewModel  DailyStudyRecord .com.example.word.ui.progress.ProgressViewModel  DifficultyStats .com.example.word.ui.progress.ProgressViewModel  GameificationManager .com.example.word.ui.progress.ProgressViewModel  MutableLiveData .com.example.word.ui.progress.ProgressViewModel  StudyStatistics .com.example.word.ui.progress.ProgressViewModel  System .com.example.word.ui.progress.ProgressViewModel  VocabularyMastery .com.example.word.ui.progress.ProgressViewModel  WordDatabase .com.example.word.ui.progress.ProgressViewModel  WordRepository .com.example.word.ui.progress.ProgressViewModel  
_achievements .com.example.word.ui.progress.ProgressViewModel  
_errorMessage .com.example.word.ui.progress.ProgressViewModel  
_isLoading .com.example.word.ui.progress.ProgressViewModel  
_studyHistory .com.example.word.ui.progress.ProgressViewModel  _studyStatistics .com.example.word.ui.progress.ProgressViewModel  _vocabularyMastery .com.example.word.ui.progress.ProgressViewModel  achievements .com.example.word.ui.progress.ProgressViewModel  calculateNextLevelExp .com.example.word.ui.progress.ProgressViewModel  clearErrorMessage .com.example.word.ui.progress.ProgressViewModel  createConsecutiveAchievements .com.example.word.ui.progress.ProgressViewModel  createExperienceAchievements .com.example.word.ui.progress.ProgressViewModel  createStudyDaysAchievements .com.example.word.ui.progress.ProgressViewModel  createVocabularyAchievements .com.example.word.ui.progress.ProgressViewModel  errorMessage .com.example.word.ui.progress.ProgressViewModel  getDatabase .com.example.word.ui.progress.ProgressViewModel  getDayStartTime .com.example.word.ui.progress.ProgressViewModel  getTodayStartTime .com.example.word.ui.progress.ProgressViewModel  getWeekStartTime .com.example.word.ui.progress.ProgressViewModel  	isLoading .com.example.word.ui.progress.ProgressViewModel  launch .com.example.word.ui.progress.ProgressViewModel  loadAchievements .com.example.word.ui.progress.ProgressViewModel  loadStudyHistory .com.example.word.ui.progress.ProgressViewModel  loadStudyStatistics .com.example.word.ui.progress.ProgressViewModel  loadVocabularyMastery .com.example.word.ui.progress.ProgressViewModel  
mutableListOf .com.example.word.ui.progress.ProgressViewModel  mutableMapOf .com.example.word.ui.progress.ProgressViewModel  
repository .com.example.word.ui.progress.ProgressViewModel  reversed .com.example.word.ui.progress.ProgressViewModel  set .com.example.word.ui.progress.ProgressViewModel  sortedByDescending .com.example.word.ui.progress.ProgressViewModel  studyStatistics .com.example.word.ui.progress.ProgressViewModel  until .com.example.word.ui.progress.ProgressViewModel  userProgress .com.example.word.ui.progress.ProgressViewModel  viewModelScope .com.example.word.ui.progress.ProgressViewModel  vocabularyMastery .com.example.word.ui.progress.ProgressViewModel  
ViewHolder )com.example.word.ui.progress.RecyclerView  averageAccuracy ,com.example.word.ui.progress.StudyStatistics  bookmarkedWordsCount ,com.example.word.ui.progress.StudyStatistics  consecutiveStudyDays ,com.example.word.ui.progress.StudyStatistics  studiedWordsCount ,com.example.word.ui.progress.StudyStatistics  
todayAccuracy ,com.example.word.ui.progress.StudyStatistics  todayStudyCount ,com.example.word.ui.progress.StudyStatistics  totalStudyDays ,com.example.word.ui.progress.StudyStatistics  totalStudyTime ,com.example.word.ui.progress.StudyStatistics  weekStudyCount ,com.example.word.ui.progress.StudyStatistics  difficultyStats .com.example.word.ui.progress.VocabularyMastery  
masteredWords .com.example.word.ui.progress.VocabularyMastery  masteryRate .com.example.word.ui.progress.VocabularyMastery  studiedWords .com.example.word.ui.progress.VocabularyMastery  
totalWords .com.example.word.ui.progress.VocabularyMastery  example  com.example.word.ui.progress.com  word (com.example.word.ui.progress.com.example  data -com.example.word.ui.progress.com.example.word  entities 2com.example.word.ui.progress.com.example.word.data  UserProgress ;com.example.word.ui.progress.com.example.word.data.entities  AndroidViewModel com.example.word.ui.quiz  Application com.example.word.ui.quiz  Boolean com.example.word.ui.quiz  Bundle com.example.word.ui.quiz  	Exception com.example.word.ui.quiz  Fragment com.example.word.ui.quiz  FragmentQuizBinding com.example.word.ui.quiz  Int com.example.word.ui.quiz  LayoutInflater com.example.word.ui.quiz  List com.example.word.ui.quiz  LiveData com.example.word.ui.quiz  Long com.example.word.ui.quiz  MutableLiveData com.example.word.ui.quiz  QuestionType com.example.word.ui.quiz  
QuizConfig com.example.word.ui.quiz  QuizFragment com.example.word.ui.quiz  QuizProgress com.example.word.ui.quiz  QuizQuestion com.example.word.ui.quiz  
QuizResult com.example.word.ui.quiz  
QuizSource com.example.word.ui.quiz  	QuizState com.example.word.ui.quiz  
QuizViewModel com.example.word.ui.quiz  Random com.example.word.ui.quiz  String com.example.word.ui.quiz  StudySession com.example.word.ui.quiz  System com.example.word.ui.quiz  Toast com.example.word.ui.quiz  View com.example.word.ui.quiz  	ViewGroup com.example.word.ui.quiz  Word com.example.word.ui.quiz  WordDatabase com.example.word.ui.quiz  WordRepository com.example.word.ui.quiz  
_errorMessage com.example.word.ui.quiz  
_isLoading com.example.word.ui.quiz  
_quizProgress com.example.word.ui.quiz  
_quizState com.example.word.ui.quiz  any com.example.word.ui.quiz  coerceIn com.example.word.ui.quiz  correctAnswers com.example.word.ui.quiz  currentQuestionIndex com.example.word.ui.quiz  distinct com.example.word.ui.quiz  	emptyList com.example.word.ui.quiz  equals com.example.word.ui.quiz  filter com.example.word.ui.quiz  first com.example.word.ui.quiz  generateNextQuestion com.example.word.ui.quiz  getDatabase com.example.word.ui.quiz  getValue com.example.word.ui.quiz  
isNotEmpty com.example.word.ui.quiz  launch com.example.word.ui.quiz  let com.example.word.ui.quiz  map com.example.word.ui.quiz  nextBoolean com.example.word.ui.quiz  nextInt com.example.word.ui.quiz  plus com.example.word.ui.quiz  provideDelegate com.example.word.ui.quiz  	quizWords com.example.word.ui.quiz  recordStudySession com.example.word.ui.quiz  replace com.example.word.ui.quiz  
repository com.example.word.ui.quiz  run com.example.word.ui.quiz  shuffled com.example.word.ui.quiz  split com.example.word.ui.quiz  	startTime com.example.word.ui.quiz  take com.example.word.ui.quiz  trim com.example.word.ui.quiz  	uppercase com.example.word.ui.quiz  
FILL_BLANK %com.example.word.ui.quiz.QuestionType  MIXED %com.example.word.ui.quiz.QuestionType  MULTIPLE_CHOICE %com.example.word.ui.quiz.QuestionType  TRANSLATION %com.example.word.ui.quiz.QuestionType  values %com.example.word.ui.quiz.QuestionType  
difficulty #com.example.word.ui.quiz.QuizConfig  
questionCount #com.example.word.ui.quiz.QuizConfig  questionType #com.example.word.ui.quiz.QuizConfig  source #com.example.word.ui.quiz.QuizConfig  FragmentQuizBinding %com.example.word.ui.quiz.QuizFragment  QuestionType %com.example.word.ui.quiz.QuizFragment  
QuizConfig %com.example.word.ui.quiz.QuizFragment  
QuizSource %com.example.word.ui.quiz.QuizFragment  	QuizState %com.example.word.ui.quiz.QuizFragment  Toast %com.example.word.ui.quiz.QuizFragment  View %com.example.word.ui.quiz.QuizFragment  _binding %com.example.word.ui.quiz.QuizFragment  binding %com.example.word.ui.quiz.QuizFragment  currentQuestion %com.example.word.ui.quiz.QuizFragment  displayQuestion %com.example.word.ui.quiz.QuizFragment  
displayResult %com.example.word.ui.quiz.QuizFragment  
formatTime %com.example.word.ui.quiz.QuizFragment  getValue %com.example.word.ui.quiz.QuizFragment  
isNotEmpty %com.example.word.ui.quiz.QuizFragment  let %com.example.word.ui.quiz.QuizFragment  provideDelegate %com.example.word.ui.quiz.QuizFragment  
quizViewModel %com.example.word.ui.quiz.QuizFragment  requireContext %com.example.word.ui.quiz.QuizFragment  run %com.example.word.ui.quiz.QuizFragment  setupClickListeners %com.example.word.ui.quiz.QuizFragment  setupObservers %com.example.word.ui.quiz.QuizFragment  showLoading %com.example.word.ui.quiz.QuizFragment  showMultipleChoiceOptions %com.example.word.ui.quiz.QuizFragment  showQuizInProgress %com.example.word.ui.quiz.QuizFragment  showQuizResult %com.example.word.ui.quiz.QuizFragment  
showQuizSetup %com.example.word.ui.quiz.QuizFragment  showTextInputOption %com.example.word.ui.quiz.QuizFragment  	startQuiz %com.example.word.ui.quiz.QuizFragment  submitAnswer %com.example.word.ui.quiz.QuizFragment  trim %com.example.word.ui.quiz.QuizFragment  updateProgress %com.example.word.ui.quiz.QuizFragment  viewLifecycleOwner %com.example.word.ui.quiz.QuizFragment  
viewModels %com.example.word.ui.quiz.QuizFragment  correctAnswers %com.example.word.ui.quiz.QuizProgress  currentQuestion %com.example.word.ui.quiz.QuizProgress  totalQuestions %com.example.word.ui.quiz.QuizProgress  
correctAnswer %com.example.word.ui.quiz.QuizQuestion  hint %com.example.word.ui.quiz.QuizQuestion  let %com.example.word.ui.quiz.QuizQuestion  options %com.example.word.ui.quiz.QuizQuestion  question %com.example.word.ui.quiz.QuizQuestion  type %com.example.word.ui.quiz.QuizQuestion  word %com.example.word.ui.quiz.QuizQuestion  accuracy #com.example.word.ui.quiz.QuizResult  averageTimePerQuestion #com.example.word.ui.quiz.QuizResult  correctAnswers #com.example.word.ui.quiz.QuizResult  let #com.example.word.ui.quiz.QuizResult  totalQuestions #com.example.word.ui.quiz.QuizResult  	totalTime #com.example.word.ui.quiz.QuizResult  
BOOKMARKED #com.example.word.ui.quiz.QuizSource  
DIFFICULTY #com.example.word.ui.quiz.QuizSource  RANDOM #com.example.word.ui.quiz.QuizSource  REVIEW #com.example.word.ui.quiz.QuizSource  	COMPLETED "com.example.word.ui.quiz.QuizState  IDLE "com.example.word.ui.quiz.QuizState  IN_PROGRESS "com.example.word.ui.quiz.QuizState  LOADING "com.example.word.ui.quiz.QuizState  MutableLiveData &com.example.word.ui.quiz.QuizViewModel  QuestionType &com.example.word.ui.quiz.QuizViewModel  QuizProgress &com.example.word.ui.quiz.QuizViewModel  QuizQuestion &com.example.word.ui.quiz.QuizViewModel  
QuizResult &com.example.word.ui.quiz.QuizViewModel  
QuizSource &com.example.word.ui.quiz.QuizViewModel  	QuizState &com.example.word.ui.quiz.QuizViewModel  Random &com.example.word.ui.quiz.QuizViewModel  StudySession &com.example.word.ui.quiz.QuizViewModel  System &com.example.word.ui.quiz.QuizViewModel  WordDatabase &com.example.word.ui.quiz.QuizViewModel  WordRepository &com.example.word.ui.quiz.QuizViewModel  _currentQuestion &com.example.word.ui.quiz.QuizViewModel  
_errorMessage &com.example.word.ui.quiz.QuizViewModel  
_isLoading &com.example.word.ui.quiz.QuizViewModel  
_quizProgress &com.example.word.ui.quiz.QuizViewModel  _quizResult &com.example.word.ui.quiz.QuizViewModel  
_quizState &com.example.word.ui.quiz.QuizViewModel  any &com.example.word.ui.quiz.QuizViewModel  checkAnswer &com.example.word.ui.quiz.QuizViewModel  clearErrorMessage &com.example.word.ui.quiz.QuizViewModel  coerceIn &com.example.word.ui.quiz.QuizViewModel  correctAnswers &com.example.word.ui.quiz.QuizViewModel  currentQuestion &com.example.word.ui.quiz.QuizViewModel  currentQuestionIndex &com.example.word.ui.quiz.QuizViewModel  distinct &com.example.word.ui.quiz.QuizViewModel  	emptyList &com.example.word.ui.quiz.QuizViewModel  equals &com.example.word.ui.quiz.QuizViewModel  errorMessage &com.example.word.ui.quiz.QuizViewModel  filter &com.example.word.ui.quiz.QuizViewModel  
finishQuiz &com.example.word.ui.quiz.QuizViewModel  first &com.example.word.ui.quiz.QuizViewModel  generateFillBlankQuestion &com.example.word.ui.quiz.QuizViewModel  generateMultipleChoiceQuestion &com.example.word.ui.quiz.QuizViewModel  generateNextQuestion &com.example.word.ui.quiz.QuizViewModel  generateQuestionByType &com.example.word.ui.quiz.QuizViewModel  generateTranslationQuestion &com.example.word.ui.quiz.QuizViewModel  getDatabase &com.example.word.ui.quiz.QuizViewModel  	isLoading &com.example.word.ui.quiz.QuizViewModel  
isNotEmpty &com.example.word.ui.quiz.QuizViewModel  launch &com.example.word.ui.quiz.QuizViewModel  map &com.example.word.ui.quiz.QuizViewModel  nextBoolean &com.example.word.ui.quiz.QuizViewModel  nextInt &com.example.word.ui.quiz.QuizViewModel  plus &com.example.word.ui.quiz.QuizViewModel  questionStartTime &com.example.word.ui.quiz.QuizViewModel  quizProgress &com.example.word.ui.quiz.QuizViewModel  
quizResult &com.example.word.ui.quiz.QuizViewModel  	quizState &com.example.word.ui.quiz.QuizViewModel  	quizWords &com.example.word.ui.quiz.QuizViewModel  recordStudySession &com.example.word.ui.quiz.QuizViewModel  replace &com.example.word.ui.quiz.QuizViewModel  
repository &com.example.word.ui.quiz.QuizViewModel  	resetQuiz &com.example.word.ui.quiz.QuizViewModel  shuffled &com.example.word.ui.quiz.QuizViewModel  split &com.example.word.ui.quiz.QuizViewModel  	startQuiz &com.example.word.ui.quiz.QuizViewModel  	startTime &com.example.word.ui.quiz.QuizViewModel  submitAnswer &com.example.word.ui.quiz.QuizViewModel  take &com.example.word.ui.quiz.QuizViewModel  trim &com.example.word.ui.quiz.QuizViewModel  	uppercase &com.example.word.ui.quiz.QuizViewModel  viewModelScope &com.example.word.ui.quiz.QuizViewModel  AppCompatDelegate com.example.word.ui.settings  Bundle com.example.word.ui.settings  Context com.example.word.ui.settings  Fragment com.example.word.ui.settings  FragmentSettingsBinding com.example.word.ui.settings  Int com.example.word.ui.settings  LayoutInflater com.example.word.ui.settings  Long com.example.word.ui.settings  OnlineTTSHelper com.example.word.ui.settings  SettingsFragment com.example.word.ui.settings  SharedPreferences com.example.word.ui.settings  String com.example.word.ui.settings  StudyNotificationManager com.example.word.ui.settings  TimePickerDialog com.example.word.ui.settings  Toast com.example.word.ui.settings  View com.example.word.ui.settings  	ViewGroup com.example.word.ui.settings  android com.example.word.ui.settings  androidx com.example.word.ui.settings  apply com.example.word.ui.settings  arrayOf com.example.word.ui.settings  cancelStudyReminder com.example.word.ui.settings  createNotificationChannel com.example.word.ui.settings  format com.example.word.ui.settings  getTTSAudio com.example.word.ui.settings  scheduleStudyReminder com.example.word.ui.settings  sharedPreferences com.example.word.ui.settings  
simpleAPITest com.example.word.ui.settings  AppCompatDelegate -com.example.word.ui.settings.SettingsFragment  Context -com.example.word.ui.settings.SettingsFragment  FragmentSettingsBinding -com.example.word.ui.settings.SettingsFragment  OnlineTTSHelper -com.example.word.ui.settings.SettingsFragment  String -com.example.word.ui.settings.SettingsFragment  StudyNotificationManager -com.example.word.ui.settings.SettingsFragment  TimePickerDialog -com.example.word.ui.settings.SettingsFragment  Toast -com.example.word.ui.settings.SettingsFragment  View -com.example.word.ui.settings.SettingsFragment  _binding -com.example.word.ui.settings.SettingsFragment  android -com.example.word.ui.settings.SettingsFragment  androidx -com.example.word.ui.settings.SettingsFragment  apply -com.example.word.ui.settings.SettingsFragment  arrayOf -com.example.word.ui.settings.SettingsFragment  binding -com.example.word.ui.settings.SettingsFragment  cancelStudyReminder -com.example.word.ui.settings.SettingsFragment  
clearUserData -com.example.word.ui.settings.SettingsFragment  createNotificationChannel -com.example.word.ui.settings.SettingsFragment  exportUserData -com.example.word.ui.settings.SettingsFragment  format -com.example.word.ui.settings.SettingsFragment  getTTSAudio -com.example.word.ui.settings.SettingsFragment  importUserData -com.example.word.ui.settings.SettingsFragment  loadSettings -com.example.word.ui.settings.SettingsFragment  requireContext -com.example.word.ui.settings.SettingsFragment  scheduleNotification -com.example.word.ui.settings.SettingsFragment  scheduleStudyReminder -com.example.word.ui.settings.SettingsFragment  setupClickListeners -com.example.word.ui.settings.SettingsFragment  
setupViews -com.example.word.ui.settings.SettingsFragment  sharedPreferences -com.example.word.ui.settings.SettingsFragment  showAboutDialog -com.example.word.ui.settings.SettingsFragment  showClearDataDialog -com.example.word.ui.settings.SettingsFragment  showTimePickerDialog -com.example.word.ui.settings.SettingsFragment  
simpleAPITest -com.example.word.ui.settings.SettingsFragment  
testTTSAPI -com.example.word.ui.settings.SettingsFragment  testTTSAudio -com.example.word.ui.settings.SettingsFragment  widget $com.example.word.ui.settings.android  AdapterView +com.example.word.ui.settings.android.widget  OnItemSelectedListener 7com.example.word.ui.settings.android.widget.AdapterView  AndroidViewModel com.example.word.ui.viewmodel  Application com.example.word.ui.viewmodel  Boolean com.example.word.ui.viewmodel  	Exception com.example.word.ui.viewmodel  Int com.example.word.ui.viewmodel  List com.example.word.ui.viewmodel  LiveData com.example.word.ui.viewmodel  Long com.example.word.ui.viewmodel  Math com.example.word.ui.viewmodel  MutableLiveData com.example.word.ui.viewmodel  Phrase com.example.word.ui.viewmodel  PhraseViewModel com.example.word.ui.viewmodel  String com.example.word.ui.viewmodel  StudySession com.example.word.ui.viewmodel  System com.example.word.ui.viewmodel  Word com.example.word.ui.viewmodel  WordDatabase com.example.word.ui.viewmodel  WordRepository com.example.word.ui.viewmodel  
WordViewModel com.example.word.ui.viewmodel  _currentQuizIndex com.example.word.ui.viewmodel  _currentWord com.example.word.ui.viewmodel  
_errorMessage com.example.word.ui.viewmodel  _filteredPhrases com.example.word.ui.viewmodel  
_isLoading com.example.word.ui.viewmodel  _phraseCategories com.example.word.ui.viewmodel  _phraseTypes com.example.word.ui.viewmodel  
_quizScore com.example.word.ui.viewmodel  
_quizWords com.example.word.ui.viewmodel  _searchResults com.example.word.ui.viewmodel  _selectedCategory com.example.word.ui.viewmodel  _selectedDifficulty com.example.word.ui.viewmodel  
_selectedType com.example.word.ui.viewmodel  
_studyMode com.example.word.ui.viewmodel  _wordsForReview com.example.word.ui.viewmodel  
allPhrases com.example.word.ui.viewmodel  calculateNewAccuracy com.example.word.ui.viewmodel  
coerceAtLeast com.example.word.ui.viewmodel  coerceAtMost com.example.word.ui.viewmodel  coerceIn com.example.word.ui.viewmodel  	emptyList com.example.word.ui.viewmodel  filter com.example.word.ui.viewmodel  getDatabase com.example.word.ui.viewmodel  isBlank com.example.word.ui.viewmodel  
isNotEmpty com.example.word.ui.viewmodel  launch com.example.word.ui.viewmodel  maxOf com.example.word.ui.viewmodel  
repository com.example.word.ui.viewmodel  take com.example.word.ui.viewmodel  updateSpacedRepetition com.example.word.ui.viewmodel  updateUserProgress com.example.word.ui.viewmodel  MutableLiveData -com.example.word.ui.viewmodel.PhraseViewModel  StudySession -com.example.word.ui.viewmodel.PhraseViewModel  System -com.example.word.ui.viewmodel.PhraseViewModel  WordDatabase -com.example.word.ui.viewmodel.PhraseViewModel  WordRepository -com.example.word.ui.viewmodel.PhraseViewModel  _currentPhrase -com.example.word.ui.viewmodel.PhraseViewModel  
_errorMessage -com.example.word.ui.viewmodel.PhraseViewModel  _filteredPhrases -com.example.word.ui.viewmodel.PhraseViewModel  
_isLoading -com.example.word.ui.viewmodel.PhraseViewModel  _phraseCategories -com.example.word.ui.viewmodel.PhraseViewModel  _phraseTypes -com.example.word.ui.viewmodel.PhraseViewModel  _searchResults -com.example.word.ui.viewmodel.PhraseViewModel  _selectedCategory -com.example.word.ui.viewmodel.PhraseViewModel  _selectedDifficulty -com.example.word.ui.viewmodel.PhraseViewModel  
_selectedType -com.example.word.ui.viewmodel.PhraseViewModel  
allPhrases -com.example.word.ui.viewmodel.PhraseViewModel  applyFilters -com.example.word.ui.viewmodel.PhraseViewModel  bookmarkedPhrases -com.example.word.ui.viewmodel.PhraseViewModel  clearErrorMessage -com.example.word.ui.viewmodel.PhraseViewModel  	emptyList -com.example.word.ui.viewmodel.PhraseViewModel  errorMessage -com.example.word.ui.viewmodel.PhraseViewModel  filter -com.example.word.ui.viewmodel.PhraseViewModel  getDatabase -com.example.word.ui.viewmodel.PhraseViewModel  getRandomPhrases -com.example.word.ui.viewmodel.PhraseViewModel  isBlank -com.example.word.ui.viewmodel.PhraseViewModel  	isLoading -com.example.word.ui.viewmodel.PhraseViewModel  launch -com.example.word.ui.viewmodel.PhraseViewModel  loadPhraseCategories -com.example.word.ui.viewmodel.PhraseViewModel  loadPhraseTypes -com.example.word.ui.viewmodel.PhraseViewModel  
repository -com.example.word.ui.viewmodel.PhraseViewModel  setCurrentPhrase -com.example.word.ui.viewmodel.PhraseViewModel  toggleBookmark -com.example.word.ui.viewmodel.PhraseViewModel  viewModelScope -com.example.word.ui.viewmodel.PhraseViewModel  Math +com.example.word.ui.viewmodel.WordViewModel  MutableLiveData +com.example.word.ui.viewmodel.WordViewModel  StudySession +com.example.word.ui.viewmodel.WordViewModel  System +com.example.word.ui.viewmodel.WordViewModel  WordDatabase +com.example.word.ui.viewmodel.WordViewModel  WordRepository +com.example.word.ui.viewmodel.WordViewModel  _currentQuizIndex +com.example.word.ui.viewmodel.WordViewModel  _currentWord +com.example.word.ui.viewmodel.WordViewModel  
_errorMessage +com.example.word.ui.viewmodel.WordViewModel  
_isLoading +com.example.word.ui.viewmodel.WordViewModel  
_quizScore +com.example.word.ui.viewmodel.WordViewModel  
_quizWords +com.example.word.ui.viewmodel.WordViewModel  _searchResults +com.example.word.ui.viewmodel.WordViewModel  
_studyMode +com.example.word.ui.viewmodel.WordViewModel  _wordsForReview +com.example.word.ui.viewmodel.WordViewModel  allWords +com.example.word.ui.viewmodel.WordViewModel  bookmarkedWords +com.example.word.ui.viewmodel.WordViewModel  calculateNewAccuracy +com.example.word.ui.viewmodel.WordViewModel  clearErrorMessage +com.example.word.ui.viewmodel.WordViewModel  
coerceAtLeast +com.example.word.ui.viewmodel.WordViewModel  coerceAtMost +com.example.word.ui.viewmodel.WordViewModel  coerceIn +com.example.word.ui.viewmodel.WordViewModel  	emptyList +com.example.word.ui.viewmodel.WordViewModel  errorMessage +com.example.word.ui.viewmodel.WordViewModel  filter +com.example.word.ui.viewmodel.WordViewModel  
finishQuiz +com.example.word.ui.viewmodel.WordViewModel  getDatabase +com.example.word.ui.viewmodel.WordViewModel  isBlank +com.example.word.ui.viewmodel.WordViewModel  	isLoading +com.example.word.ui.viewmodel.WordViewModel  
isNotEmpty +com.example.word.ui.viewmodel.WordViewModel  launch +com.example.word.ui.viewmodel.WordViewModel  loadWordsForReview +com.example.word.ui.viewmodel.WordViewModel  maxOf +com.example.word.ui.viewmodel.WordViewModel  nextQuizQuestion +com.example.word.ui.viewmodel.WordViewModel  recordStudyResult +com.example.word.ui.viewmodel.WordViewModel  
repository +com.example.word.ui.viewmodel.WordViewModel  
searchResults +com.example.word.ui.viewmodel.WordViewModel  searchWords +com.example.word.ui.viewmodel.WordViewModel  setCurrentWord +com.example.word.ui.viewmodel.WordViewModel  take +com.example.word.ui.viewmodel.WordViewModel  toggleBookmark +com.example.word.ui.viewmodel.WordViewModel  updateSpacedRepetition +com.example.word.ui.viewmodel.WordViewModel  updateUserProgress +com.example.word.ui.viewmodel.WordViewModel  viewModelScope +com.example.word.ui.viewmodel.WordViewModel  wordsForReview +com.example.word.ui.viewmodel.WordViewModel  Boolean com.example.word.ui.vocabulary  Bundle com.example.word.ui.vocabulary  DiffUtil com.example.word.ui.vocabulary  Fragment com.example.word.ui.vocabulary  FragmentVocabularyBinding com.example.word.ui.vocabulary  Int com.example.word.ui.vocabulary  ItemWordBinding com.example.word.ui.vocabulary  LayoutInflater com.example.word.ui.vocabulary  LinearLayoutManager com.example.word.ui.vocabulary  ListAdapter com.example.word.ui.vocabulary  R com.example.word.ui.vocabulary  RecyclerView com.example.word.ui.vocabulary  String com.example.word.ui.vocabulary  
TTSManager com.example.word.ui.vocabulary  Toast com.example.word.ui.vocabulary  Unit com.example.word.ui.vocabulary  View com.example.word.ui.vocabulary  	ViewGroup com.example.word.ui.vocabulary  VocabularyFragment com.example.word.ui.vocabulary  Word com.example.word.ui.vocabulary  WordAdapter com.example.word.ui.vocabulary  WordViewHolder com.example.word.ui.vocabulary  
WordViewModel com.example.word.ui.vocabulary  android com.example.word.ui.vocabulary  androidx com.example.word.ui.vocabulary  apply com.example.word.ui.vocabulary  getValue com.example.word.ui.vocabulary  
isInitialized com.example.word.ui.vocabulary  
isNullOrBlank com.example.word.ui.vocabulary  let com.example.word.ui.vocabulary  onBookmarkClick com.example.word.ui.vocabulary  onSpeakClick com.example.word.ui.vocabulary  onWordClick com.example.word.ui.vocabulary  provideDelegate com.example.word.ui.vocabulary  requireContext com.example.word.ui.vocabulary  viewLifecycleOwner com.example.word.ui.vocabulary  wordAdapter com.example.word.ui.vocabulary  
wordViewModel com.example.word.ui.vocabulary  ItemCallback 'com.example.word.ui.vocabulary.DiffUtil  
ViewHolder +com.example.word.ui.vocabulary.RecyclerView  FragmentVocabularyBinding 1com.example.word.ui.vocabulary.VocabularyFragment  LinearLayoutManager 1com.example.word.ui.vocabulary.VocabularyFragment  
TTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  Toast 1com.example.word.ui.vocabulary.VocabularyFragment  View 1com.example.word.ui.vocabulary.VocabularyFragment  WordAdapter 1com.example.word.ui.vocabulary.VocabularyFragment  _binding 1com.example.word.ui.vocabulary.VocabularyFragment  android 1com.example.word.ui.vocabulary.VocabularyFragment  apply 1com.example.word.ui.vocabulary.VocabularyFragment  binding 1com.example.word.ui.vocabulary.VocabularyFragment  getValue 1com.example.word.ui.vocabulary.VocabularyFragment  initTTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  
isInitialized 1com.example.word.ui.vocabulary.VocabularyFragment  
isNullOrBlank 1com.example.word.ui.vocabulary.VocabularyFragment  let 1com.example.word.ui.vocabulary.VocabularyFragment  provideDelegate 1com.example.word.ui.vocabulary.VocabularyFragment  requireContext 1com.example.word.ui.vocabulary.VocabularyFragment  setupClickListeners 1com.example.word.ui.vocabulary.VocabularyFragment  setupObservers 1com.example.word.ui.vocabulary.VocabularyFragment  setupRecyclerView 1com.example.word.ui.vocabulary.VocabularyFragment  setupSearchView 1com.example.word.ui.vocabulary.VocabularyFragment  showBookmarkedWords 1com.example.word.ui.vocabulary.VocabularyFragment  showFilterDialog 1com.example.word.ui.vocabulary.VocabularyFragment  showReviewWords 1com.example.word.ui.vocabulary.VocabularyFragment  	speakWord 1com.example.word.ui.vocabulary.VocabularyFragment  startRandomStudy 1com.example.word.ui.vocabulary.VocabularyFragment  
ttsManager 1com.example.word.ui.vocabulary.VocabularyFragment  updateEmptyState 1com.example.word.ui.vocabulary.VocabularyFragment  viewLifecycleOwner 1com.example.word.ui.vocabulary.VocabularyFragment  
viewModels 1com.example.word.ui.vocabulary.VocabularyFragment  wordAdapter 1com.example.word.ui.vocabulary.VocabularyFragment  
wordViewModel 1com.example.word.ui.vocabulary.VocabularyFragment  Boolean *com.example.word.ui.vocabulary.WordAdapter  DiffUtil *com.example.word.ui.vocabulary.WordAdapter  Int *com.example.word.ui.vocabulary.WordAdapter  ItemWordBinding *com.example.word.ui.vocabulary.WordAdapter  LayoutInflater *com.example.word.ui.vocabulary.WordAdapter  R *com.example.word.ui.vocabulary.WordAdapter  RecyclerView *com.example.word.ui.vocabulary.WordAdapter  String *com.example.word.ui.vocabulary.WordAdapter  Unit *com.example.word.ui.vocabulary.WordAdapter  	ViewGroup *com.example.word.ui.vocabulary.WordAdapter  Word *com.example.word.ui.vocabulary.WordAdapter  WordDiffCallback *com.example.word.ui.vocabulary.WordAdapter  WordViewHolder *com.example.word.ui.vocabulary.WordAdapter  android *com.example.word.ui.vocabulary.WordAdapter  apply *com.example.word.ui.vocabulary.WordAdapter  getItem *com.example.word.ui.vocabulary.WordAdapter  onBookmarkClick *com.example.word.ui.vocabulary.WordAdapter  onSpeakClick *com.example.word.ui.vocabulary.WordAdapter  onWordClick *com.example.word.ui.vocabulary.WordAdapter  
submitList *com.example.word.ui.vocabulary.WordAdapter  ItemCallback 3com.example.word.ui.vocabulary.WordAdapter.DiffUtil  
ViewHolder 7com.example.word.ui.vocabulary.WordAdapter.RecyclerView  R 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  android 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  apply 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  bind 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  binding 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  onBookmarkClick 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  onSpeakClick 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  onWordClick 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  	appcompat 'com.example.word.ui.vocabulary.androidx  widget 1com.example.word.ui.vocabulary.androidx.appcompat  
SearchView 8com.example.word.ui.vocabulary.androidx.appcompat.widget  OnQueryTextListener Ccom.example.word.ui.vocabulary.androidx.appcompat.widget.SearchView  API_KEY com.example.word.utils  Achievement com.example.word.utils  AchievementCategory com.example.word.utils  AlarmManager com.example.word.utils  AttributeSet com.example.word.utils  AudioAttributes com.example.word.utils  Boolean com.example.word.utils  BroadcastReceiver com.example.word.utils  Build com.example.word.utils  	ByteArray com.example.word.utils  Calendar com.example.word.utils  Canvas com.example.word.utils  CircularProgressChart com.example.word.utils  Context com.example.word.utils  
ContextCompat com.example.word.utils  CoroutineScope com.example.word.utils  DifficultyLevel com.example.word.utils  Dispatchers com.example.word.utils  	Exception com.example.word.utils  ExperienceType com.example.word.utils  Float com.example.word.utils  GameificationManager com.example.word.utils  Int com.example.word.utils  Intent com.example.word.utils  JvmOverloads com.example.word.utils  List com.example.word.utils  Locale com.example.word.utils  Log com.example.word.utils  Long com.example.word.utils  MainActivity com.example.word.utils  Math com.example.word.utils  MediaPlayer com.example.word.utils  NOTIFICATION_ID com.example.word.utils  NotificationChannel com.example.word.utils  NotificationCompat com.example.word.utils  NotificationManager com.example.word.utils  NotificationManagerCompat com.example.word.utils  OkHttpClient com.example.word.utils  OnlineTTSHelper com.example.word.utils  Paint com.example.word.utils  Pair com.example.word.utils  Path com.example.word.utils  
PendingIntent com.example.word.utils  R com.example.word.utils  RectF com.example.word.utils  Request com.example.word.utils  ReviewResult com.example.word.utils  SpacedRepetitionAlgorithm com.example.word.utils  String com.example.word.utils  StudyDataPoint com.example.word.utils  StudyNotificationManager com.example.word.utils  StudyProgressChart com.example.word.utils  StudyReminderReceiver com.example.word.utils  StudySession com.example.word.utils  
StudyStats com.example.word.utils  
SupervisorJob com.example.word.utils  System com.example.word.utils  TAG com.example.word.utils  
TTSManager com.example.word.utils  TTS_API_URL com.example.word.utils  TextToSpeech com.example.word.utils  Unit com.example.word.utils  	UserLevel com.example.word.utils  View com.example.word.utils  android com.example.word.utils  
appendLine com.example.word.utils  apply com.example.word.utils  buildString com.example.word.utils  cancel com.example.word.utils  coerceIn com.example.word.utils  contains com.example.word.utils  context com.example.word.utils  	emptyList com.example.word.utils  fallbackToSystemTTS com.example.word.utils  filter com.example.word.utils  format com.example.word.utils  
httpClient com.example.word.utils  indices com.example.word.utils  isBlank com.example.word.utils  
isNotEmpty com.example.word.utils  java com.example.word.utils  kotlin com.example.word.utils  launch com.example.word.utils  let com.example.word.utils  listOf com.example.word.utils  map com.example.word.utils  max com.example.word.utils  maxOf com.example.word.utils  min com.example.word.utils  minOf com.example.word.utils  
mutableListOf com.example.word.utils  parseAndPlayAudio com.example.word.utils  
playAudioData com.example.word.utils  showStudyReminderNotification com.example.word.utils  
startsWith com.example.word.utils  take com.example.word.utils  toByteArray com.example.word.utils  	toHttpUrl com.example.word.utils  use com.example.word.utils  with com.example.word.utils  withContext com.example.word.utils  
writeBytes com.example.word.utils  
ContextCompat ,com.example.word.utils.CircularProgressChart  Paint ,com.example.word.utils.CircularProgressChart  RectF ,com.example.word.utils.CircularProgressChart  android ,com.example.word.utils.CircularProgressChart  apply ,com.example.word.utils.CircularProgressChart  backgroundPaint ,com.example.word.utils.CircularProgressChart  context ,com.example.word.utils.CircularProgressChart  height ,com.example.word.utils.CircularProgressChart  
invalidate ,com.example.word.utils.CircularProgressChart  maxProgress ,com.example.word.utils.CircularProgressChart  min ,com.example.word.utils.CircularProgressChart  progress ,com.example.word.utils.CircularProgressChart  
progressPaint ,com.example.word.utils.CircularProgressChart  setProgress ,com.example.word.utils.CircularProgressChart  setupPaints ,com.example.word.utils.CircularProgressChart  strokeWidth ,com.example.word.utils.CircularProgressChart  subtitle ,com.example.word.utils.CircularProgressChart  	textPaint ,com.example.word.utils.CircularProgressChart  title ,com.example.word.utils.CircularProgressChart  width ,com.example.word.utils.CircularProgressChart  Achievement +com.example.word.utils.GameificationManager  AchievementCategory +com.example.word.utils.GameificationManager  Boolean +com.example.word.utils.GameificationManager  ExperienceType +com.example.word.utils.GameificationManager  Float +com.example.word.utils.GameificationManager  Int +com.example.word.utils.GameificationManager  List +com.example.word.utils.GameificationManager  R +com.example.word.utils.GameificationManager  String +com.example.word.utils.GameificationManager  
StudyStats +com.example.word.utils.GameificationManager  	UserLevel +com.example.word.utils.GameificationManager  calculateUserLevel +com.example.word.utils.GameificationManager  createConsecutiveAchievements +com.example.word.utils.GameificationManager  createExperienceAchievements +com.example.word.utils.GameificationManager  createStudyDaysAchievements +com.example.word.utils.GameificationManager  createVocabularyAchievements +com.example.word.utils.GameificationManager  filter +com.example.word.utils.GameificationManager  getExperienceRequiredForLevel +com.example.word.utils.GameificationManager  getLevelIcon +com.example.word.utils.GameificationManager  
getLevelTitle +com.example.word.utils.GameificationManager  listOf +com.example.word.utils.GameificationManager  map +com.example.word.utils.GameificationManager  minOf +com.example.word.utils.GameificationManager  
mutableListOf +com.example.word.utils.GameificationManager  currentValue 7com.example.word.utils.GameificationManager.Achievement  description 7com.example.word.utils.GameificationManager.Achievement  id 7com.example.word.utils.GameificationManager.Achievement  
isUnlocked 7com.example.word.utils.GameificationManager.Achievement  targetValue 7com.example.word.utils.GameificationManager.Achievement  title 7com.example.word.utils.GameificationManager.Achievement  CONSECUTIVE ?com.example.word.utils.GameificationManager.AchievementCategory  
EXPERIENCE ?com.example.word.utils.GameificationManager.AchievementCategory  
STUDY_DAYS ?com.example.word.utils.GameificationManager.AchievementCategory  
VOCABULARY ?com.example.word.utils.GameificationManager.AchievementCategory  points :com.example.word.utils.GameificationManager.ExperienceType  consecutiveStudyDays 6com.example.word.utils.GameificationManager.StudyStats  totalStudyDays 6com.example.word.utils.GameificationManager.StudyStats  totalWordsLearned 6com.example.word.utils.GameificationManager.StudyStats  currentExperience 5com.example.word.utils.GameificationManager.UserLevel  experienceRequired 5com.example.word.utils.GameificationManager.UserLevel  level 5com.example.word.utils.GameificationManager.UserLevel  title 5com.example.word.utils.GameificationManager.UserLevel  API_KEY &com.example.word.utils.OnlineTTSHelper  CoroutineScope &com.example.word.utils.OnlineTTSHelper  Dispatchers &com.example.word.utils.OnlineTTSHelper  Log &com.example.word.utils.OnlineTTSHelper  OkHttpClient &com.example.word.utils.OnlineTTSHelper  Request &com.example.word.utils.OnlineTTSHelper  TAG &com.example.word.utils.OnlineTTSHelper  TTS_API_URL &com.example.word.utils.OnlineTTSHelper  
appendLine &com.example.word.utils.OnlineTTSHelper  buildString &com.example.word.utils.OnlineTTSHelper  contains &com.example.word.utils.OnlineTTSHelper  getTTSAudio &com.example.word.utils.OnlineTTSHelper  
httpClient &com.example.word.utils.OnlineTTSHelper  
isNotEmpty &com.example.word.utils.OnlineTTSHelper  java &com.example.word.utils.OnlineTTSHelper  launch &com.example.word.utils.OnlineTTSHelper  
simpleAPITest &com.example.word.utils.OnlineTTSHelper  take &com.example.word.utils.OnlineTTSHelper  	toHttpUrl &com.example.word.utils.OnlineTTSHelper  use &com.example.word.utils.OnlineTTSHelper  withContext &com.example.word.utils.OnlineTTSHelper  INITIAL_INTERVAL 0com.example.word.utils.SpacedRepetitionAlgorithm  MAX_EASINESS_FACTOR 0com.example.word.utils.SpacedRepetitionAlgorithm  MIN_EASINESS_FACTOR 0com.example.word.utils.SpacedRepetitionAlgorithm  Math 0com.example.word.utils.SpacedRepetitionAlgorithm  Pair 0com.example.word.utils.SpacedRepetitionAlgorithm  ReviewResult 0com.example.word.utils.SpacedRepetitionAlgorithm  SECOND_INTERVAL 0com.example.word.utils.SpacedRepetitionAlgorithm  System 0com.example.word.utils.SpacedRepetitionAlgorithm  addRandomFactor 0com.example.word.utils.SpacedRepetitionAlgorithm  calculateEasinessFactor 0com.example.word.utils.SpacedRepetitionAlgorithm  calculateForgettingProbability 0com.example.word.utils.SpacedRepetitionAlgorithm  coerceIn 0com.example.word.utils.SpacedRepetitionAlgorithm  kotlin 0com.example.word.utils.SpacedRepetitionAlgorithm  max 0com.example.word.utils.SpacedRepetitionAlgorithm  label %com.example.word.utils.StudyDataPoint  value %com.example.word.utils.StudyDataPoint  AlarmManager /com.example.word.utils.StudyNotificationManager  Build /com.example.word.utils.StudyNotificationManager  
CHANNEL_ID /com.example.word.utils.StudyNotificationManager  Calendar /com.example.word.utils.StudyNotificationManager  Context /com.example.word.utils.StudyNotificationManager  Intent /com.example.word.utils.StudyNotificationManager  MainActivity /com.example.word.utils.StudyNotificationManager  NOTIFICATION_ID /com.example.word.utils.StudyNotificationManager  NotificationChannel /com.example.word.utils.StudyNotificationManager  NotificationCompat /com.example.word.utils.StudyNotificationManager  NotificationManager /com.example.word.utils.StudyNotificationManager  NotificationManagerCompat /com.example.word.utils.StudyNotificationManager  
PendingIntent /com.example.word.utils.StudyNotificationManager  R /com.example.word.utils.StudyNotificationManager  REQUEST_CODE /com.example.word.utils.StudyNotificationManager  StudyReminderReceiver /com.example.word.utils.StudyNotificationManager  System /com.example.word.utils.StudyNotificationManager  apply /com.example.word.utils.StudyNotificationManager  cancelStudyReminder /com.example.word.utils.StudyNotificationManager  createNotificationChannel /com.example.word.utils.StudyNotificationManager  java /com.example.word.utils.StudyNotificationManager  scheduleStudyReminder /com.example.word.utils.StudyNotificationManager  showStudyReminderNotification /com.example.word.utils.StudyNotificationManager  with /com.example.word.utils.StudyNotificationManager  
ContextCompat )com.example.word.utils.StudyProgressChart  Paint )com.example.word.utils.StudyProgressChart  Path )com.example.word.utils.StudyProgressChart  String )com.example.word.utils.StudyProgressChart  android )com.example.word.utils.StudyProgressChart  apply )com.example.word.utils.StudyProgressChart  context )com.example.word.utils.StudyProgressChart  data )com.example.word.utils.StudyProgressChart  drawAxes )com.example.word.utils.StudyProgressChart  drawData )com.example.word.utils.StudyProgressChart  drawEmptyState )com.example.word.utils.StudyProgressChart  drawGrid )com.example.word.utils.StudyProgressChart  
drawLabels )com.example.word.utils.StudyProgressChart  	emptyList )com.example.word.utils.StudyProgressChart  format )com.example.word.utils.StudyProgressChart  	gridPaint )com.example.word.utils.StudyProgressChart  height )com.example.word.utils.StudyProgressChart  indices )com.example.word.utils.StudyProgressChart  
invalidate )com.example.word.utils.StudyProgressChart  
isNotEmpty )com.example.word.utils.StudyProgressChart  max )com.example.word.utils.StudyProgressChart  maxOf )com.example.word.utils.StudyProgressChart  maxValue )com.example.word.utils.StudyProgressChart  minOf )com.example.word.utils.StudyProgressChart  minValue )com.example.word.utils.StudyProgressChart  padding )com.example.word.utils.StudyProgressChart  paint )com.example.word.utils.StudyProgressChart  setData )com.example.word.utils.StudyProgressChart  setupPaints )com.example.word.utils.StudyProgressChart  	textPaint )com.example.word.utils.StudyProgressChart  textSize )com.example.word.utils.StudyProgressChart  width )com.example.word.utils.StudyProgressChart  StudyNotificationManager ,com.example.word.utils.StudyReminderReceiver  showStudyReminderNotification ,com.example.word.utils.StudyReminderReceiver  endTime #com.example.word.utils.StudySession  	startTime #com.example.word.utils.StudySession  API_KEY !com.example.word.utils.TTSManager  AudioAttributes !com.example.word.utils.TTSManager  Boolean !com.example.word.utils.TTSManager  	ByteArray !com.example.word.utils.TTSManager  Context !com.example.word.utils.TTSManager  CoroutineScope !com.example.word.utils.TTSManager  Dispatchers !com.example.word.utils.TTSManager  	Exception !com.example.word.utils.TTSManager  Int !com.example.word.utils.TTSManager  Locale !com.example.word.utils.TTSManager  Log !com.example.word.utils.TTSManager  MediaPlayer !com.example.word.utils.TTSManager  OkHttpClient !com.example.word.utils.TTSManager  Request !com.example.word.utils.TTSManager  String !com.example.word.utils.TTSManager  
SupervisorJob !com.example.word.utils.TTSManager  TAG !com.example.word.utils.TTSManager  TTS_API_URL !com.example.word.utils.TTSManager  TextToSpeech !com.example.word.utils.TTSManager  Unit !com.example.word.utils.TTSManager  apply !com.example.word.utils.TTSManager  cancel !com.example.word.utils.TTSManager  context !com.example.word.utils.TTSManager  coroutineScope !com.example.word.utils.TTSManager  fallbackToSystemTTS !com.example.word.utils.TTSManager  
httpClient !com.example.word.utils.TTSManager  initializeSystemTTS !com.example.word.utils.TTSManager  isBlank !com.example.word.utils.TTSManager  isSystemTTSReady !com.example.word.utils.TTSManager  java !com.example.word.utils.TTSManager  launch !com.example.word.utils.TTSManager  let !com.example.word.utils.TTSManager  mediaPlayer !com.example.word.utils.TTSManager  onTTSStatusChanged !com.example.word.utils.TTSManager  parseAndPlayAudio !com.example.word.utils.TTSManager  
playAudioData !com.example.word.utils.TTSManager  release !com.example.word.utils.TTSManager  speak !com.example.word.utils.TTSManager  speakWithOnlineTTS !com.example.word.utils.TTSManager  speakWithSystemTTS !com.example.word.utils.TTSManager  
startsWith !com.example.word.utils.TTSManager  	systemTTS !com.example.word.utils.TTSManager  toByteArray !com.example.word.utils.TTSManager  	toHttpUrl !com.example.word.utils.TTSManager  use !com.example.word.utils.TTSManager  withContext !com.example.word.utils.TTSManager  
writeBytes !com.example.word.utils.TTSManager  API_KEY +com.example.word.utils.TTSManager.Companion  AudioAttributes +com.example.word.utils.TTSManager.Companion  CoroutineScope +com.example.word.utils.TTSManager.Companion  Dispatchers +com.example.word.utils.TTSManager.Companion  Locale +com.example.word.utils.TTSManager.Companion  Log +com.example.word.utils.TTSManager.Companion  MediaPlayer +com.example.word.utils.TTSManager.Companion  OkHttpClient +com.example.word.utils.TTSManager.Companion  Request +com.example.word.utils.TTSManager.Companion  
SupervisorJob +com.example.word.utils.TTSManager.Companion  TAG +com.example.word.utils.TTSManager.Companion  TTS_API_URL +com.example.word.utils.TTSManager.Companion  TextToSpeech +com.example.word.utils.TTSManager.Companion  apply +com.example.word.utils.TTSManager.Companion  cancel +com.example.word.utils.TTSManager.Companion  fallbackToSystemTTS +com.example.word.utils.TTSManager.Companion  
httpClient +com.example.word.utils.TTSManager.Companion  isBlank +com.example.word.utils.TTSManager.Companion  java +com.example.word.utils.TTSManager.Companion  launch +com.example.word.utils.TTSManager.Companion  let +com.example.word.utils.TTSManager.Companion  parseAndPlayAudio +com.example.word.utils.TTSManager.Companion  
playAudioData +com.example.word.utils.TTSManager.Companion  
startsWith +com.example.word.utils.TTSManager.Companion  toByteArray +com.example.word.utils.TTSManager.Companion  	toHttpUrl +com.example.word.utils.TTSManager.Companion  use +com.example.word.utils.TTSManager.Companion  withContext +com.example.word.utils.TTSManager.Companion  
writeBytes +com.example.word.utils.TTSManager.Companion  OnInitListener #com.example.word.utils.TextToSpeech  BottomNavigationView ,com.google.android.material.bottomnavigation  MaterialCardView  com.google.android.material.card  setOnClickListener 1com.google.android.material.card.MaterialCardView  SwitchMaterial *com.google.android.material.switchmaterial  	isChecked 9com.google.android.material.switchmaterial.SwitchMaterial  setOnCheckedChangeListener 9com.google.android.material.switchmaterial.SwitchMaterial  TextInputEditText %com.google.android.material.textfield  text 7com.google.android.material.textfield.TextInputEditText  IOException java.io  InputStream java.io  absolutePath java.io.File  createTempFile java.io.File  delete java.io.File  printStackTrace java.io.IOException  	available java.io.InputStream  close java.io.InputStream  read java.io.InputStream  
Appendable 	java.lang  Class 	java.lang  	Exception 	java.lang  
StringBuilder 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  random java.lang.Math  
appendLine java.lang.StringBuilder  take java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  Charset java.nio.charset  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  API_KEY 	java.util  AchievementAdapter 	java.util  AlarmManager 	java.util  AndroidViewModel 	java.util  AppCompatDelegate 	java.util  Application 	java.util  AudioAttributes 	java.util  Boolean 	java.util  BroadcastReceiver 	java.util  Build 	java.util  Bundle 	java.util  	ByteArray 	java.util  Calendar 	java.util  
Comparator 	java.util  Context 	java.util  CoroutineScope 	java.util  DailyStudyRecord 	java.util  Date 	java.util  DifficultyStats 	java.util  Dispatchers 	java.util  	Exception 	java.util  Float 	java.util  Fragment 	java.util  FragmentProgressBinding 	java.util  FragmentSettingsBinding 	java.util  GameificationManager 	java.util  Int 	java.util  Intent 	java.util  LayoutInflater 	java.util  LinearLayoutManager 	java.util  List 	java.util  LiveData 	java.util  Locale 	java.util  Log 	java.util  Long 	java.util  MainActivity 	java.util  Map 	java.util  Math 	java.util  MediaPlayer 	java.util  MutableLiveData 	java.util  NOTIFICATION_ID 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  NotificationManagerCompat 	java.util  OkHttpClient 	java.util  OnlineTTSHelper 	java.util  
PendingIntent 	java.util  ProgressViewModel 	java.util  R 	java.util  Request 	java.util  SharedPreferences 	java.util  SimpleDateFormat 	java.util  String 	java.util  StudyDataPoint 	java.util  StudyNotificationManager 	java.util  StudyReminderReceiver 	java.util  StudySession 	java.util  StudyStatistics 	java.util  
SupervisorJob 	java.util  System 	java.util  TAG 	java.util  TTS_API_URL 	java.util  TextToSpeech 	java.util  TimePickerDialog 	java.util  Toast 	java.util  Unit 	java.util  UserProgress 	java.util  View 	java.util  	ViewGroup 	java.util  VocabularyMastery 	java.util  Word 	java.util  WordDatabase 	java.util  WordRepository 	java.util  
_achievements 	java.util  _currentQuizIndex 	java.util  _currentWord 	java.util  
_errorMessage 	java.util  
_isLoading 	java.util  
_quizScore 	java.util  
_quizWords 	java.util  _searchResults 	java.util  
_studyHistory 	java.util  
_studyMode 	java.util  _studyStatistics 	java.util  _vocabularyMastery 	java.util  _wordsForReview 	java.util  achievementAdapter 	java.util  android 	java.util  androidx 	java.util  apply 	java.util  arrayOf 	java.util  calculateNewAccuracy 	java.util  calculateNextLevelExp 	java.util  cancel 	java.util  cancelStudyReminder 	java.util  
coerceAtLeast 	java.util  coerceAtMost 	java.util  coerceIn 	java.util  com 	java.util  createConsecutiveAchievements 	java.util  createExperienceAchievements 	java.util  createNotificationChannel 	java.util  createStudyDaysAchievements 	java.util  createVocabularyAchievements 	java.util  downTo 	java.util  	emptyList 	java.util  fallbackToSystemTTS 	java.util  filter 	java.util  format 	java.util  formatStudyTime 	java.util  getDatabase 	java.util  getDayStartTime 	java.util  getTTSAudio 	java.util  getTodayStartTime 	java.util  getValue 	java.util  getWeekStartTime 	java.util  
httpClient 	java.util  isBlank 	java.util  
isNotEmpty 	java.util  java 	java.util  launch 	java.util  let 	java.util  maxOf 	java.util  
mutableListOf 	java.util  mutableMapOf 	java.util  parseAndPlayAudio 	java.util  
playAudioData 	java.util  provideDelegate 	java.util  random 	java.util  
repository 	java.util  requireContext 	java.util  reversed 	java.util  scheduleStudyReminder 	java.util  set 	java.util  sharedPreferences 	java.util  showStudyReminderNotification 	java.util  
simpleAPITest 	java.util  sortedByDescending 	java.util  
startsWith 	java.util  take 	java.util  toByteArray 	java.util  	toHttpUrl 	java.util  until 	java.util  updateDifficultyStats 	java.util  updateSpacedRepetition 	java.util  updateUserProgress 	java.util  use 	java.util  with 	java.util  withContext 	java.util  
writeBytes 	java.util  DAY_OF_MONTH java.util.Calendar  DAY_OF_WEEK java.util.Calendar  DAY_OF_YEAR java.util.Calendar  HOUR_OF_DAY java.util.Calendar  MILLISECOND java.util.Calendar  MINUTE java.util.Calendar  MONDAY java.util.Calendar  MONTH java.util.Calendar  SECOND java.util.Calendar  YEAR java.util.Calendar  add java.util.Calendar  get java.util.Calendar  getInstance java.util.Calendar  set java.util.Calendar  time java.util.Calendar  timeInMillis java.util.Calendar  Achievement java.util.GameificationManager  US java.util.Locale  
getDefault java.util.Locale  OnInitListener java.util.TextToSpeech  widget java.util.android  AdapterView java.util.android.widget  OnItemSelectedListener $java.util.android.widget.AdapterView  example 
java.util.com  word java.util.com.example  data java.util.com.example.word  entities java.util.com.example.word.data  UserProgress (java.util.com.example.word.data.entities  ExecutorService java.util.concurrent  TimeUnit java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  SECONDS java.util.concurrent.TimeUnit  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  IntArray kotlin  Lazy kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  arrayOf kotlin  getValue kotlin  
isInitialized kotlin  let kotlin  map kotlin  plus kotlin  run kotlin  synchronized kotlin  use kotlin  with kotlin  equals 
kotlin.Any  toString 
kotlin.Any  filter kotlin.Array  get kotlin.Array  not kotlin.Boolean  
isNotEmpty kotlin.ByteArray  size kotlin.ByteArray  	uppercase kotlin.Char  plus 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  Int kotlin.Enum  String kotlin.Enum  
coerceAtLeast kotlin.Float  coerceAtMost kotlin.Float  coerceIn kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  toString 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  rem kotlin.Long  toFloat kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  equals 
kotlin.String  first 
kotlin.String  format 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  length 
kotlin.String  let 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  take 
kotlin.String  toByteArray 
kotlin.String  	toHttpUrl 
kotlin.String  trim 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Set kotlin.collections  any kotlin.collections  contains kotlin.collections  distinct kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  first kotlin.collections  getValue kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  max kotlin.collections  maxOf kotlin.collections  min kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  random kotlin.collections  reversed kotlin.collections  set kotlin.collections  shuffled kotlin.collections  sortedByDescending kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  any kotlin.collections.List  distinct kotlin.collections.List  filter kotlin.collections.List  get kotlin.collections.List  indexOf kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  map kotlin.collections.List  maxOf kotlin.collections.List  minOf kotlin.collections.List  plus kotlin.collections.List  shuffled kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  reversed kotlin.collections.MutableList  sortedByDescending kotlin.collections.MutableList  set kotlin.collections.MutableMap  maxOf kotlin.comparisons  minOf kotlin.comparisons  reversed kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  
startsWith 	kotlin.io  use 	kotlin.io  
writeBytes 	kotlin.io  JvmOverloads 
kotlin.jvm  Volatile 
kotlin.jvm  java 
kotlin.jvm  kotlin 
kotlin.jvm  exp kotlin.math  max kotlin.math  min kotlin.math  pow kotlin.math  Random 
kotlin.random  Default kotlin.random.Random  nextBoolean kotlin.random.Random  nextInt kotlin.random.Random  nextBoolean kotlin.random.Random.Default  nextInt kotlin.random.Random.Default  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  downTo 
kotlin.ranges  first 
kotlin.ranges  random 
kotlin.ranges  reversed 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  random kotlin.ranges.IntRange  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  distinct kotlin.sequences  filter kotlin.sequences  first kotlin.sequences  map kotlin.sequences  max kotlin.sequences  maxOf kotlin.sequences  min kotlin.sequences  minOf kotlin.sequences  plus kotlin.sequences  shuffled kotlin.sequences  sortedByDescending kotlin.sequences  take kotlin.sequences  Charsets kotlin.text  String kotlin.text  any kotlin.text  
appendLine kotlin.text  buildString kotlin.text  contains kotlin.text  equals kotlin.text  filter kotlin.text  first kotlin.text  format kotlin.text  indices kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  map kotlin.text  max kotlin.text  maxOf kotlin.text  min kotlin.text  minOf kotlin.text  plus kotlin.text  random kotlin.text  replace kotlin.text  reversed kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  take kotlin.text  toByteArray kotlin.text  trim kotlin.text  	uppercase kotlin.text  UTF_8 kotlin.text.Charsets  API_KEY kotlinx.coroutines  AudioAttributes kotlinx.coroutines  Boolean kotlinx.coroutines  	ByteArray kotlinx.coroutines  CompletableJob kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  Int kotlinx.coroutines  Job kotlinx.coroutines  Locale kotlinx.coroutines  Log kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MediaPlayer kotlinx.coroutines  OkHttpClient kotlinx.coroutines  Request kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TAG kotlinx.coroutines  TTS_API_URL kotlinx.coroutines  TextToSpeech kotlinx.coroutines  Unit kotlinx.coroutines  
appendLine kotlinx.coroutines  apply kotlinx.coroutines  buildString kotlinx.coroutines  cancel kotlinx.coroutines  contains kotlinx.coroutines  fallbackToSystemTTS kotlinx.coroutines  
httpClient kotlinx.coroutines  isBlank kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  java kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  parseAndPlayAudio kotlinx.coroutines  
playAudioData kotlinx.coroutines  
startsWith kotlinx.coroutines  take kotlinx.coroutines  toByteArray kotlinx.coroutines  	toHttpUrl kotlinx.coroutines  use kotlinx.coroutines  withContext kotlinx.coroutines  
writeBytes kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  API_KEY !kotlinx.coroutines.CoroutineScope  Calendar !kotlinx.coroutines.CoroutineScope  Context !kotlinx.coroutines.CoroutineScope  DailyStudyRecord !kotlinx.coroutines.CoroutineScope  DatabaseInitializer !kotlinx.coroutines.CoroutineScope  DifficultyStats !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  GameificationManager !kotlinx.coroutines.CoroutineScope  KEY_INITIALIZED !kotlinx.coroutines.CoroutineScope  KEY_VERSION !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  
PREFS_NAME !kotlinx.coroutines.CoroutineScope  QuizProgress !kotlinx.coroutines.CoroutineScope  
QuizSource !kotlinx.coroutines.CoroutineScope  	QuizState !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  StudySession !kotlinx.coroutines.CoroutineScope  StudyStatistics !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  TTS_API_URL !kotlinx.coroutines.CoroutineScope  VocabularyJsonLoader !kotlinx.coroutines.CoroutineScope  VocabularyMastery !kotlinx.coroutines.CoroutineScope  WordDatabase !kotlinx.coroutines.CoroutineScope  
_achievements !kotlinx.coroutines.CoroutineScope  _currentQuizIndex !kotlinx.coroutines.CoroutineScope  _currentWord !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  _filteredPhrases !kotlinx.coroutines.CoroutineScope  _filteredTemplates !kotlinx.coroutines.CoroutineScope  
_isLoading !kotlinx.coroutines.CoroutineScope  _phraseCategories !kotlinx.coroutines.CoroutineScope  _phraseTypes !kotlinx.coroutines.CoroutineScope  
_quizProgress !kotlinx.coroutines.CoroutineScope  
_quizScore !kotlinx.coroutines.CoroutineScope  
_quizState !kotlinx.coroutines.CoroutineScope  
_quizWords !kotlinx.coroutines.CoroutineScope  _searchResults !kotlinx.coroutines.CoroutineScope  _selectedCategory !kotlinx.coroutines.CoroutineScope  _selectedDifficulty !kotlinx.coroutines.CoroutineScope  
_selectedType !kotlinx.coroutines.CoroutineScope  
_studyHistory !kotlinx.coroutines.CoroutineScope  
_studyMode !kotlinx.coroutines.CoroutineScope  _studyStatistics !kotlinx.coroutines.CoroutineScope  _templateCategories !kotlinx.coroutines.CoroutineScope  _templateTypes !kotlinx.coroutines.CoroutineScope  _vocabularyMastery !kotlinx.coroutines.CoroutineScope  _wordsForReview !kotlinx.coroutines.CoroutineScope  
allPhrases !kotlinx.coroutines.CoroutineScope  allTemplates !kotlinx.coroutines.CoroutineScope  
appendLine !kotlinx.coroutines.CoroutineScope  buildString !kotlinx.coroutines.CoroutineScope  calculateNewAccuracy !kotlinx.coroutines.CoroutineScope  calculateNextLevelExp !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  clearOldData !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  correctAnswers !kotlinx.coroutines.CoroutineScope  createConsecutiveAchievements !kotlinx.coroutines.CoroutineScope  createExperienceAchievements !kotlinx.coroutines.CoroutineScope  createStudyDaysAchievements !kotlinx.coroutines.CoroutineScope  createVocabularyAchievements !kotlinx.coroutines.CoroutineScope  currentQuestionIndex !kotlinx.coroutines.CoroutineScope  fallbackToSystemTTS !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  gamificationManager !kotlinx.coroutines.CoroutineScope  generateNextQuestion !kotlinx.coroutines.CoroutineScope  getDatabase !kotlinx.coroutines.CoroutineScope  getDayStartTime !kotlinx.coroutines.CoroutineScope  getTodayStartTime !kotlinx.coroutines.CoroutineScope  getVocabularyMetadata !kotlinx.coroutines.CoroutineScope  getWeekStartTime !kotlinx.coroutines.CoroutineScope  
httpClient !kotlinx.coroutines.CoroutineScope  initializeDatabase !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadVocabularyData !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  mutableMapOf !kotlinx.coroutines.CoroutineScope  parseAndPlayAudio !kotlinx.coroutines.CoroutineScope  
playAudioData !kotlinx.coroutines.CoroutineScope  	quizWords !kotlinx.coroutines.CoroutineScope  recordStudySession !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  reversed !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  sortedByDescending !kotlinx.coroutines.CoroutineScope  	startTime !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  	toHttpUrl !kotlinx.coroutines.CoroutineScope  until !kotlinx.coroutines.CoroutineScope  updateDailyGoal !kotlinx.coroutines.CoroutineScope  updateLevelInfo !kotlinx.coroutines.CoroutineScope  updateSpacedRepetition !kotlinx.coroutines.CoroutineScope  updateUserProgress !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  OnInitListener kotlinx.coroutines.TextToSpeech  API_KEY okhttp3  AudioAttributes okhttp3  Boolean okhttp3  	ByteArray okhttp3  Call okhttp3  Context okhttp3  CoroutineScope okhttp3  
Dispatcher okhttp3  Dispatchers okhttp3  	Exception okhttp3  HttpUrl okhttp3  Int okhttp3  Locale okhttp3  Log okhttp3  MediaPlayer okhttp3  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  ResponseBody okhttp3  String okhttp3  
SupervisorJob okhttp3  TAG okhttp3  TTS_API_URL okhttp3  TextToSpeech okhttp3  Unit okhttp3  
appendLine okhttp3  apply okhttp3  buildString okhttp3  cancel okhttp3  contains okhttp3  fallbackToSystemTTS okhttp3  
httpClient okhttp3  isBlank okhttp3  
isNotEmpty okhttp3  java okhttp3  launch okhttp3  let okhttp3  parseAndPlayAudio okhttp3  
playAudioData okhttp3  
startsWith okhttp3  take okhttp3  toByteArray okhttp3  	toHttpUrl okhttp3  use okhttp3  withContext okhttp3  
writeBytes okhttp3  execute okhttp3.Call  executorService okhttp3.Dispatcher  Builder okhttp3.HttpUrl  
newBuilder okhttp3.HttpUrl  addQueryParameter okhttp3.HttpUrl.Builder  build okhttp3.HttpUrl.Builder  	toHttpUrl okhttp3.HttpUrl.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  
dispatcher okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  get okhttp3.Request.Builder  url okhttp3.Request.Builder  body okhttp3.Response  code okhttp3.Response  header okhttp3.Response  isSuccessful okhttp3.Response  message okhttp3.Response  use okhttp3.Response  bytes okhttp3.ResponseBody  string okhttp3.ResponseBody  OnInitListener okhttp3.TextToSpeech  
JSONObject org.json  
getJSONObject org.json.JSONArray  length org.json.JSONArray  getInt org.json.JSONObject  getJSONArray org.json.JSONObject  
getJSONObject org.json.JSONObject  	getString org.json.JSONObject  	optString org.json.JSONObject  POST_NOTIFICATIONS android.Manifest.permission  IllegalStateException android.app.Activity  android android.app.Activity  finish android.app.Activity  
runOnUiThread android.app.Activity  canScheduleExactAlarms android.app.AlarmManager  	Exception android.app.Application  Log android.app.Application  StudyNotificationManager android.app.Application  System android.app.Application  TAG android.app.Application  Thread android.app.Application  
appendLine android.app.Application  buildString android.app.Application  createNotificationChannel android.app.Application  forEach android.app.Application  	javaClass android.app.Application  onCreate android.app.Application  	Exception !android.content.BroadcastReceiver  Log !android.content.BroadcastReceiver  TAG !android.content.BroadcastReceiver  IllegalStateException android.content.Context  Log android.content.Context  StudyNotificationManager android.content.Context  System android.content.Context  TAG android.content.Context  Thread android.content.Context  android android.content.Context  
appendLine android.content.Context  buildString android.content.Context  createNotificationChannel android.content.Context  forEach android.content.Context  	javaClass android.content.Context  
runOnUiThread android.content.Context  IllegalStateException android.content.ContextWrapper  Log android.content.ContextWrapper  StudyNotificationManager android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  Thread android.content.ContextWrapper  android android.content.ContextWrapper  
appendLine android.content.ContextWrapper  buildString android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  forEach android.content.ContextWrapper  	javaClass android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  	isPlaying android.media.MediaPlayer  
isReleased android.media.MediaPlayer  let android.media.MediaPlayer  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  IllegalStateException  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  LENGTH_LONG android.widget.Toast  IllegalStateException #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  
runOnUiThread #androidx.activity.ComponentActivity  IllegalStateException -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  
runOnUiThread -androidx.activity.ComponentActivity.Companion  IllegalStateException (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  
runOnUiThread (androidx.appcompat.app.AppCompatActivity  IllegalStateException #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  areNotificationsEnabled +androidx.core.app.NotificationManagerCompat  checkSelfPermission #androidx.core.content.ContextCompat  Log androidx.fragment.app.Fragment  TAG androidx.fragment.app.Fragment  isAdded androidx.fragment.app.Fragment  
isDetached androidx.fragment.app.Fragment  IllegalStateException &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  
runOnUiThread &androidx.fragment.app.FragmentActivity  Application com.example.word  IllegalStateException com.example.word  Log com.example.word  StudyNotificationManager com.example.word  System com.example.word  TAG com.example.word  Thread com.example.word  	Throwable com.example.word  WordApplication com.example.word  android com.example.word  
appendLine com.example.word  buildString com.example.word  createNotificationChannel com.example.word  forEach com.example.word  	javaClass com.example.word  
runOnUiThread com.example.word  IllegalStateException com.example.word.MainActivity  android com.example.word.MainActivity  finish com.example.word.MainActivity  
runOnUiThread com.example.word.MainActivity  	Exception  com.example.word.WordApplication  Log  com.example.word.WordApplication  StudyNotificationManager  com.example.word.WordApplication  System  com.example.word.WordApplication  TAG  com.example.word.WordApplication  Thread  com.example.word.WordApplication  	Throwable  com.example.word.WordApplication  
appendLine  com.example.word.WordApplication  buildString  com.example.word.WordApplication  createNotificationChannel  com.example.word.WordApplication  forEach  com.example.word.WordApplication  	javaClass  com.example.word.WordApplication  logCrashInfo  com.example.word.WordApplication  setupGlobalExceptionHandler  com.example.word.WordApplication  Log *com.example.word.WordApplication.Companion  StudyNotificationManager *com.example.word.WordApplication.Companion  System *com.example.word.WordApplication.Companion  TAG *com.example.word.WordApplication.Companion  Thread *com.example.word.WordApplication.Companion  
appendLine *com.example.word.WordApplication.Companion  buildString *com.example.word.WordApplication.Companion  createNotificationChannel *com.example.word.WordApplication.Companion  forEach *com.example.word.WordApplication.Companion  	javaClass *com.example.word.WordApplication.Companion  Log com.example.word.data.database  TAG com.example.word.data.database  Log 2com.example.word.data.database.DatabaseInitializer  TAG 2com.example.word.data.database.DatabaseInitializer  Log com.example.word.ui.home  TAG com.example.word.ui.home  Bundle %com.example.word.ui.home.HomeFragment  	Exception %com.example.word.ui.home.HomeFragment  Int %com.example.word.ui.home.HomeFragment  LayoutInflater %com.example.word.ui.home.HomeFragment  Log %com.example.word.ui.home.HomeFragment  String %com.example.word.ui.home.HomeFragment  TAG %com.example.word.ui.home.HomeFragment  View %com.example.word.ui.home.HomeFragment  	ViewGroup %com.example.word.ui.home.HomeFragment  
WordViewModel %com.example.word.ui.home.HomeFragment  isAdded %com.example.word.ui.home.HomeFragment  
isDetached %com.example.word.ui.home.HomeFragment  safeNavigate %com.example.word.ui.home.HomeFragment  FragmentHomeBinding /com.example.word.ui.home.HomeFragment.Companion  GameificationManager /com.example.word.ui.home.HomeFragment.Companion  Log /com.example.word.ui.home.HomeFragment.Companion  R /com.example.word.ui.home.HomeFragment.Companion  TAG /com.example.word.ui.home.HomeFragment.Companion  apply /com.example.word.ui.home.HomeFragment.Companion  arrayOf /com.example.word.ui.home.HomeFragment.Companion  findNavController /com.example.word.ui.home.HomeFragment.Companion  gamificationManager /com.example.word.ui.home.HomeFragment.Companion  getValue /com.example.word.ui.home.HomeFragment.Companion  java /com.example.word.ui.home.HomeFragment.Companion  launch /com.example.word.ui.home.HomeFragment.Companion  lifecycleScope /com.example.word.ui.home.HomeFragment.Companion  provideDelegate /com.example.word.ui.home.HomeFragment.Companion  updateDailyGoal /com.example.word.ui.home.HomeFragment.Companion  updateLevelInfo /com.example.word.ui.home.HomeFragment.Companion  
viewModels /com.example.word.ui.home.HomeFragment.Companion  	UserLevel :com.example.word.ui.home.HomeFragment.GameificationManager  PackageManager com.example.word.utils  
isReleased com.example.word.utils  
ContextCompat /com.example.word.utils.StudyNotificationManager  Log /com.example.word.utils.StudyNotificationManager  PackageManager /com.example.word.utils.StudyNotificationManager  TAG /com.example.word.utils.StudyNotificationManager  android /com.example.word.utils.StudyNotificationManager  	Companion ,com.example.word.utils.StudyReminderReceiver  Context ,com.example.word.utils.StudyReminderReceiver  	Exception ,com.example.word.utils.StudyReminderReceiver  Intent ,com.example.word.utils.StudyReminderReceiver  Log ,com.example.word.utils.StudyReminderReceiver  TAG ,com.example.word.utils.StudyReminderReceiver  Log 6com.example.word.utils.StudyReminderReceiver.Companion  StudyNotificationManager 6com.example.word.utils.StudyReminderReceiver.Companion  TAG 6com.example.word.utils.StudyReminderReceiver.Companion  showStudyReminderNotification 6com.example.word.utils.StudyReminderReceiver.Companion  
isReleased !com.example.word.utils.TTSManager  releaseMediaPlayer !com.example.word.utils.TTSManager  
isReleased +com.example.word.utils.TTSManager.Companion  setupWithNavController Acom.google.android.material.bottomnavigation.BottomNavigationView  exists java.io.File  IllegalStateException 	java.lang  Runnable 	java.lang  
simpleName java.lang.Class  <SAM-CONSTRUCTOR> java.lang.Runnable  System java.lang.StringBuilder  forEach java.lang.StringBuilder  	javaClass java.lang.StringBuilder  UncaughtExceptionHandler java.lang.Thread  "getDefaultUncaughtExceptionHandler java.lang.Thread  name java.lang.Thread  "setDefaultUncaughtExceptionHandler java.lang.Thread  <SAM-CONSTRUCTOR> )java.lang.Thread.UncaughtExceptionHandler  uncaughtException )java.lang.Thread.UncaughtExceptionHandler  
ContextCompat 	java.util  PackageManager 	java.util  
isReleased 	java.util  	Throwable kotlin  cause kotlin.Throwable  	javaClass kotlin.Throwable  
stackTrace kotlin.Throwable  forEach kotlin.collections  	javaClass 
kotlin.jvm  forEach kotlin.sequences  forEach kotlin.text  
isReleased kotlinx.coroutines  android !kotlinx.coroutines.CoroutineScope  
runOnUiThread !kotlinx.coroutines.CoroutineScope  ConnectionPool okhttp3  
isReleased okhttp3  evictAll okhttp3.ConnectionPool  connectionPool okhttp3.OkHttpClient  ErrorHandler android.app.Application  
getCrashStats android.app.Application  
initialize android.app.Application  logError android.app.Application  ErrorHandler android.content.Context  filesDir android.content.Context  
getCrashStats android.content.Context  
initialize android.content.Context  logError android.content.Context  ErrorHandler android.content.ContextWrapper  
getCrashStats android.content.ContextWrapper  
initialize android.content.ContextWrapper  logError android.content.ContextWrapper  getLong !android.content.SharedPreferences  putLong (android.content.SharedPreferences.Editor  context androidx.fragment.app.Fragment  
isRemoving androidx.fragment.app.Fragment  safeContext androidx.fragment.app.Fragment  safeExecute androidx.fragment.app.Fragment  safeUIOperation androidx.fragment.app.Fragment  view androidx.fragment.app.Fragment  	Exception androidx.room.RoomDatabase  Log androidx.room.RoomDatabase  TAG androidx.room.RoomDatabase  close androidx.room.RoomDatabase  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  onOpen #androidx.room.RoomDatabase.Callback  Log $androidx.room.RoomDatabase.Companion  TAG $androidx.room.RoomDatabase.Companion  version (androidx.sqlite.db.SupportSQLiteDatabase  ErrorHandler com.example.word  
getCrashStats com.example.word  
initialize com.example.word  logError com.example.word  ErrorHandler  com.example.word.WordApplication  
getCrashStats  com.example.word.WordApplication  
initialize  com.example.word.WordApplication  logError  com.example.word.WordApplication  ErrorHandler *com.example.word.WordApplication.Companion  
getCrashStats *com.example.word.WordApplication.Companion  
initialize *com.example.word.WordApplication.Companion  logError *com.example.word.WordApplication.Companion  
JSONException com.example.word.data.database  ifEmpty com.example.word.data.database  isEmpty com.example.word.data.database  use com.example.word.data.database  Log 3com.example.word.data.database.VocabularyJsonLoader  TAG 3com.example.word.data.database.VocabularyJsonLoader  ifEmpty 3com.example.word.data.database.VocabularyJsonLoader  isEmpty 3com.example.word.data.database.VocabularyJsonLoader  
isNotEmpty 3com.example.word.data.database.VocabularyJsonLoader  use 3com.example.word.data.database.VocabularyJsonLoader  	Exception +com.example.word.data.database.WordDatabase  Log +com.example.word.data.database.WordDatabase  TAG +com.example.word.data.database.WordDatabase  close +com.example.word.data.database.WordDatabase  Log 5com.example.word.data.database.WordDatabase.Companion  TAG 5com.example.word.data.database.WordDatabase.Companion  Log @com.example.word.data.database.WordDatabase.WordDatabaseCallback  TAG @com.example.word.data.database.WordDatabase.WordDatabaseCallback  	Exception com.example.word.ui.vocabulary  Log com.example.word.ui.vocabulary  TAG com.example.word.ui.vocabulary  run com.example.word.ui.vocabulary  Boolean 1com.example.word.ui.vocabulary.VocabularyFragment  Bundle 1com.example.word.ui.vocabulary.VocabularyFragment  	Exception 1com.example.word.ui.vocabulary.VocabularyFragment  LayoutInflater 1com.example.word.ui.vocabulary.VocabularyFragment  Log 1com.example.word.ui.vocabulary.VocabularyFragment  String 1com.example.word.ui.vocabulary.VocabularyFragment  TAG 1com.example.word.ui.vocabulary.VocabularyFragment  	ViewGroup 1com.example.word.ui.vocabulary.VocabularyFragment  
WordViewModel 1com.example.word.ui.vocabulary.VocabularyFragment  androidx 1com.example.word.ui.vocabulary.VocabularyFragment  context 1com.example.word.ui.vocabulary.VocabularyFragment  run 1com.example.word.ui.vocabulary.VocabularyFragment  safeUIOperation 1com.example.word.ui.vocabulary.VocabularyFragment  FragmentVocabularyBinding ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  LinearLayoutManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  Log ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  TAG ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
TTSManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  Toast ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  View ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  WordAdapter ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  android ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  apply ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getValue ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
isNullOrBlank ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  let ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  provideDelegate ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  run ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  safeUIOperation ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  viewLifecycleOwner ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
viewModels ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  wordAdapter ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
wordViewModel ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  	appcompat :com.example.word.ui.vocabulary.VocabularyFragment.androidx  widget Dcom.example.word.ui.vocabulary.VocabularyFragment.androidx.appcompat  
SearchView Kcom.example.word.ui.vocabulary.VocabularyFragment.androidx.appcompat.widget  OnQueryTextListener Vcom.example.word.ui.vocabulary.VocabularyFragment.androidx.appcompat.widget.SearchView  Any com.example.word.utils  CRASH_LOG_FILE com.example.word.utils  CrashPrevention com.example.word.utils  
CrashStats com.example.word.utils  Date com.example.word.utils  ERROR_LOG_FILE com.example.word.utils  ErrorHandler com.example.word.utils  File com.example.word.utils  
FileWriter com.example.word.utils  Fragment com.example.word.utils  
NavController com.example.word.utils  SimpleDateFormat com.example.word.utils  T com.example.word.utils  Thread com.example.word.utils  	Throwable com.example.word.utils  buildCrashReport com.example.word.utils  buildErrorReport com.example.word.utils  forEach com.example.word.utils  getCurrentTimestamp com.example.word.utils  	javaClass com.example.word.utils  safeExecute com.example.word.utils  writeToFile com.example.word.utils  Log &com.example.word.utils.CrashPrevention  TAG &com.example.word.utils.CrashPrevention  findNavController &com.example.word.utils.CrashPrevention  safeContext &com.example.word.utils.CrashPrevention  safeExecute &com.example.word.utils.CrashPrevention  safeUIOperation &com.example.word.utils.CrashPrevention  withSafeContext &com.example.word.utils.CrashPrevention  CRASH_LOG_FILE #com.example.word.utils.ErrorHandler  Context #com.example.word.utils.ErrorHandler  CoroutineScope #com.example.word.utils.ErrorHandler  
CrashStats #com.example.word.utils.ErrorHandler  Date #com.example.word.utils.ErrorHandler  Dispatchers #com.example.word.utils.ErrorHandler  ERROR_LOG_FILE #com.example.word.utils.ErrorHandler  	Exception #com.example.word.utils.ErrorHandler  File #com.example.word.utils.ErrorHandler  
FileWriter #com.example.word.utils.ErrorHandler  Int #com.example.word.utils.ErrorHandler  KEY_CRASH_COUNT #com.example.word.utils.ErrorHandler  KEY_LAST_CRASH_TIME #com.example.word.utils.ErrorHandler  Locale #com.example.word.utils.ErrorHandler  Log #com.example.word.utils.ErrorHandler  Long #com.example.word.utils.ErrorHandler  MAX_LOG_SIZE #com.example.word.utils.ErrorHandler  
PREFS_NAME #com.example.word.utils.ErrorHandler  SimpleDateFormat #com.example.word.utils.ErrorHandler  String #com.example.word.utils.ErrorHandler  System #com.example.word.utils.ErrorHandler  TAG #com.example.word.utils.ErrorHandler  Thread #com.example.word.utils.ErrorHandler  	Throwable #com.example.word.utils.ErrorHandler  
appendLine #com.example.word.utils.ErrorHandler  buildCrashReport #com.example.word.utils.ErrorHandler  buildErrorReport #com.example.word.utils.ErrorHandler  buildString #com.example.word.utils.ErrorHandler  cleanupOldLogs #com.example.word.utils.ErrorHandler  forEach #com.example.word.utils.ErrorHandler  
getCrashStats #com.example.word.utils.ErrorHandler  getCurrentTimestamp #com.example.word.utils.ErrorHandler  handleCrash #com.example.word.utils.ErrorHandler  
initialize #com.example.word.utils.ErrorHandler  	javaClass #com.example.word.utils.ErrorHandler  launch #com.example.word.utils.ErrorHandler  let #com.example.word.utils.ErrorHandler  logCrash #com.example.word.utils.ErrorHandler  logError #com.example.word.utils.ErrorHandler  take #com.example.word.utils.ErrorHandler  updateCrashStats #com.example.word.utils.ErrorHandler  use #com.example.word.utils.ErrorHandler  writeToFile #com.example.word.utils.ErrorHandler  
crashCount .com.example.word.utils.ErrorHandler.CrashStats  
lastCrashTime .com.example.word.utils.ErrorHandler.CrashStats  File java.io  
FileWriter java.io  lastModified java.io.File  length java.io.File  append java.io.FileWriter  flush java.io.FileWriter  use java.io.FileWriter  use java.io.InputStream  append java.io.OutputStreamWriter  flush java.io.OutputStreamWriter  append java.io.Writer  Thread 	java.lang  getCurrentTimestamp java.lang.StringBuilder  let java.lang.StringBuilder  CRASH_LOG_FILE 	java.util  
CrashStats 	java.util  ERROR_LOG_FILE 	java.util  File 	java.util  
FileWriter 	java.util  Thread 	java.util  	Throwable 	java.util  
appendLine 	java.util  buildCrashReport 	java.util  buildErrorReport 	java.util  buildString 	java.util  forEach 	java.util  getCurrentTimestamp 	java.util  	javaClass 	java.util  writeToFile 	java.util  safeExecute 
kotlin.Any  isEmpty kotlin.CharSequence  invoke kotlin.Function0  Log kotlin.Nothing  TAG kotlin.Nothing  isEmpty 
kotlin.String  let kotlin.Throwable  ifEmpty kotlin.collections  isEmpty kotlin.collections  ifEmpty kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction0 kotlin.coroutines  invoke "kotlin.coroutines.SuspendFunction0  ifEmpty kotlin.sequences  ifEmpty kotlin.text  isEmpty kotlin.text  CRASH_LOG_FILE !kotlinx.coroutines.CoroutineScope  ERROR_LOG_FILE !kotlinx.coroutines.CoroutineScope  buildCrashReport !kotlinx.coroutines.CoroutineScope  buildErrorReport !kotlinx.coroutines.CoroutineScope  writeToFile !kotlinx.coroutines.CoroutineScope  
JSONException org.json  has org.json.JSONObject  optInt org.json.JSONObject  MODE_PRIVATE android.app.Activity  androidx android.app.Activity  com android.app.Activity  getSharedPreferences android.app.Activity  intent android.app.Activity  kotlinx android.app.Activity  resetAppData android.app.Activity  
resetDatabase android.app.Activity  
startActivity android.app.Activity  androidx android.content.Context  com android.content.Context  finish android.content.Context  getDatabasePath android.content.Context  intent android.content.Context  kotlinx android.content.Context  resetAppData android.content.Context  
resetDatabase android.content.Context  
startActivity android.content.Context  MODE_PRIVATE android.content.ContextWrapper  androidx android.content.ContextWrapper  com android.content.ContextWrapper  finish android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  intent android.content.ContextWrapper  kotlinx android.content.ContextWrapper  resetAppData android.content.ContextWrapper  
resetDatabase android.content.ContextWrapper  
startActivity android.content.ContextWrapper  clear (android.content.SharedPreferences.Editor  MODE_PRIVATE  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  com  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getSharedPreferences  android.view.ContextThemeWrapper  intent  android.view.ContextThemeWrapper  kotlinx  android.view.ContextThemeWrapper  resetAppData  android.view.ContextThemeWrapper  
resetDatabase  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  MODE_PRIVATE #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  com #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getSharedPreferences #androidx.activity.ComponentActivity  intent #androidx.activity.ComponentActivity  kotlinx #androidx.activity.ComponentActivity  resetAppData #androidx.activity.ComponentActivity  
resetDatabase #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  MODE_PRIVATE -androidx.activity.ComponentActivity.Companion  androidx -androidx.activity.ComponentActivity.Companion  com -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  getSharedPreferences -androidx.activity.ComponentActivity.Companion  intent -androidx.activity.ComponentActivity.Companion  kotlinx -androidx.activity.ComponentActivity.Companion  resetAppData -androidx.activity.ComponentActivity.Companion  
resetDatabase -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  MODE_PRIVATE (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  com (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  getSharedPreferences (androidx.appcompat.app.AppCompatActivity  intent (androidx.appcompat.app.AppCompatActivity  kotlinx (androidx.appcompat.app.AppCompatActivity  resetAppData (androidx.appcompat.app.AppCompatActivity  
resetDatabase (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  MODE_PRIVATE #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  com #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getSharedPreferences #androidx.core.app.ComponentActivity  intent #androidx.core.app.ComponentActivity  kotlinx #androidx.core.app.ComponentActivity  resetAppData #androidx.core.app.ComponentActivity  
resetDatabase #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  MODE_PRIVATE &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  com &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  getSharedPreferences &androidx.fragment.app.FragmentActivity  intent &androidx.fragment.app.FragmentActivity  kotlinx &androidx.fragment.app.FragmentActivity  resetAppData &androidx.fragment.app.FragmentActivity  
resetDatabase &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  MODE_PRIVATE com.example.word  androidx com.example.word  com com.example.word  finish com.example.word  getSharedPreferences com.example.word  intent com.example.word  kotlinx com.example.word  lifecycleScope com.example.word  resetAppData com.example.word  
resetDatabase com.example.word  
startActivity com.example.word  MODE_PRIVATE com.example.word.MainActivity  androidx com.example.word.MainActivity  com com.example.word.MainActivity  getSharedPreferences com.example.word.MainActivity  intent com.example.word.MainActivity  kotlinx com.example.word.MainActivity  resetAppData com.example.word.MainActivity  
resetDatabase com.example.word.MainActivity  
startActivity com.example.word.MainActivity  
resetDatabase com.example.word.data.database  
resetDatabase 2com.example.word.data.database.DatabaseInitializer  
resetDatabase +com.example.word.data.database.WordDatabase  
resetDatabase 5com.example.word.data.database.WordDatabase.Companion  delay kotlinx.coroutines  MODE_PRIVATE !kotlinx.coroutines.CoroutineScope  androidx !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  getSharedPreferences !kotlinx.coroutines.CoroutineScope  intent !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  lifecycleScope !kotlinx.coroutines.CoroutineScope  resetAppData !kotlinx.coroutines.CoroutineScope  
resetDatabase !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  packageManager android.content.Context  queryIntentActivities !android.content.pm.PackageManager  ERROR android.speech.tts.TextToSpeech  
defaultEngine android.speech.tts.TextToSpeech  engines android.speech.tts.TextToSpeech  setPitch android.speech.tts.TextToSpeech  label *android.speech.tts.TextToSpeech.EngineInfo  name *android.speech.tts.TextToSpeech.EngineInfo  <SAM-CONSTRUCTOR> .android.speech.tts.TextToSpeech.OnInitListener  TTSDebugHelper androidx.fragment.app.Fragment  checkSystemTTSSettings androidx.fragment.app.Fragment  checkTTSAvailability androidx.fragment.app.Fragment  getTTSEngineInfo androidx.fragment.app.Fragment  TextDataParser com.example.word.data.database  parseEssayTemplatesFromText com.example.word.data.database  parsePhrasesFromText com.example.word.data.database  parseWordsFromText com.example.word.data.database  TextDataParser 2com.example.word.data.database.DatabaseInitializer  parseEssayTemplatesFromText 2com.example.word.data.database.DatabaseInitializer  parsePhrasesFromText 2com.example.word.data.database.DatabaseInitializer  parseWordsFromText 2com.example.word.data.database.DatabaseInitializer  copy ,com.example.word.data.entities.EssayTemplate  let ,com.example.word.data.entities.EssayTemplate  copy %com.example.word.data.entities.Phrase  let %com.example.word.data.entities.Phrase  copy #com.example.word.data.entities.Word  let #com.example.word.data.entities.Word  BufferedReader com.example.word.data.parser  Context com.example.word.data.parser  
EssayTemplate com.example.word.data.parser  	Exception com.example.word.data.parser  InputStreamReader com.example.word.data.parser  Int com.example.word.data.parser  List com.example.word.data.parser  Log com.example.word.data.parser  Phrase com.example.word.data.parser  Regex com.example.word.data.parser  String com.example.word.data.parser  TextDataParser com.example.word.data.parser  Word com.example.word.data.parser  also com.example.word.data.parser  contains com.example.word.data.parser  indexOfFirst com.example.word.data.parser  isEmpty com.example.word.data.parser  
isNotEmpty com.example.word.data.parser  let com.example.word.data.parser  matches com.example.word.data.parser  
mutableListOf com.example.word.data.parser  
startsWith com.example.word.data.parser  	substring com.example.word.data.parser  toInt com.example.word.data.parser  toRegex com.example.word.data.parser  trim com.example.word.data.parser  BufferedReader +com.example.word.data.parser.TextDataParser  
EssayTemplate +com.example.word.data.parser.TextDataParser  InputStreamReader +com.example.word.data.parser.TextDataParser  Log +com.example.word.data.parser.TextDataParser  Phrase +com.example.word.data.parser.TextDataParser  Regex +com.example.word.data.parser.TextDataParser  TAG +com.example.word.data.parser.TextDataParser  Word +com.example.word.data.parser.TextDataParser  also +com.example.word.data.parser.TextDataParser   calculateDifficultyFromFrequency +com.example.word.data.parser.TextDataParser  contains +com.example.word.data.parser.TextDataParser  indexOfFirst +com.example.word.data.parser.TextDataParser  isEmpty +com.example.word.data.parser.TextDataParser  
isNotEmpty +com.example.word.data.parser.TextDataParser  let +com.example.word.data.parser.TextDataParser  matches +com.example.word.data.parser.TextDataParser  
mutableListOf +com.example.word.data.parser.TextDataParser  
parseCET4Word +com.example.word.data.parser.TextDataParser  parseEssayTemplateLine +com.example.word.data.parser.TextDataParser  parseEssayTemplatesFromText +com.example.word.data.parser.TextDataParser  parseHighFreqWord +com.example.word.data.parser.TextDataParser  parsePhraseLine +com.example.word.data.parser.TextDataParser  parsePhrasesFromText +com.example.word.data.parser.TextDataParser  parseWordsFromText +com.example.word.data.parser.TextDataParser  
startsWith +com.example.word.data.parser.TextDataParser  	substring +com.example.word.data.parser.TextDataParser  toInt +com.example.word.data.parser.TextDataParser  toRegex +com.example.word.data.parser.TextDataParser  trim +com.example.word.data.parser.TextDataParser  TTSDebugHelper com.example.word.ui.vocabulary  checkSystemTTSSettings com.example.word.ui.vocabulary  checkTTSAvailability com.example.word.ui.vocabulary  forEach com.example.word.ui.vocabulary  getTTSEngineInfo com.example.word.ui.vocabulary  TTSDebugHelper 1com.example.word.ui.vocabulary.VocabularyFragment  checkSystemTTSSettings 1com.example.word.ui.vocabulary.VocabularyFragment  checkTTSAvailability 1com.example.word.ui.vocabulary.VocabularyFragment  getTTSEngineInfo 1com.example.word.ui.vocabulary.VocabularyFragment  TTSDebugHelper ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  checkSystemTTSSettings ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  checkTTSAvailability ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getTTSEngineInfo ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  TTSDebugHelper com.example.word.utils  run com.example.word.utils  Locale %com.example.word.utils.TTSDebugHelper  Log %com.example.word.utils.TTSDebugHelper  TAG %com.example.word.utils.TTSDebugHelper  TextToSpeech %com.example.word.utils.TTSDebugHelper  android %com.example.word.utils.TTSDebugHelper  checkSystemTTSSettings %com.example.word.utils.TTSDebugHelper  checkTTSAvailability %com.example.word.utils.TTSDebugHelper  getTTSEngineInfo %com.example.word.utils.TTSDebugHelper  
isNotEmpty %com.example.word.utils.TTSDebugHelper  let %com.example.word.utils.TTSDebugHelper  
mutableListOf %com.example.word.utils.TTSDebugHelper  run %com.example.word.utils.TTSDebugHelper  getTTSStatus !com.example.word.utils.TTSManager  isTTSAvailable !com.example.word.utils.TTSManager  run !com.example.word.utils.TTSManager  run +com.example.word.utils.TTSManager.Companion  BufferedReader java.io  InputStreamReader java.io  close java.io.BufferedReader  readLine java.io.BufferedReader  run 	java.util  country java.util.Locale  language java.util.Locale  also kotlin  toString kotlin.Char  toLong 
kotlin.Int  indexOfFirst 
kotlin.String  matches 
kotlin.String  	substring 
kotlin.String  toInt 
kotlin.String  toRegex 
kotlin.String  indexOfFirst kotlin.collections  indexOfFirst kotlin.sequences  MatchResult kotlin.text  Regex kotlin.text  indexOfFirst kotlin.text  matches kotlin.text  	substring kotlin.text  toInt kotlin.text  toRegex kotlin.text  Destructured kotlin.text.MatchResult  destructured kotlin.text.MatchResult  let kotlin.text.MatchResult  
component1 $kotlin.text.MatchResult.Destructured  
component2 $kotlin.text.MatchResult.Destructured  
component3 $kotlin.text.MatchResult.Destructured  
component4 $kotlin.text.MatchResult.Destructured  
component5 $kotlin.text.MatchResult.Destructured  
component6 $kotlin.text.MatchResult.Destructured  find kotlin.text.Regex  run kotlinx.coroutines  run okhttp3  UtteranceProgressListener android.speech.tts  
isSpeaking android.speech.tts.TextToSpeech  setOnUtteranceProgressListener android.speech.tts.TextToSpeech  Log ,android.speech.tts.UtteranceProgressListener  TAG ,android.speech.tts.UtteranceProgressListener  DatabaseDebugHelper androidx.fragment.app.Fragment  SimpleTTSManager androidx.fragment.app.Fragment  
check1TxtFile androidx.fragment.app.Fragment  checkDatabaseStatus androidx.fragment.app.Fragment  forceReinitializeDatabase androidx.fragment.app.Fragment  testTextParsing androidx.fragment.app.Fragment  getTemplateCount *com.example.word.data.dao.EssayTemplateDao  getPhraseCount #com.example.word.data.dao.PhraseDao  getWordCount !com.example.word.data.dao.WordDao  getCurrentDataVersion 2com.example.word.data.database.DatabaseInitializer  isDatabaseInitialized 2com.example.word.data.database.DatabaseInitializer  DatabaseDebugHelper com.example.word.ui.vocabulary  SimpleTTSManager com.example.word.ui.vocabulary  
check1TxtFile com.example.word.ui.vocabulary  checkDatabaseStatus com.example.word.ui.vocabulary  context com.example.word.ui.vocabulary  forceReinitializeDatabase com.example.word.ui.vocabulary  launch com.example.word.ui.vocabulary  safeUIOperation com.example.word.ui.vocabulary  testTextParsing com.example.word.ui.vocabulary  DatabaseDebugHelper 1com.example.word.ui.vocabulary.VocabularyFragment  SimpleTTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  
check1TxtFile 1com.example.word.ui.vocabulary.VocabularyFragment  checkDatabaseStatus 1com.example.word.ui.vocabulary.VocabularyFragment  debugDatabaseStatus 1com.example.word.ui.vocabulary.VocabularyFragment  forceReinitializeDatabase 1com.example.word.ui.vocabulary.VocabularyFragment  launch 1com.example.word.ui.vocabulary.VocabularyFragment  lifecycleScope 1com.example.word.ui.vocabulary.VocabularyFragment  simpleTTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  testTextParsing 1com.example.word.ui.vocabulary.VocabularyFragment  DatabaseDebugHelper ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  SimpleTTSManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
check1TxtFile ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  checkDatabaseStatus ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  context ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  forceReinitializeDatabase ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  launch ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  lifecycleScope ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  testTextParsing ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  DatabaseDebugHelper com.example.word.utils  DatabaseInitializer com.example.word.utils  DatabaseStatus com.example.word.utils  SimpleTTSManager com.example.word.utils  TextDataParser com.example.word.utils  TextParsingResult com.example.word.utils  UtteranceProgressListener com.example.word.utils  WordDatabase com.example.word.utils  getCurrentDataVersion com.example.word.utils  getDatabase com.example.word.utils  initializeDatabase com.example.word.utils  isDatabaseInitialized com.example.word.utils  parseEssayTemplatesFromText com.example.word.utils  parsePhrasesFromText com.example.word.utils  parseWordsFromText com.example.word.utils  
resetDatabase com.example.word.utils  Boolean *com.example.word.utils.DatabaseDebugHelper  Context *com.example.word.utils.DatabaseDebugHelper  DatabaseInitializer *com.example.word.utils.DatabaseDebugHelper  DatabaseStatus *com.example.word.utils.DatabaseDebugHelper  Dispatchers *com.example.word.utils.DatabaseDebugHelper  	Exception *com.example.word.utils.DatabaseDebugHelper  Int *com.example.word.utils.DatabaseDebugHelper  Log *com.example.word.utils.DatabaseDebugHelper  String *com.example.word.utils.DatabaseDebugHelper  TAG *com.example.word.utils.DatabaseDebugHelper  TextDataParser *com.example.word.utils.DatabaseDebugHelper  TextParsingResult *com.example.word.utils.DatabaseDebugHelper  WordDatabase *com.example.word.utils.DatabaseDebugHelper  
check1TxtFile *com.example.word.utils.DatabaseDebugHelper  checkDatabaseStatus *com.example.word.utils.DatabaseDebugHelper  forceReinitializeDatabase *com.example.word.utils.DatabaseDebugHelper  getCurrentDataVersion *com.example.word.utils.DatabaseDebugHelper  getDatabase *com.example.word.utils.DatabaseDebugHelper  initializeDatabase *com.example.word.utils.DatabaseDebugHelper  isDatabaseInitialized *com.example.word.utils.DatabaseDebugHelper  
isNotEmpty *com.example.word.utils.DatabaseDebugHelper  parseEssayTemplatesFromText *com.example.word.utils.DatabaseDebugHelper  parsePhrasesFromText *com.example.word.utils.DatabaseDebugHelper  parseWordsFromText *com.example.word.utils.DatabaseDebugHelper  
resetDatabase *com.example.word.utils.DatabaseDebugHelper  testTextParsing *com.example.word.utils.DatabaseDebugHelper  withContext *com.example.word.utils.DatabaseDebugHelper  phraseCount 9com.example.word.utils.DatabaseDebugHelper.DatabaseStatus  	wordCount 9com.example.word.utils.DatabaseDebugHelper.DatabaseStatus  Boolean 'com.example.word.utils.SimpleTTSManager  Context 'com.example.word.utils.SimpleTTSManager  	Exception 'com.example.word.utils.SimpleTTSManager  Locale 'com.example.word.utils.SimpleTTSManager  Log 'com.example.word.utils.SimpleTTSManager  String 'com.example.word.utils.SimpleTTSManager  TAG 'com.example.word.utils.SimpleTTSManager  TextToSpeech 'com.example.word.utils.SimpleTTSManager  Unit 'com.example.word.utils.SimpleTTSManager  UtteranceProgressListener 'com.example.word.utils.SimpleTTSManager  context 'com.example.word.utils.SimpleTTSManager  	getStatus 'com.example.word.utils.SimpleTTSManager  
initialize 'com.example.word.utils.SimpleTTSManager  
isInitialized 'com.example.word.utils.SimpleTTSManager  isInitializing 'com.example.word.utils.SimpleTTSManager  isReady 'com.example.word.utils.SimpleTTSManager  
isSpeaking 'com.example.word.utils.SimpleTTSManager  let 'com.example.word.utils.SimpleTTSManager  release 'com.example.word.utils.SimpleTTSManager  run 'com.example.word.utils.SimpleTTSManager  speak 'com.example.word.utils.SimpleTTSManager  textToSpeech 'com.example.word.utils.SimpleTTSManager  Locale 1com.example.word.utils.SimpleTTSManager.Companion  Log 1com.example.word.utils.SimpleTTSManager.Companion  TAG 1com.example.word.utils.SimpleTTSManager.Companion  TextToSpeech 1com.example.word.utils.SimpleTTSManager.Companion  let 1com.example.word.utils.SimpleTTSManager.Companion  run 1com.example.word.utils.SimpleTTSManager.Companion  UtteranceProgressListener 	java.util  DatabaseDebugHelper !kotlinx.coroutines.CoroutineScope  DatabaseStatus !kotlinx.coroutines.CoroutineScope  TextDataParser !kotlinx.coroutines.CoroutineScope  TextParsingResult !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  
check1TxtFile !kotlinx.coroutines.CoroutineScope  checkDatabaseStatus !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  forceReinitializeDatabase !kotlinx.coroutines.CoroutineScope  getCurrentDataVersion !kotlinx.coroutines.CoroutineScope  isDatabaseInitialized !kotlinx.coroutines.CoroutineScope  parseEssayTemplatesFromText !kotlinx.coroutines.CoroutineScope  parsePhrasesFromText !kotlinx.coroutines.CoroutineScope  parseWordsFromText !kotlinx.coroutines.CoroutineScope  safeUIOperation !kotlinx.coroutines.CoroutineScope  testTextParsing !kotlinx.coroutines.CoroutineScope  Uri android.net  ACTION_CHECK_TTS_DATA &android.speech.tts.TextToSpeech.Engine  TTSFallbackManager androidx.fragment.app.Fragment  
DataOrganizer com.example.word.data.database  initializeDatabase com.example.word.data.database  organizeAllData com.example.word.data.database  
DataOrganizer 2com.example.word.data.database.DatabaseInitializer  forceReloadData 2com.example.word.data.database.DatabaseInitializer  organizeAllData 2com.example.word.data.database.DatabaseInitializer  BufferedReader com.example.word.data.organizer  Context com.example.word.data.organizer  
DataOrganizer com.example.word.data.organizer  
EssayTemplate com.example.word.data.organizer  	Exception com.example.word.data.organizer  InputStreamReader com.example.word.data.organizer  Int com.example.word.data.organizer  List com.example.word.data.organizer  Log com.example.word.data.organizer  
OrganizedData com.example.word.data.organizer  Phrase com.example.word.data.organizer  Regex com.example.word.data.organizer  String com.example.word.data.organizer  Word com.example.word.data.organizer  also com.example.word.data.organizer  contains com.example.word.data.organizer  indexOfFirst com.example.word.data.organizer  isEmpty com.example.word.data.organizer  
isNotEmpty com.example.word.data.organizer  let com.example.word.data.organizer  matches com.example.word.data.organizer  
mutableListOf com.example.word.data.organizer  
startsWith com.example.word.data.organizer  	substring com.example.word.data.organizer  toInt com.example.word.data.organizer  toRegex com.example.word.data.organizer  trim com.example.word.data.organizer  BufferedReader -com.example.word.data.organizer.DataOrganizer  Context -com.example.word.data.organizer.DataOrganizer  
EssayTemplate -com.example.word.data.organizer.DataOrganizer  	Exception -com.example.word.data.organizer.DataOrganizer  InputStreamReader -com.example.word.data.organizer.DataOrganizer  Int -com.example.word.data.organizer.DataOrganizer  List -com.example.word.data.organizer.DataOrganizer  Log -com.example.word.data.organizer.DataOrganizer  
OrganizedData -com.example.word.data.organizer.DataOrganizer  Phrase -com.example.word.data.organizer.DataOrganizer  Regex -com.example.word.data.organizer.DataOrganizer  String -com.example.word.data.organizer.DataOrganizer  TAG -com.example.word.data.organizer.DataOrganizer  Word -com.example.word.data.organizer.DataOrganizer  also -com.example.word.data.organizer.DataOrganizer   calculateDifficultyFromFrequency -com.example.word.data.organizer.DataOrganizer  contains -com.example.word.data.organizer.DataOrganizer  indexOfFirst -com.example.word.data.organizer.DataOrganizer  isEmpty -com.example.word.data.organizer.DataOrganizer  
isNotEmpty -com.example.word.data.organizer.DataOrganizer  let -com.example.word.data.organizer.DataOrganizer  matches -com.example.word.data.organizer.DataOrganizer  
mutableListOf -com.example.word.data.organizer.DataOrganizer  organizeAllData -com.example.word.data.organizer.DataOrganizer  
parseCET4Word -com.example.word.data.organizer.DataOrganizer  parseEssayTemplate -com.example.word.data.organizer.DataOrganizer  parseHighFreqWord -com.example.word.data.organizer.DataOrganizer  parsePhrase -com.example.word.data.organizer.DataOrganizer  
startsWith -com.example.word.data.organizer.DataOrganizer  	substring -com.example.word.data.organizer.DataOrganizer  toInt -com.example.word.data.organizer.DataOrganizer  toRegex -com.example.word.data.organizer.DataOrganizer  trim -com.example.word.data.organizer.DataOrganizer  essayTemplates ;com.example.word.data.organizer.DataOrganizer.OrganizedData  phrases ;com.example.word.data.organizer.DataOrganizer.OrganizedData  words ;com.example.word.data.organizer.DataOrganizer.OrganizedData  TTSFallbackManager com.example.word.ui.vocabulary  TTSFallbackManager 1com.example.word.ui.vocabulary.VocabularyFragment  ttsFallbackManager 1com.example.word.ui.vocabulary.VocabularyFragment  TTSFallbackManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
DataOrganizer com.example.word.utils  TTSFallbackManager com.example.word.utils  Toast com.example.word.utils  forceReloadData com.example.word.utils  organizeAllData com.example.word.utils  
plusAssign com.example.word.utils  
DataOrganizer *com.example.word.utils.DatabaseDebugHelper  forceReloadData *com.example.word.utils.DatabaseDebugHelper  organizeAllData *com.example.word.utils.DatabaseDebugHelper  Boolean )com.example.word.utils.TTSFallbackManager  Context )com.example.word.utils.TTSFallbackManager  	Exception )com.example.word.utils.TTSFallbackManager  Intent )com.example.word.utils.TTSFallbackManager  Locale )com.example.word.utils.TTSFallbackManager  Log )com.example.word.utils.TTSFallbackManager  String )com.example.word.utils.TTSFallbackManager  TAG )com.example.word.utils.TTSFallbackManager  TextToSpeech )com.example.word.utils.TTSFallbackManager  Toast )com.example.word.utils.TTSFallbackManager  Unit )com.example.word.utils.TTSFallbackManager  checkAndProvideSolution )com.example.word.utils.TTSFallbackManager  context )com.example.word.utils.TTSFallbackManager  
isNotEmpty )com.example.word.utils.TTSFallbackManager  let )com.example.word.utils.TTSFallbackManager  
plusAssign )com.example.word.utils.TTSFallbackManager  showLanguagePackSolution )com.example.word.utils.TTSFallbackManager  showTTSInstallSolution )com.example.word.utils.TTSFallbackManager  showTextAsFallback )com.example.word.utils.TTSFallbackManager  Intent 3com.example.word.utils.TTSFallbackManager.Companion  Locale 3com.example.word.utils.TTSFallbackManager.Companion  Log 3com.example.word.utils.TTSFallbackManager.Companion  TAG 3com.example.word.utils.TTSFallbackManager.Companion  TextToSpeech 3com.example.word.utils.TTSFallbackManager.Companion  Toast 3com.example.word.utils.TTSFallbackManager.Companion  
isNotEmpty 3com.example.word.utils.TTSFallbackManager.Companion  let 3com.example.word.utils.TTSFallbackManager.Companion  
plusAssign 3com.example.word.utils.TTSFallbackManager.Companion  
plusAssign 	java.util  inc kotlin.Long  plus 
kotlin.String  
plusAssign 
kotlin.String  
plusAssign kotlin.collections  
DataOrganizer !kotlinx.coroutines.CoroutineScope  forceReloadData !kotlinx.coroutines.CoroutineScope  organizeAllData !kotlinx.coroutines.CoroutineScope  	JSONArray org.json  AppHealthChecker android.app.Activity  Toast android.app.Activity  
isNotEmpty android.app.Activity  performHealthCheck android.app.Activity  AppHealthChecker android.content.Context  Toast android.content.Context  
isNotEmpty android.content.Context  performHealthCheck android.content.Context  AppHealthChecker android.content.ContextWrapper  Toast android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  performHealthCheck android.content.ContextWrapper  AppHealthChecker  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  performHealthCheck  android.view.ContextThemeWrapper  AppHealthChecker #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  performHealthCheck #androidx.activity.ComponentActivity  AppHealthChecker -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  performHealthCheck -androidx.activity.ComponentActivity.Companion  AppHealthChecker (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  performHealthCheck (androidx.appcompat.app.AppCompatActivity  AppHealthChecker #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  performHealthCheck #androidx.core.app.ComponentActivity  AppHealthChecker &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  performHealthCheck &androidx.fragment.app.FragmentActivity  AppHealthChecker com.example.word  Toast com.example.word  
isNotEmpty com.example.word  performHealthCheck com.example.word  AppHealthChecker com.example.word.MainActivity  Toast com.example.word.MainActivity  
isNotEmpty com.example.word.MainActivity  performHealthCheck com.example.word.MainActivity  performHealthCheckAndInit com.example.word.MainActivity  AppHealthChecker com.example.word.utils  DatabaseCheckResult com.example.word.utils  FileCheckResult com.example.word.utils  HealthCheckResult com.example.word.utils  NavigationCheckResult com.example.word.utils  ParsingCheckResult com.example.word.utils  TTSCheckResult com.example.word.utils  attemptDatabaseFix com.example.word.utils  
check1TxtFile com.example.word.utils  checkDataParsing com.example.word.utils  checkDatabaseHealth com.example.word.utils  checkDatabaseStatus com.example.word.utils  checkNavigationHealth com.example.word.utils  checkTTSHealth com.example.word.utils  generateSummary com.example.word.utils  testTextParsing com.example.word.utils  Boolean 'com.example.word.utils.AppHealthChecker  Context 'com.example.word.utils.AppHealthChecker  DatabaseCheckResult 'com.example.word.utils.AppHealthChecker  DatabaseDebugHelper 'com.example.word.utils.AppHealthChecker  DatabaseInitializer 'com.example.word.utils.AppHealthChecker  Dispatchers 'com.example.word.utils.AppHealthChecker  	Exception 'com.example.word.utils.AppHealthChecker  FileCheckResult 'com.example.word.utils.AppHealthChecker  HealthCheckResult 'com.example.word.utils.AppHealthChecker  Int 'com.example.word.utils.AppHealthChecker  List 'com.example.word.utils.AppHealthChecker  Log 'com.example.word.utils.AppHealthChecker  NavigationCheckResult 'com.example.word.utils.AppHealthChecker  ParsingCheckResult 'com.example.word.utils.AppHealthChecker  SimpleTTSManager 'com.example.word.utils.AppHealthChecker  String 'com.example.word.utils.AppHealthChecker  TAG 'com.example.word.utils.AppHealthChecker  TTSCheckResult 'com.example.word.utils.AppHealthChecker  Thread 'com.example.word.utils.AppHealthChecker  attemptDatabaseFix 'com.example.word.utils.AppHealthChecker  
check1TxtFile 'com.example.word.utils.AppHealthChecker  checkDataParsing 'com.example.word.utils.AppHealthChecker  checkDatabaseHealth 'com.example.word.utils.AppHealthChecker  checkDatabaseStatus 'com.example.word.utils.AppHealthChecker  checkNavigationHealth 'com.example.word.utils.AppHealthChecker  checkTTSHealth 'com.example.word.utils.AppHealthChecker  	emptyList 'com.example.word.utils.AppHealthChecker  forceReloadData 'com.example.word.utils.AppHealthChecker  generateSummary 'com.example.word.utils.AppHealthChecker  
isNotEmpty 'com.example.word.utils.AppHealthChecker  listOf 'com.example.word.utils.AppHealthChecker  
mutableListOf 'com.example.word.utils.AppHealthChecker  performHealthCheck 'com.example.word.utils.AppHealthChecker  testTextParsing 'com.example.word.utils.AppHealthChecker  withContext 'com.example.word.utils.AppHealthChecker  	isHealthy ;com.example.word.utils.AppHealthChecker.DatabaseCheckResult  message ;com.example.word.utils.AppHealthChecker.DatabaseCheckResult  	isHealthy 7com.example.word.utils.AppHealthChecker.FileCheckResult  message 7com.example.word.utils.AppHealthChecker.FileCheckResult  fixes 9com.example.word.utils.AppHealthChecker.HealthCheckResult  	isHealthy 9com.example.word.utils.AppHealthChecker.HealthCheckResult  summary 9com.example.word.utils.AppHealthChecker.HealthCheckResult  	isHealthy =com.example.word.utils.AppHealthChecker.NavigationCheckResult  message =com.example.word.utils.AppHealthChecker.NavigationCheckResult  	isHealthy :com.example.word.utils.AppHealthChecker.ParsingCheckResult  message :com.example.word.utils.AppHealthChecker.ParsingCheckResult  phraseCount :com.example.word.utils.AppHealthChecker.ParsingCheckResult  	wordCount :com.example.word.utils.AppHealthChecker.ParsingCheckResult  	isHealthy 6com.example.word.utils.AppHealthChecker.TTSCheckResult  message 6com.example.word.utils.AppHealthChecker.TTSCheckResult  
isInitialized 9com.example.word.utils.DatabaseDebugHelper.DatabaseStatus  error <com.example.word.utils.DatabaseDebugHelper.TextParsingResult  phraseCount <com.example.word.utils.DatabaseDebugHelper.TextParsingResult  success <com.example.word.utils.DatabaseDebugHelper.TextParsingResult  	wordCount <com.example.word.utils.DatabaseDebugHelper.TextParsingResult  sleep java.lang.Thread  isEmpty kotlin.collections.MutableList  AppHealthChecker !kotlinx.coroutines.CoroutineScope  HealthCheckResult !kotlinx.coroutines.CoroutineScope  attemptDatabaseFix !kotlinx.coroutines.CoroutineScope  checkDataParsing !kotlinx.coroutines.CoroutineScope  checkDatabaseHealth !kotlinx.coroutines.CoroutineScope  checkNavigationHealth !kotlinx.coroutines.CoroutineScope  checkTTSHealth !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  generateSummary !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  performHealthCheck !kotlinx.coroutines.CoroutineScope  AppOptimizer android.app.Activity  markHealthCheckDone android.app.Activity  optimizeAppStartup android.app.Activity  AppOptimizer android.content.Context  markHealthCheckDone android.content.Context  optimizeAppStartup android.content.Context  AppOptimizer android.content.ContextWrapper  markHealthCheckDone android.content.ContextWrapper  optimizeAppStartup android.content.ContextWrapper  AppOptimizer  android.view.ContextThemeWrapper  markHealthCheckDone  android.view.ContextThemeWrapper  optimizeAppStartup  android.view.ContextThemeWrapper  AppOptimizer #androidx.activity.ComponentActivity  markHealthCheckDone #androidx.activity.ComponentActivity  optimizeAppStartup #androidx.activity.ComponentActivity  AppOptimizer -androidx.activity.ComponentActivity.Companion  markHealthCheckDone -androidx.activity.ComponentActivity.Companion  optimizeAppStartup -androidx.activity.ComponentActivity.Companion  AppOptimizer (androidx.appcompat.app.AppCompatActivity  markHealthCheckDone (androidx.appcompat.app.AppCompatActivity  optimizeAppStartup (androidx.appcompat.app.AppCompatActivity  AppOptimizer #androidx.core.app.ComponentActivity  markHealthCheckDone #androidx.core.app.ComponentActivity  optimizeAppStartup #androidx.core.app.ComponentActivity  AppOptimizer androidx.fragment.app.Fragment  markDatabaseCheckDone androidx.fragment.app.Fragment  markTTSInitDone androidx.fragment.app.Fragment  shouldInitializeTTS androidx.fragment.app.Fragment  shouldPerformDatabaseCheck androidx.fragment.app.Fragment  AppOptimizer &androidx.fragment.app.FragmentActivity  markHealthCheckDone &androidx.fragment.app.FragmentActivity  optimizeAppStartup &androidx.fragment.app.FragmentActivity  AppOptimizer com.example.word  markHealthCheckDone com.example.word  optimizeAppStartup com.example.word  AppOptimizer com.example.word.MainActivity  markHealthCheckDone com.example.word.MainActivity  optimizeAppStartup com.example.word.MainActivity  AppOptimizer com.example.word.ui.vocabulary  markDatabaseCheckDone com.example.word.ui.vocabulary  markTTSInitDone com.example.word.ui.vocabulary  shouldInitializeTTS com.example.word.ui.vocabulary  shouldPerformDatabaseCheck com.example.word.ui.vocabulary  AppOptimizer 1com.example.word.ui.vocabulary.VocabularyFragment  markDatabaseCheckDone 1com.example.word.ui.vocabulary.VocabularyFragment  markTTSInitDone 1com.example.word.ui.vocabulary.VocabularyFragment  shouldInitializeTTS 1com.example.word.ui.vocabulary.VocabularyFragment  shouldPerformDatabaseCheck 1com.example.word.ui.vocabulary.VocabularyFragment  AppOptimizer ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  markDatabaseCheckDone ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  markTTSInitDone ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  shouldInitializeTTS ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  shouldPerformDatabaseCheck ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  AppOptimizer com.example.word.utils  KEY_DATABASE_CHECK_DONE com.example.word.utils  KEY_HEALTH_CHECK_DONE com.example.word.utils  KEY_LAST_CHECK_TIME com.example.word.utils  KEY_TTS_INIT_DONE com.example.word.utils  SharedPreferences com.example.word.utils  StartupOptimizationResult com.example.word.utils  joinToString com.example.word.utils  Boolean #com.example.word.utils.AppOptimizer  CHECK_INTERVAL #com.example.word.utils.AppOptimizer  Context #com.example.word.utils.AppOptimizer  KEY_DATABASE_CHECK_DONE #com.example.word.utils.AppOptimizer  KEY_HEALTH_CHECK_DONE #com.example.word.utils.AppOptimizer  KEY_LAST_CHECK_TIME #com.example.word.utils.AppOptimizer  KEY_TTS_INIT_DONE #com.example.word.utils.AppOptimizer  List #com.example.word.utils.AppOptimizer  Log #com.example.word.utils.AppOptimizer  
PREFS_NAME #com.example.word.utils.AppOptimizer  SharedPreferences #com.example.word.utils.AppOptimizer  StartupOptimizationResult #com.example.word.utils.AppOptimizer  String #com.example.word.utils.AppOptimizer  System #com.example.word.utils.AppOptimizer  TAG #com.example.word.utils.AppOptimizer  
appendLine #com.example.word.utils.AppOptimizer  buildString #com.example.word.utils.AppOptimizer  getPrefs #com.example.word.utils.AppOptimizer  
isNotEmpty #com.example.word.utils.AppOptimizer  joinToString #com.example.word.utils.AppOptimizer  markDatabaseCheckDone #com.example.word.utils.AppOptimizer  markHealthCheckDone #com.example.word.utils.AppOptimizer  markTTSInitDone #com.example.word.utils.AppOptimizer  
mutableListOf #com.example.word.utils.AppOptimizer  optimizeAppStartup #com.example.word.utils.AppOptimizer  shouldInitializeTTS #com.example.word.utils.AppOptimizer  shouldPerformDatabaseCheck #com.example.word.utils.AppOptimizer  shouldPerformHealthCheck #com.example.word.utils.AppOptimizer  hasOptimizations =com.example.word.utils.AppOptimizer.StartupOptimizationResult  
isNotEmpty =com.example.word.utils.AppOptimizer.StartupOptimizationResult  joinToString =com.example.word.utils.AppOptimizer.StartupOptimizationResult  needsHealthCheck =com.example.word.utils.AppOptimizer.StartupOptimizationResult  
optimizations =com.example.word.utils.AppOptimizer.StartupOptimizationResult  summary =com.example.word.utils.AppOptimizer.StartupOptimizationResult  KEY_DATABASE_CHECK_DONE java.lang.StringBuilder  KEY_HEALTH_CHECK_DONE java.lang.StringBuilder  KEY_LAST_CHECK_TIME java.lang.StringBuilder  KEY_TTS_INIT_DONE java.lang.StringBuilder  joinToString kotlin.collections  joinToString kotlin.collections.List  joinToString kotlin.sequences  AppOptimizer !kotlinx.coroutines.CoroutineScope  markHealthCheckDone !kotlinx.coroutines.CoroutineScope  optimizeAppStartup !kotlinx.coroutines.CoroutineScope  Log android.app.Activity  PerformanceMonitor android.app.Activity  TAG android.app.Activity  startMonitoring android.app.Activity  stopMonitoring android.app.Activity  PerformanceMonitor android.content.Context  startMonitoring android.content.Context  stopMonitoring android.content.Context  PerformanceMonitor android.content.ContextWrapper  startMonitoring android.content.ContextWrapper  stopMonitoring android.content.ContextWrapper  Handler 
android.os  Looper 
android.os  post android.os.Handler  
getMainLooper android.os.Looper  myLooper android.os.Looper  Log  android.view.ContextThemeWrapper  PerformanceMonitor  android.view.ContextThemeWrapper  TAG  android.view.ContextThemeWrapper  startMonitoring  android.view.ContextThemeWrapper  stopMonitoring  android.view.ContextThemeWrapper  Log #androidx.activity.ComponentActivity  PerformanceMonitor #androidx.activity.ComponentActivity  TAG #androidx.activity.ComponentActivity  startMonitoring #androidx.activity.ComponentActivity  stopMonitoring #androidx.activity.ComponentActivity  Log -androidx.activity.ComponentActivity.Companion  PerformanceMonitor -androidx.activity.ComponentActivity.Companion  TAG -androidx.activity.ComponentActivity.Companion  startMonitoring -androidx.activity.ComponentActivity.Companion  stopMonitoring -androidx.activity.ComponentActivity.Companion  Log (androidx.appcompat.app.AppCompatActivity  PerformanceMonitor (androidx.appcompat.app.AppCompatActivity  TAG (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  startMonitoring (androidx.appcompat.app.AppCompatActivity  stopMonitoring (androidx.appcompat.app.AppCompatActivity  Log #androidx.core.app.ComponentActivity  PerformanceMonitor #androidx.core.app.ComponentActivity  TAG #androidx.core.app.ComponentActivity  startMonitoring #androidx.core.app.ComponentActivity  stopMonitoring #androidx.core.app.ComponentActivity  Dispatchers androidx.fragment.app.Fragment  PerformanceMonitor androidx.fragment.app.Fragment  debugDatabaseStatus androidx.fragment.app.Fragment  simpleTTSManager androidx.fragment.app.Fragment  Log &androidx.fragment.app.FragmentActivity  PerformanceMonitor &androidx.fragment.app.FragmentActivity  TAG &androidx.fragment.app.FragmentActivity  startMonitoring &androidx.fragment.app.FragmentActivity  stopMonitoring &androidx.fragment.app.FragmentActivity  PerformanceMonitor com.example.word  startMonitoring com.example.word  stopMonitoring com.example.word  BottomNavigationView com.example.word.MainActivity  Bundle com.example.word.MainActivity  	Exception com.example.word.MainActivity  Log com.example.word.MainActivity  NavHostFragment com.example.word.MainActivity  PerformanceMonitor com.example.word.MainActivity  TAG com.example.word.MainActivity  startMonitoring com.example.word.MainActivity  startPerformanceMonitoring com.example.word.MainActivity  stopMonitoring com.example.word.MainActivity  ActivityMainBinding 'com.example.word.MainActivity.Companion  AppHealthChecker 'com.example.word.MainActivity.Companion  AppOptimizer 'com.example.word.MainActivity.Companion  DatabaseInitializer 'com.example.word.MainActivity.Companion  IllegalStateException 'com.example.word.MainActivity.Companion  Log 'com.example.word.MainActivity.Companion  MODE_PRIVATE 'com.example.word.MainActivity.Companion  PerformanceMonitor 'com.example.word.MainActivity.Companion  R 'com.example.word.MainActivity.Companion  TAG 'com.example.word.MainActivity.Companion  Toast 'com.example.word.MainActivity.Companion  android 'com.example.word.MainActivity.Companion  androidx 'com.example.word.MainActivity.Companion  com 'com.example.word.MainActivity.Companion  finish 'com.example.word.MainActivity.Companion  getSharedPreferences 'com.example.word.MainActivity.Companion  initializeDatabase 'com.example.word.MainActivity.Companion  intent 'com.example.word.MainActivity.Companion  
isNotEmpty 'com.example.word.MainActivity.Companion  kotlinx 'com.example.word.MainActivity.Companion  launch 'com.example.word.MainActivity.Companion  lifecycleScope 'com.example.word.MainActivity.Companion  markHealthCheckDone 'com.example.word.MainActivity.Companion  optimizeAppStartup 'com.example.word.MainActivity.Companion  performHealthCheck 'com.example.word.MainActivity.Companion  resetAppData 'com.example.word.MainActivity.Companion  
resetDatabase 'com.example.word.MainActivity.Companion  
runOnUiThread 'com.example.word.MainActivity.Companion  setupWithNavController 'com.example.word.MainActivity.Companion  
startActivity 'com.example.word.MainActivity.Companion  startMonitoring 'com.example.word.MainActivity.Companion  stopMonitoring 'com.example.word.MainActivity.Companion  use com.example.word.data.organizer  use -com.example.word.data.organizer.DataOrganizer  Dispatchers com.example.word.ui.vocabulary  PerformanceMonitor com.example.word.ui.vocabulary  debugDatabaseStatus com.example.word.ui.vocabulary  simpleTTSManager com.example.word.ui.vocabulary  Dispatchers 1com.example.word.ui.vocabulary.VocabularyFragment  PerformanceMonitor 1com.example.word.ui.vocabulary.VocabularyFragment  Dispatchers ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  PerformanceMonitor ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  debugDatabaseStatus ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  simpleTTSManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
AtomicBoolean com.example.word.utils  Handler com.example.word.utils  Job com.example.word.utils  Looper com.example.word.utils  PerformanceMonitor com.example.word.utils  PerformanceReport com.example.word.utils  Runtime com.example.word.utils  delay com.example.word.utils  isActive com.example.word.utils  isMonitoring com.example.word.utils  monitorMainThread com.example.word.utils  monitorMemoryUsage com.example.word.utils  
AtomicBoolean )com.example.word.utils.PerformanceMonitor  Boolean )com.example.word.utils.PerformanceMonitor  Context )com.example.word.utils.PerformanceMonitor  CoroutineScope )com.example.word.utils.PerformanceMonitor  Dispatchers )com.example.word.utils.PerformanceMonitor  	Exception )com.example.word.utils.PerformanceMonitor  Handler )com.example.word.utils.PerformanceMonitor  Job )com.example.word.utils.PerformanceMonitor  Log )com.example.word.utils.PerformanceMonitor  Long )com.example.word.utils.PerformanceMonitor  Looper )com.example.word.utils.PerformanceMonitor  PerformanceReport )com.example.word.utils.PerformanceMonitor  Runtime )com.example.word.utils.PerformanceMonitor  String )com.example.word.utils.PerformanceMonitor  System )com.example.word.utils.PerformanceMonitor  TAG )com.example.word.utils.PerformanceMonitor  Unit )com.example.word.utils.PerformanceMonitor  delay )com.example.word.utils.PerformanceMonitor  isActive )com.example.word.utils.PerformanceMonitor  isMonitoring )com.example.word.utils.PerformanceMonitor  launch )com.example.word.utils.PerformanceMonitor  monitorMainThread )com.example.word.utils.PerformanceMonitor  monitorMemoryUsage )com.example.word.utils.PerformanceMonitor  
monitoringJob )com.example.word.utils.PerformanceMonitor  safeUIOperation )com.example.word.utils.PerformanceMonitor  startMonitoring )com.example.word.utils.PerformanceMonitor  stopMonitoring )com.example.word.utils.PerformanceMonitor  isMonitoring ;com.example.word.utils.PerformanceMonitor.PerformanceReport  maxMemoryMB ;com.example.word.utils.PerformanceMonitor.PerformanceReport  memoryUsagePercentage ;com.example.word.utils.PerformanceMonitor.PerformanceReport  usedMemoryMB ;com.example.word.utils.PerformanceMonitor.PerformanceReport  use java.io.BufferedReader  
freeMemory java.lang.Runtime  
getRuntime java.lang.Runtime  	maxMemory java.lang.Runtime  totalMemory java.lang.Runtime  gc java.lang.System  
AtomicBoolean java.util.concurrent.atomic  get )java.util.concurrent.atomic.AtomicBoolean  	getAndSet )java.util.concurrent.atomic.AtomicBoolean  times kotlin.Long  
AtomicBoolean kotlinx.coroutines  Delay kotlinx.coroutines  Handler kotlinx.coroutines  Long kotlinx.coroutines  Looper kotlinx.coroutines  PerformanceReport kotlinx.coroutines  Runtime kotlinx.coroutines  System kotlinx.coroutines  isActive kotlinx.coroutines  isMonitoring kotlinx.coroutines  monitorMainThread kotlinx.coroutines  monitorMemoryUsage kotlinx.coroutines  PerformanceMonitor !kotlinx.coroutines.CoroutineScope  SimpleTTSManager !kotlinx.coroutines.CoroutineScope  debugDatabaseStatus !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  isActive !kotlinx.coroutines.CoroutineScope  isMonitoring !kotlinx.coroutines.CoroutineScope  markDatabaseCheckDone !kotlinx.coroutines.CoroutineScope  markTTSInitDone !kotlinx.coroutines.CoroutineScope  monitorMainThread !kotlinx.coroutines.CoroutineScope  monitorMemoryUsage !kotlinx.coroutines.CoroutineScope  simpleTTSManager !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  	Companion com.example.word.MainActivity  AudioFormat 
android.media  AudioManager 
android.media  
AudioTrack 
android.media  EnhancedOnlineTTSManager androidx.fragment.app.Fragment  generateTTSAudio androidx.fragment.app.Fragment  generateTTSAudio com.example.word.ui.settings  generateTTSAudio -com.example.word.ui.settings.SettingsFragment  EnhancedOnlineTTSManager com.example.word.ui.vocabulary  OnlineTTSHelper com.example.word.ui.vocabulary  
simpleAPITest com.example.word.ui.vocabulary  EnhancedOnlineTTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  OnlineTTSHelper 1com.example.word.ui.vocabulary.VocabularyFragment  enhancedOnlineTTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  
simpleAPITest 1com.example.word.ui.vocabulary.VocabularyFragment  EnhancedOnlineTTSManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  OnlineTTSHelper ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
simpleAPITest ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  CompletableDeferred com.example.word.utils  EnhancedOnlineTTSManager com.example.word.utils  
JSONObject com.example.word.utils  TTSVoice com.example.word.utils  VOICES_API_URL com.example.word.utils  availableVoices com.example.word.utils  cachedVoices com.example.word.utils  
clearCache com.example.word.utils  count com.example.word.utils  firstOrNull com.example.word.utils  generateTTSAudio com.example.word.utils  generateTTSAudioInternal com.example.word.utils  getAPIStatusSummary com.example.word.utils  getAvailableVoices com.example.word.utils  getRecommendedEnglishVoice com.example.word.utils  
isInitialized com.example.word.utils  
isNullOrEmpty com.example.word.utils  parseVoicesResponse com.example.word.utils  preferredVoice com.example.word.utils  
simpleAPITest com.example.word.utils  toList com.example.word.utils  until com.example.word.utils  Boolean /com.example.word.utils.EnhancedOnlineTTSManager  Context /com.example.word.utils.EnhancedOnlineTTSManager  List /com.example.word.utils.EnhancedOnlineTTSManager  Log /com.example.word.utils.EnhancedOnlineTTSManager  OnlineTTSHelper /com.example.word.utils.EnhancedOnlineTTSManager  String /com.example.word.utils.EnhancedOnlineTTSManager  TAG /com.example.word.utils.EnhancedOnlineTTSManager  Unit /com.example.word.utils.EnhancedOnlineTTSManager  
appendLine /com.example.word.utils.EnhancedOnlineTTSManager  availableVoices /com.example.word.utils.EnhancedOnlineTTSManager  buildString /com.example.word.utils.EnhancedOnlineTTSManager  
clearCache /com.example.word.utils.EnhancedOnlineTTSManager  count /com.example.word.utils.EnhancedOnlineTTSManager  	emptyList /com.example.word.utils.EnhancedOnlineTTSManager  filter /com.example.word.utils.EnhancedOnlineTTSManager  generateTTSAudio /com.example.word.utils.EnhancedOnlineTTSManager  getAPIStatusSummary /com.example.word.utils.EnhancedOnlineTTSManager  getAvailableVoices /com.example.word.utils.EnhancedOnlineTTSManager  getRecommendedEnglishVoice /com.example.word.utils.EnhancedOnlineTTSManager  
initialize /com.example.word.utils.EnhancedOnlineTTSManager  isBlank /com.example.word.utils.EnhancedOnlineTTSManager  
isInitialized /com.example.word.utils.EnhancedOnlineTTSManager  
isNotEmpty /com.example.word.utils.EnhancedOnlineTTSManager  isReady /com.example.word.utils.EnhancedOnlineTTSManager  let /com.example.word.utils.EnhancedOnlineTTSManager  preferredVoice /com.example.word.utils.EnhancedOnlineTTSManager  release /com.example.word.utils.EnhancedOnlineTTSManager  
simpleAPITest /com.example.word.utils.EnhancedOnlineTTSManager  speak /com.example.word.utils.EnhancedOnlineTTSManager  take /com.example.word.utils.EnhancedOnlineTTSManager  
testOnlineTTS /com.example.word.utils.EnhancedOnlineTTSManager  toList /com.example.word.utils.EnhancedOnlineTTSManager  Log 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  OnlineTTSHelper 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  TAG 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  
appendLine 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  availableVoices 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  buildString 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  
clearCache 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  count 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  	emptyList 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  filter 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  generateTTSAudio 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  getAPIStatusSummary 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  getAvailableVoices 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  getRecommendedEnglishVoice 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  isBlank 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  
isInitialized 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  
isNotEmpty 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  preferredVoice 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  
simpleAPITest 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  take 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  toList 9com.example.word.utils.EnhancedOnlineTTSManager.Companion  TTSVoice ?com.example.word.utils.EnhancedOnlineTTSManager.OnlineTTSHelper  Boolean &com.example.word.utils.OnlineTTSHelper  CompletableDeferred &com.example.word.utils.OnlineTTSHelper  Context &com.example.word.utils.OnlineTTSHelper  	Exception &com.example.word.utils.OnlineTTSHelper  Float &com.example.word.utils.OnlineTTSHelper  
JSONObject &com.example.word.utils.OnlineTTSHelper  List &com.example.word.utils.OnlineTTSHelper  Pair &com.example.word.utils.OnlineTTSHelper  String &com.example.word.utils.OnlineTTSHelper  TTSVoice &com.example.word.utils.OnlineTTSHelper  Unit &com.example.word.utils.OnlineTTSHelper  VOICES_API_URL &com.example.word.utils.OnlineTTSHelper  cachedVoices &com.example.word.utils.OnlineTTSHelper  
clearCache &com.example.word.utils.OnlineTTSHelper  	emptyList &com.example.word.utils.OnlineTTSHelper  filter &com.example.word.utils.OnlineTTSHelper  firstOrNull &com.example.word.utils.OnlineTTSHelper  generateTTSAudio &com.example.word.utils.OnlineTTSHelper  generateTTSAudioInternal &com.example.word.utils.OnlineTTSHelper  getAPIStatusSummary &com.example.word.utils.OnlineTTSHelper  getAvailableVoices &com.example.word.utils.OnlineTTSHelper  getRecommendedEnglishVoice &com.example.word.utils.OnlineTTSHelper  
isNullOrEmpty &com.example.word.utils.OnlineTTSHelper  let &com.example.word.utils.OnlineTTSHelper  minOf &com.example.word.utils.OnlineTTSHelper  
mutableListOf &com.example.word.utils.OnlineTTSHelper  parseVoicesResponse &com.example.word.utils.OnlineTTSHelper  
startsWith &com.example.word.utils.OnlineTTSHelper  until &com.example.word.utils.OnlineTTSHelper  displayName /com.example.word.utils.OnlineTTSHelper.TTSVoice  gender /com.example.word.utils.OnlineTTSHelper.TTSVoice  	isChinese /com.example.word.utils.OnlineTTSHelper.TTSVoice  	isEnglish /com.example.word.utils.OnlineTTSHelper.TTSVoice  language /com.example.word.utils.OnlineTTSHelper.TTSVoice  name /com.example.word.utils.OnlineTTSHelper.TTSVoice  
startsWith /com.example.word.utils.OnlineTTSHelper.TTSVoice  API_KEY java.lang.StringBuilder  
JSONObject java.lang.StringBuilder  OnlineTTSHelper java.lang.StringBuilder  TTS_API_URL java.lang.StringBuilder  VOICES_API_URL java.lang.StringBuilder  append java.lang.StringBuilder  availableVoices java.lang.StringBuilder  cachedVoices java.lang.StringBuilder  count java.lang.StringBuilder  filter java.lang.StringBuilder  getAPIStatusSummary java.lang.StringBuilder  getRecommendedEnglishVoice java.lang.StringBuilder  
isInitialized java.lang.StringBuilder  
isNotEmpty java.lang.StringBuilder  minOf java.lang.StringBuilder  preferredVoice java.lang.StringBuilder  until java.lang.StringBuilder  generateTTSAudio 	java.util  toList kotlin  toString kotlin.Float  count kotlin.collections  firstOrNull kotlin.collections  
isNullOrEmpty kotlin.collections  toList kotlin.collections  count kotlin.collections.List  firstOrNull kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  let kotlin.collections.List  toList kotlin.collections.List  firstOrNull 
kotlin.ranges  count kotlin.sequences  firstOrNull kotlin.sequences  toList kotlin.sequences  count kotlin.text  firstOrNull kotlin.text  
isNullOrEmpty kotlin.text  toList kotlin.text  CompletableDeferred kotlinx.coroutines  Float kotlinx.coroutines  
JSONObject kotlinx.coroutines  List kotlinx.coroutines  OnlineTTSHelper kotlinx.coroutines  Pair kotlinx.coroutines  TTSVoice kotlinx.coroutines  VOICES_API_URL kotlinx.coroutines  availableVoices kotlinx.coroutines  cachedVoices kotlinx.coroutines  
clearCache kotlinx.coroutines  count kotlinx.coroutines  	emptyList kotlinx.coroutines  filter kotlinx.coroutines  firstOrNull kotlinx.coroutines  forEach kotlinx.coroutines  generateTTSAudio kotlinx.coroutines  generateTTSAudioInternal kotlinx.coroutines  getAPIStatusSummary kotlinx.coroutines  getAvailableVoices kotlinx.coroutines  getRecommendedEnglishVoice kotlinx.coroutines  
isInitialized kotlinx.coroutines  
isNullOrEmpty kotlinx.coroutines  minOf kotlinx.coroutines  
mutableListOf kotlinx.coroutines  parseVoicesResponse kotlinx.coroutines  preferredVoice kotlinx.coroutines  
simpleAPITest kotlinx.coroutines  toList kotlinx.coroutines  until kotlinx.coroutines  await &kotlinx.coroutines.CompletableDeferred  complete &kotlinx.coroutines.CompletableDeferred  CompletableDeferred !kotlinx.coroutines.CoroutineScope  
JSONObject !kotlinx.coroutines.CoroutineScope  Pair !kotlinx.coroutines.CoroutineScope  VOICES_API_URL !kotlinx.coroutines.CoroutineScope  cachedVoices !kotlinx.coroutines.CoroutineScope  generateTTSAudioInternal !kotlinx.coroutines.CoroutineScope  getAvailableVoices !kotlinx.coroutines.CoroutineScope  getRecommendedEnglishVoice !kotlinx.coroutines.CoroutineScope  
isNullOrEmpty !kotlinx.coroutines.CoroutineScope  minOf !kotlinx.coroutines.CoroutineScope  parseVoicesResponse !kotlinx.coroutines.CoroutineScope  TTSVoice "kotlinx.coroutines.OnlineTTSHelper  CompletableDeferred okhttp3  Float okhttp3  
JSONObject okhttp3  List okhttp3  Pair okhttp3  TTSVoice okhttp3  VOICES_API_URL okhttp3  cachedVoices okhttp3  	emptyList okhttp3  filter okhttp3  firstOrNull okhttp3  generateTTSAudioInternal okhttp3  getAvailableVoices okhttp3  getRecommendedEnglishVoice okhttp3  
isNullOrEmpty okhttp3  minOf okhttp3  
mutableListOf okhttp3  parseVoicesResponse okhttp3  until okhttp3  optJSONArray org.json.JSONObject  
optJSONObject org.json.JSONObject  ResourceMonitor android.app.Activity  ResourceMonitor android.content.Context  ResourceMonitor android.content.ContextWrapper  ACTION_SETTINGS android.provider.Settings  ERROR_INVALID_REQUEST android.speech.tts.TextToSpeech  
ERROR_NETWORK android.speech.tts.TextToSpeech  ERROR_NETWORK_TIMEOUT android.speech.tts.TextToSpeech  ERROR_NOT_INSTALLED_YET android.speech.tts.TextToSpeech  
ERROR_SERVICE android.speech.tts.TextToSpeech  ResourceMonitor  android.view.ContextThemeWrapper  ResourceMonitor #androidx.activity.ComponentActivity  ResourceMonitor -androidx.activity.ComponentActivity.Companion  ResourceMonitor (androidx.appcompat.app.AppCompatActivity  ResourceMonitor #androidx.core.app.ComponentActivity  ResourceMonitor &androidx.fragment.app.FragmentActivity  ResourceMonitor com.example.word  ResourceMonitor com.example.word.MainActivity  ResourceMonitor 'com.example.word.MainActivity.Companion  
AtomicLong com.example.word.utils  	Closeable com.example.word.utils  ConcurrentHashMap com.example.word.utils  	FixResult com.example.word.utils  InterruptedException com.example.word.utils  ResourceInfo com.example.word.utils  ResourceMonitor com.example.word.utils  SystemIssue com.example.word.utils  SystemIssueResolver com.example.word.utils  TTSError com.example.word.utils  TTSErrorHandler com.example.word.utils  TTSErrorInfo com.example.word.utils  UnsatisfiedLinkError com.example.word.utils  activeResources com.example.word.utils  analyzeTTSError com.example.word.utils  
attemptTTSFix com.example.word.utils  checkResourceLeaks com.example.word.utils  checkTTSAvailability com.example.word.utils  cleanupExpiredResources com.example.word.utils  
component1 com.example.word.utils  
component2 com.example.word.utils  createRecoveryStrategy com.example.word.utils  detectAndFixIssues com.example.word.utils  forceGarbageCollection com.example.word.utils  
getMemoryInfo com.example.word.utils  getResourceReport com.example.word.utils  getSystemReport com.example.word.utils  resourceCounter com.example.word.utils  set com.example.word.utils  SystemIssueResolver 'com.example.word.utils.AppHealthChecker  detectAndFixIssues 'com.example.word.utils.AppHealthChecker  delay &com.example.word.utils.OnlineTTSHelper  ResourceMonitor )com.example.word.utils.PerformanceMonitor  checkResourceLeaks )com.example.word.utils.PerformanceMonitor  forceGarbageCollection )com.example.word.utils.PerformanceMonitor  
isNotEmpty )com.example.word.utils.PerformanceMonitor  Any &com.example.word.utils.ResourceMonitor  
AtomicLong &com.example.word.utils.ResourceMonitor  	Closeable &com.example.word.utils.ResourceMonitor  ConcurrentHashMap &com.example.word.utils.ResourceMonitor  Context &com.example.word.utils.ResourceMonitor  CoroutineScope &com.example.word.utils.ResourceMonitor  Dispatchers &com.example.word.utils.ResourceMonitor  	Exception &com.example.word.utils.ResourceMonitor  InterruptedException &com.example.word.utils.ResourceMonitor  Job &com.example.word.utils.ResourceMonitor  List &com.example.word.utils.ResourceMonitor  Log &com.example.word.utils.ResourceMonitor  Long &com.example.word.utils.ResourceMonitor  Pair &com.example.word.utils.ResourceMonitor  R &com.example.word.utils.ResourceMonitor  ResourceInfo &com.example.word.utils.ResourceMonitor  Runtime &com.example.word.utils.ResourceMonitor  String &com.example.word.utils.ResourceMonitor  System &com.example.word.utils.ResourceMonitor  T &com.example.word.utils.ResourceMonitor  TAG &com.example.word.utils.ResourceMonitor  Thread &com.example.word.utils.ResourceMonitor  activeResources &com.example.word.utils.ResourceMonitor  
appendLine &com.example.word.utils.ResourceMonitor  buildString &com.example.word.utils.ResourceMonitor  checkResourceLeaks &com.example.word.utils.ResourceMonitor  cleanupExpiredResources &com.example.word.utils.ResourceMonitor  
component1 &com.example.word.utils.ResourceMonitor  
component2 &com.example.word.utils.ResourceMonitor  delay &com.example.word.utils.ResourceMonitor  forEach &com.example.word.utils.ResourceMonitor  forceGarbageCollection &com.example.word.utils.ResourceMonitor  
getMemoryInfo &com.example.word.utils.ResourceMonitor  getResourceReport &com.example.word.utils.ResourceMonitor  getSystemReport &com.example.word.utils.ResourceMonitor  
isNotEmpty &com.example.word.utils.ResourceMonitor  joinToString &com.example.word.utils.ResourceMonitor  launch &com.example.word.utils.ResourceMonitor  let &com.example.word.utils.ResourceMonitor  
mutableListOf &com.example.word.utils.ResourceMonitor  registerResource &com.example.word.utils.ResourceMonitor  resourceCounter &com.example.word.utils.ResourceMonitor  	safeClose &com.example.word.utils.ResourceMonitor  set &com.example.word.utils.ResourceMonitor  startMonitoring &com.example.word.utils.ResourceMonitor  unregisterResource &com.example.word.utils.ResourceMonitor  use &com.example.word.utils.ResourceMonitor  	createdAt 3com.example.word.utils.ResourceMonitor.ResourceInfo  let 3com.example.word.utils.ResourceMonitor.ResourceInfo  name 3com.example.word.utils.ResourceMonitor.ResourceInfo  type 3com.example.word.utils.ResourceMonitor.ResourceInfo  TTSErrorHandler 'com.example.word.utils.SimpleTTSManager  analyzeTTSError 'com.example.word.utils.SimpleTTSManager  TTSErrorHandler 1com.example.word.utils.SimpleTTSManager.Companion  analyzeTTSError 1com.example.word.utils.SimpleTTSManager.Companion  Boolean *com.example.word.utils.SystemIssueResolver  CompletableDeferred *com.example.word.utils.SystemIssueResolver  Context *com.example.word.utils.SystemIssueResolver  Dispatchers *com.example.word.utils.SystemIssueResolver  	Exception *com.example.word.utils.SystemIssueResolver  	FixResult *com.example.word.utils.SystemIssueResolver  List *com.example.word.utils.SystemIssueResolver  Log *com.example.word.utils.SystemIssueResolver  Pair *com.example.word.utils.SystemIssueResolver  ResourceMonitor *com.example.word.utils.SystemIssueResolver  Runtime *com.example.word.utils.SystemIssueResolver  String *com.example.word.utils.SystemIssueResolver  System *com.example.word.utils.SystemIssueResolver  SystemIssue *com.example.word.utils.SystemIssueResolver  TAG *com.example.word.utils.SystemIssueResolver  TTSErrorHandler *com.example.word.utils.SystemIssueResolver  UnsatisfiedLinkError *com.example.word.utils.SystemIssueResolver  analyzeTTSError *com.example.word.utils.SystemIssueResolver  
appendLine *com.example.word.utils.SystemIssueResolver  
attemptTTSFix *com.example.word.utils.SystemIssueResolver  buildString *com.example.word.utils.SystemIssueResolver  checkMainThreadHealth *com.example.word.utils.SystemIssueResolver  checkMissingLibraries *com.example.word.utils.SystemIssueResolver  checkResourceLeaks *com.example.word.utils.SystemIssueResolver  checkTTSAvailability *com.example.word.utils.SystemIssueResolver  cleanupExpiredResources *com.example.word.utils.SystemIssueResolver  count *com.example.word.utils.SystemIssueResolver  createRecoveryStrategy *com.example.word.utils.SystemIssueResolver  detectAndFixIssues *com.example.word.utils.SystemIssueResolver  	emptyList *com.example.word.utils.SystemIssueResolver  fixMemoryPressure *com.example.word.utils.SystemIssueResolver  fixResourceLeaks *com.example.word.utils.SystemIssueResolver  fixTTSIssues *com.example.word.utils.SystemIssueResolver  forceGarbageCollection *com.example.word.utils.SystemIssueResolver  getSystemReport *com.example.word.utils.SystemIssueResolver  
isNotEmpty *com.example.word.utils.SystemIssueResolver  joinToString *com.example.word.utils.SystemIssueResolver  listOf *com.example.word.utils.SystemIssueResolver  
mutableListOf *com.example.word.utils.SystemIssueResolver  withContext *com.example.word.utils.SystemIssueResolver  fixed 4com.example.word.utils.SystemIssueResolver.FixResult  issue 4com.example.word.utils.SystemIssueResolver.FixResult  message 4com.example.word.utils.SystemIssueResolver.FixResult  suggestions 4com.example.word.utils.SystemIssueResolver.FixResult  LIBRARY_MISSING 6com.example.word.utils.SystemIssueResolver.SystemIssue  MAIN_THREAD_BLOCKED 6com.example.word.utils.SystemIssueResolver.SystemIssue  MEMORY_PRESSURE 6com.example.word.utils.SystemIssueResolver.SystemIssue  
RESOURCE_LEAK 6com.example.word.utils.SystemIssueResolver.SystemIssue  TTS_UNAVAILABLE 6com.example.word.utils.SystemIssueResolver.SystemIssue  
UNKNOWN_ISSUE 6com.example.word.utils.SystemIssueResolver.SystemIssue  name 6com.example.word.utils.SystemIssueResolver.SystemIssue  Boolean &com.example.word.utils.TTSErrorHandler  CompletableDeferred &com.example.word.utils.TTSErrorHandler  Context &com.example.word.utils.TTSErrorHandler  CoroutineScope &com.example.word.utils.TTSErrorHandler  Dispatchers &com.example.word.utils.TTSErrorHandler  	Exception &com.example.word.utils.TTSErrorHandler  Int &com.example.word.utils.TTSErrorHandler  Intent &com.example.word.utils.TTSErrorHandler  List &com.example.word.utils.TTSErrorHandler  Log &com.example.word.utils.TTSErrorHandler  Pair &com.example.word.utils.TTSErrorHandler  String &com.example.word.utils.TTSErrorHandler  System &com.example.word.utils.TTSErrorHandler  TAG &com.example.word.utils.TTSErrorHandler  TTSError &com.example.word.utils.TTSErrorHandler  TTSErrorInfo &com.example.word.utils.TTSErrorHandler  TextToSpeech &com.example.word.utils.TTSErrorHandler  Unit &com.example.word.utils.TTSErrorHandler  analyzeTTSError &com.example.word.utils.TTSErrorHandler  android &com.example.word.utils.TTSErrorHandler  
appendLine &com.example.word.utils.TTSErrorHandler  
attemptTTSFix &com.example.word.utils.TTSErrorHandler  buildString &com.example.word.utils.TTSErrorHandler  checkTTSAvailability &com.example.word.utils.TTSErrorHandler  createRecoveryStrategy &com.example.word.utils.TTSErrorHandler  launch &com.example.word.utils.TTSErrorHandler  listOf &com.example.word.utils.TTSErrorHandler  openTTSSettings &com.example.word.utils.TTSErrorHandler  withContext &com.example.word.utils.TTSErrorHandler  ENGINE_NOT_AVAILABLE /com.example.word.utils.TTSErrorHandler.TTSError  INITIALIZATION_FAILED /com.example.word.utils.TTSErrorHandler.TTSError  LANGUAGE_NOT_SUPPORTED /com.example.word.utils.TTSErrorHandler.TTSError  
NETWORK_ERROR /com.example.word.utils.TTSErrorHandler.TTSError  
UNKNOWN_ERROR /com.example.word.utils.TTSErrorHandler.TTSError  error 3com.example.word.utils.TTSErrorHandler.TTSErrorInfo  message 3com.example.word.utils.TTSErrorHandler.TTSErrorInfo  suggestedAction 3com.example.word.utils.TTSErrorHandler.TTSErrorInfo  	Closeable java.io  close java.io.Closeable  InterruptedException 	java.lang  UnsatisfiedLinkError 	java.lang  toString java.lang.StackTraceElement  CompletableDeferred java.lang.StringBuilder  Pair java.lang.StringBuilder  ResourceMonitor java.lang.StringBuilder  activeResources java.lang.StringBuilder  checkResourceLeaks java.lang.StringBuilder  checkTTSAvailability java.lang.StringBuilder  
getMemoryInfo java.lang.StringBuilder  getResourceReport java.lang.StringBuilder  getSystemReport java.lang.StringBuilder  resourceCounter java.lang.StringBuilder  loadLibrary java.lang.System  
currentThread java.lang.Thread  	interrupt java.lang.Thread  
stackTrace java.lang.Thread  TTSErrorHandler 	java.util  analyzeTTSError 	java.util  ConcurrentHashMap java.util.concurrent  
isNotEmpty &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  size &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  
AtomicLong java.util.concurrent.atomic  get &java.util.concurrent.atomic.AtomicLong  incrementAndGet &java.util.concurrent.atomic.AtomicLong  forEach kotlin.Array  MutableCollection kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  Any kotlinx.coroutines  
AtomicLong kotlinx.coroutines  	Closeable kotlinx.coroutines  ConcurrentHashMap kotlinx.coroutines  DisposableHandle kotlinx.coroutines  	FixResult kotlinx.coroutines  Intent kotlinx.coroutines  InterruptedException kotlinx.coroutines  R kotlinx.coroutines  ResourceInfo kotlinx.coroutines  ResourceMonitor kotlinx.coroutines  SystemIssue kotlinx.coroutines  T kotlinx.coroutines  TTSError kotlinx.coroutines  TTSErrorHandler kotlinx.coroutines  TTSErrorInfo kotlinx.coroutines  Thread kotlinx.coroutines  UnsatisfiedLinkError kotlinx.coroutines  activeResources kotlinx.coroutines  analyzeTTSError kotlinx.coroutines  android kotlinx.coroutines  
attemptTTSFix kotlinx.coroutines  checkResourceLeaks kotlinx.coroutines  checkTTSAvailability kotlinx.coroutines  cleanupExpiredResources kotlinx.coroutines  
component1 kotlinx.coroutines  
component2 kotlinx.coroutines  createRecoveryStrategy kotlinx.coroutines  forceGarbageCollection kotlinx.coroutines  
getMemoryInfo kotlinx.coroutines  getResourceReport kotlinx.coroutines  getSystemReport kotlinx.coroutines  joinToString kotlinx.coroutines  listOf kotlinx.coroutines  resourceCounter kotlinx.coroutines  set kotlinx.coroutines  	FixResult !kotlinx.coroutines.CoroutineScope  SystemIssue !kotlinx.coroutines.CoroutineScope  SystemIssueResolver !kotlinx.coroutines.CoroutineScope  TTSErrorHandler !kotlinx.coroutines.CoroutineScope  analyzeTTSError !kotlinx.coroutines.CoroutineScope  
attemptTTSFix !kotlinx.coroutines.CoroutineScope  checkResourceLeaks !kotlinx.coroutines.CoroutineScope  checkTTSAvailability !kotlinx.coroutines.CoroutineScope  cleanupExpiredResources !kotlinx.coroutines.CoroutineScope  createRecoveryStrategy !kotlinx.coroutines.CoroutineScope  detectAndFixIssues !kotlinx.coroutines.CoroutineScope  invokeOnCompletion kotlinx.coroutines.Job  delay okhttp3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         