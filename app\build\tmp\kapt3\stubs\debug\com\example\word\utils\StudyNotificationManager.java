package com.example.word.utils;

/**
 * 通知管理器
 * 负责处理学习提醒通知
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u001e\u0010\u000e\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0006J\u001e\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004J\u0016\u0010\u0014\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0015\u001a\u00020\u0006J\u000e\u0010\u0016\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/example/word/utils/StudyNotificationManager;", "", "()V", "CHANNEL_ID", "", "NOTIFICATION_ID", "", "REQUEST_CODE", "TAG", "cancelStudyReminder", "", "context", "Landroid/content/Context;", "createNotificationChannel", "scheduleStudyReminder", "hour", "minute", "showAchievementNotification", "achievementTitle", "achievementDescription", "showStreakNotification", "streakDays", "showStudyReminderNotification", "app_debug"})
public final class StudyNotificationManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "StudyNotificationManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CHANNEL_ID = "study_reminder";
    private static final int NOTIFICATION_ID = 1001;
    private static final int REQUEST_CODE = 1002;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.StudyNotificationManager INSTANCE = null;
    
    private StudyNotificationManager() {
        super();
    }
    
    /**
     * 创建通知渠道
     */
    public final void createNotificationChannel(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 设置每日提醒
     */
    public final void scheduleStudyReminder(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int hour, int minute) {
    }
    
    /**
     * 取消提醒
     */
    public final void cancelStudyReminder(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 显示学习提醒通知
     */
    public final void showStudyReminderNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * 显示学习成就通知
     */
    public final void showAchievementNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String achievementTitle, @org.jetbrains.annotations.NotNull()
    java.lang.String achievementDescription) {
    }
    
    /**
     * 显示学习连击通知
     */
    public final void showStreakNotification(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int streakDays) {
    }
}