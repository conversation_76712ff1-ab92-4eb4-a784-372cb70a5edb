package com.example.word.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.speech.tts.TextToSpeech
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import java.io.IOException
import java.util.*

/**
 * TTS管理器
 * 支持系统TTS和在线TTS API
 */
class TTSManager(private val context: Context) : TextToSpeech.OnInitListener {
    
    companion object {
        private const val TAG = "TTSManager"
        private const val TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech"
        private const val API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7"
    }
    
    private var systemTTS: TextToSpeech? = null
    private var isSystemTTSReady = false
    private val httpClient = OkHttpClient()
    private var mediaPlayer: MediaPlayer? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var isReleased = false
    
    // TTS状态回调
    var onTTSStatusChanged: ((<PERSON><PERSON><PERSON>, String) -> Unit)? = null
    
    init {
        initializeSystemTTS()
    }
    
    /**
     * 初始化系统TTS
     */
    private fun initializeSystemTTS() {
        try {
            systemTTS = TextToSpeech(context, this)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize system TTS", e)
            onTTSStatusChanged?.invoke(false, "系统TTS初始化失败，将使用在线TTS")
        }
    }
    
    /**
     * 系统TTS初始化回调
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            val result = systemTTS?.setLanguage(Locale.US)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w(TAG, "System TTS language not supported")
                isSystemTTSReady = false
                onTTSStatusChanged?.invoke(false, "系统TTS语言不支持，将使用在线TTS")
            } else {
                isSystemTTSReady = true
                onTTSStatusChanged?.invoke(true, "系统TTS初始化成功")
                Log.i(TAG, "System TTS initialized successfully")
            }
        } else {
            Log.e(TAG, "System TTS initialization failed")
            isSystemTTSReady = false
            onTTSStatusChanged?.invoke(false, "系统TTS初始化失败，将使用在线TTS")
        }
    }
    
    /**
     * 朗读文本
     */
    fun speak(text: String, useSlowSpeed: Boolean = false) {
        if (text.isBlank() || isReleased) {
            Log.w(TAG, "Cannot speak: text is blank or TTS is released")
            return
        }

        try {
            Log.d(TAG, "Speaking text: '$text', useSlowSpeed: $useSlowSpeed")
            Log.d(TAG, "System TTS ready: $isSystemTTSReady")

            if (isSystemTTSReady) {
                Log.d(TAG, "Using system TTS")
                speakWithSystemTTS(text, useSlowSpeed)
            } else {
                Log.d(TAG, "Using online TTS")
                speakWithOnlineTTS(text, useSlowSpeed)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to speak text: $text", e)
            onTTSStatusChanged?.invoke(false, "语音播放失败: ${e.message}")
        }
    }
    
    /**
     * 使用系统TTS朗读
     */
    private fun speakWithSystemTTS(text: String, useSlowSpeed: Boolean) {
        try {
            systemTTS?.let { tts ->
                Log.d(TAG, "System TTS is available, configuring...")

                // 设置语速
                val speechRate = if (useSlowSpeed) 0.7f else 1.0f
                tts.setSpeechRate(speechRate)

                // 设置音调
                tts.setPitch(1.0f)

                // 开始朗读
                val result = tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, "TTS_ID_$text")

                when (result) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "System TTS speak started successfully: $text")
                        onTTSStatusChanged?.invoke(true, "正在播放: $text")
                    }
                    TextToSpeech.ERROR -> {
                        Log.e(TAG, "System TTS speak failed with ERROR")
                        onTTSStatusChanged?.invoke(false, "系统TTS播放失败")
                        // 尝试在线TTS
                        speakWithOnlineTTS(text, useSlowSpeed)
                    }
                    else -> {
                        Log.w(TAG, "System TTS speak returned unexpected result: $result")
                        onTTSStatusChanged?.invoke(false, "系统TTS状态异常")
                    }
                }
            } ?: run {
                Log.e(TAG, "System TTS is null")
                onTTSStatusChanged?.invoke(false, "系统TTS未初始化")
                speakWithOnlineTTS(text, useSlowSpeed)
            }
        } catch (e: Exception) {
            Log.e(TAG, "System TTS speak failed", e)
            onTTSStatusChanged?.invoke(false, "系统TTS异常: ${e.message}")
            // 系统TTS失败时，尝试使用在线TTS
            speakWithOnlineTTS(text, useSlowSpeed)
        }
    }
    
    /**
     * 使用在线TTS朗读
     */
    private fun speakWithOnlineTTS(text: String, useSlowSpeed: Boolean) {
        // 暂时禁用在线TTS，直接使用系统TTS作为备用
        Log.d(TAG, "Online TTS requested for: $text, falling back to system TTS")
        onTTSStatusChanged?.invoke(false, "在线TTS暂不可用，尝试系统TTS")

        // 强制尝试系统TTS，即使之前标记为不可用
        if (systemTTS != null) {
            try {
                val speechRate = if (useSlowSpeed) 0.7f else 1.0f
                systemTTS?.setSpeechRate(speechRate)
                systemTTS?.setPitch(1.0f)

                val result = systemTTS?.speak(text, TextToSpeech.QUEUE_FLUSH, null, "TTS_FALLBACK_$text")

                when (result) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "Fallback system TTS speak started: $text")
                        onTTSStatusChanged?.invoke(true, "使用系统TTS播放: $text")
                    }
                    else -> {
                        Log.e(TAG, "Fallback system TTS also failed")
                        onTTSStatusChanged?.invoke(false, "语音播放不可用")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Fallback system TTS failed", e)
                onTTSStatusChanged?.invoke(false, "语音播放异常: ${e.message}")
            }
        } else {
            Log.e(TAG, "No TTS available")
            onTTSStatusChanged?.invoke(false, "语音功能不可用")
        }
    }

    /**
     * 解析API响应并播放音频
     */
    private suspend fun parseAndPlayAudio(responseBody: String) {
        try {
            // 根据API文档，这里可能需要解析JSON响应
            // 假设API直接返回音频URL或者音频数据

            // 如果响应是JSON格式，解析获取音频URL
            if (responseBody.startsWith("{")) {
                // JSON响应处理
                Log.d(TAG, "Parsing JSON response for audio URL")
                // TODO: 根据实际API响应格式解析
                withContext(Dispatchers.Main) {
                    Log.w(TAG, "JSON parsing not implemented, using fallback")
                }
            } else {
                // 直接是音频数据
                val audioData = responseBody.toByteArray()
                withContext(Dispatchers.Main) {
                    playAudioData(audioData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse audio response", e)
        }
    }

    /**
     * 回退到系统TTS
     */
    private fun fallbackToSystemTTS(text: String, useSlowSpeed: Boolean) {
        if (isSystemTTSReady) {
            speakWithSystemTTS(text, useSlowSpeed)
        } else {
            Log.w(TAG, "Both online and system TTS failed for: $text")
        }
    }
    
    /**
     * 播放音频数据
     */
    private fun playAudioData(audioData: ByteArray) {
        if (isReleased) return

        try {
            // 释放之前的MediaPlayer
            releaseMediaPlayer()

            // 创建临时文件
            val tempFile = java.io.File.createTempFile("tts_audio", ".mp3", context.cacheDir)
            tempFile.writeBytes(audioData)

            // 创建MediaPlayer播放音频
            mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .build()
                )

                setDataSource(tempFile.absolutePath)

                setOnPreparedListener { player ->
                    if (!isReleased) {
                        player.start()
                        Log.d(TAG, "Online TTS audio started playing")
                    }
                }

                setOnCompletionListener { player ->
                    try {
                        player.release()
                        if (tempFile.exists()) {
                            tempFile.delete() // 清理临时文件
                        }
                        Log.d(TAG, "Online TTS audio playback completed")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in completion listener", e)
                    }
                }

                setOnErrorListener { player, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    try {
                        player.release()
                        if (tempFile.exists()) {
                            tempFile.delete()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in error listener", e)
                    }
                    true
                }

                prepareAsync()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to play audio data", e)
        }
    }

    /**
     * 释放MediaPlayer资源
     */
    private fun releaseMediaPlayer() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            }
            mediaPlayer = null
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing MediaPlayer", e)
        }
    }
    
    /**
     * 停止朗读
     */
    fun stop() {
        try {
            systemTTS?.stop()
            releaseMediaPlayer()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop TTS", e)
        }
    }
    
    /**
     * 检查TTS是否可用
     */
    fun isTTSAvailable(): Boolean {
        return isSystemTTSReady || true // 在线TTS总是可用（假设网络正常）
    }
    
    /**
     * 获取TTS状态信息
     */
    fun getTTSStatus(): String {
        return when {
            isSystemTTSReady -> "系统TTS可用"
            else -> "使用在线TTS"
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            isReleased = true
            coroutineScope.cancel()
            systemTTS?.stop()
            systemTTS?.shutdown()
            systemTTS = null
            releaseMediaPlayer()

            // 安全地关闭HTTP客户端
            try {
                httpClient.dispatcher.executorService.shutdown()
                httpClient.connectionPool.evictAll()
            } catch (e: Exception) {
                Log.w(TAG, "Error shutting down HTTP client", e)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to release TTS resources", e)
        }
    }
}
