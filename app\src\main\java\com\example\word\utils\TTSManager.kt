package com.example.word.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.speech.tts.TextToSpeech
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import java.io.IOException
import java.util.*

/**
 * TTS管理器
 * 支持系统TTS和在线TTS API
 */
class TTSManager(private val context: Context) : TextToSpeech.OnInitListener {
    
    companion object {
        private const val TAG = "TTSManager"
        private const val TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech"
        private const val API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7"
    }
    
    private var systemTTS: TextToSpeech? = null
    private var isSystemTTSReady = false
    private val httpClient = OkHttpClient()
    private var mediaPlayer: MediaPlayer? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var isReleased = false
    
    // TTS状态回调
    var onTTSStatusChanged: ((<PERSON><PERSON><PERSON>, String) -> Unit)? = null
    
    init {
        initializeSystemTTS()
    }
    
    /**
     * 初始化系统TTS
     */
    private fun initializeSystemTTS() {
        try {
            systemTTS = TextToSpeech(context, this)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize system TTS", e)
            onTTSStatusChanged?.invoke(false, "系统TTS初始化失败，将使用在线TTS")
        }
    }
    
    /**
     * 系统TTS初始化回调
     */
    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            val result = systemTTS?.setLanguage(Locale.US)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w(TAG, "System TTS language not supported")
                isSystemTTSReady = false
                onTTSStatusChanged?.invoke(false, "系统TTS语言不支持，将使用在线TTS")
            } else {
                isSystemTTSReady = true
                onTTSStatusChanged?.invoke(true, "系统TTS初始化成功")
                Log.i(TAG, "System TTS initialized successfully")
            }
        } else {
            Log.e(TAG, "System TTS initialization failed")
            isSystemTTSReady = false
            onTTSStatusChanged?.invoke(false, "系统TTS初始化失败，将使用在线TTS")
        }
    }
    
    /**
     * 朗读文本
     */
    fun speak(text: String, useSlowSpeed: Boolean = false) {
        if (text.isBlank() || isReleased) return

        try {
            if (isSystemTTSReady) {
                speakWithSystemTTS(text, useSlowSpeed)
            } else {
                speakWithOnlineTTS(text, useSlowSpeed)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to speak text: $text", e)
        }
    }
    
    /**
     * 使用系统TTS朗读
     */
    private fun speakWithSystemTTS(text: String, useSlowSpeed: Boolean) {
        try {
            systemTTS?.let { tts ->
                // 设置语速
                val speechRate = if (useSlowSpeed) 0.7f else 1.0f
                tts.setSpeechRate(speechRate)
                
                // 开始朗读
                tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, null)
                Log.d(TAG, "Speaking with system TTS: $text")
            }
        } catch (e: Exception) {
            Log.e(TAG, "System TTS speak failed", e)
            // 系统TTS失败时，尝试使用在线TTS
            speakWithOnlineTTS(text, useSlowSpeed)
        }
    }
    
    /**
     * 使用在线TTS朗读
     */
    private fun speakWithOnlineTTS(text: String, useSlowSpeed: Boolean) {
        coroutineScope.launch {
            try {
                Log.d(TAG, "Using online TTS for: $text")

                // 构建请求URL - 根据API文档调整参数
                val urlBuilder = TTS_API_URL.toHttpUrl().newBuilder()
                urlBuilder.addQueryParameter("key", API_KEY)
                urlBuilder.addQueryParameter("text", text)
                urlBuilder.addQueryParameter("lang", "en") // 语言设置

                if (useSlowSpeed) {
                    urlBuilder.addQueryParameter("speed", "slow") // 语速设置
                }

                val request = Request.Builder()
                    .url(urlBuilder.build())
                    .addHeader("User-Agent", "WordLearningApp/1.0")
                    .get()
                    .build()

                // 在IO线程执行网络请求
                withContext(Dispatchers.IO) {
                    httpClient.newCall(request).execute().use { response ->
                        if (response.isSuccessful) {
                            val responseBody = response.body?.string()
                            Log.d(TAG, "Online TTS response: $responseBody")

                            // 如果API返回的是JSON格式，需要解析获取音频URL
                            if (responseBody != null) {
                                parseAndPlayAudio(responseBody)
                            } else {
                                Log.e(TAG, "No response body from online TTS")
                                // 回退到系统TTS
                                withContext(Dispatchers.Main) {
                                    fallbackToSystemTTS(text, useSlowSpeed)
                                }
                            }
                        } else {
                            Log.e(TAG, "Online TTS request failed: ${response.code} - ${response.message}")
                            // 回退到系统TTS
                            withContext(Dispatchers.Main) {
                                fallbackToSystemTTS(text, useSlowSpeed)
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Online TTS failed", e)
                // 回退到系统TTS
                fallbackToSystemTTS(text, useSlowSpeed)
            }
        }
    }

    /**
     * 解析API响应并播放音频
     */
    private suspend fun parseAndPlayAudio(responseBody: String) {
        try {
            // 根据API文档，这里可能需要解析JSON响应
            // 假设API直接返回音频URL或者音频数据

            // 如果响应是JSON格式，解析获取音频URL
            if (responseBody.startsWith("{")) {
                // JSON响应处理
                Log.d(TAG, "Parsing JSON response for audio URL")
                // TODO: 根据实际API响应格式解析
                withContext(Dispatchers.Main) {
                    Log.w(TAG, "JSON parsing not implemented, using fallback")
                }
            } else {
                // 直接是音频数据
                val audioData = responseBody.toByteArray()
                withContext(Dispatchers.Main) {
                    playAudioData(audioData)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to parse audio response", e)
        }
    }

    /**
     * 回退到系统TTS
     */
    private fun fallbackToSystemTTS(text: String, useSlowSpeed: Boolean) {
        if (isSystemTTSReady) {
            speakWithSystemTTS(text, useSlowSpeed)
        } else {
            Log.w(TAG, "Both online and system TTS failed for: $text")
        }
    }
    
    /**
     * 播放音频数据
     */
    private fun playAudioData(audioData: ByteArray) {
        if (isReleased) return

        try {
            // 释放之前的MediaPlayer
            releaseMediaPlayer()

            // 创建临时文件
            val tempFile = java.io.File.createTempFile("tts_audio", ".mp3", context.cacheDir)
            tempFile.writeBytes(audioData)

            // 创建MediaPlayer播放音频
            mediaPlayer = MediaPlayer().apply {
                setAudioAttributes(
                    AudioAttributes.Builder()
                        .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                        .setUsage(AudioAttributes.USAGE_MEDIA)
                        .build()
                )

                setDataSource(tempFile.absolutePath)

                setOnPreparedListener { player ->
                    if (!isReleased) {
                        player.start()
                        Log.d(TAG, "Online TTS audio started playing")
                    }
                }

                setOnCompletionListener { player ->
                    try {
                        player.release()
                        if (tempFile.exists()) {
                            tempFile.delete() // 清理临时文件
                        }
                        Log.d(TAG, "Online TTS audio playback completed")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in completion listener", e)
                    }
                }

                setOnErrorListener { player, what, extra ->
                    Log.e(TAG, "MediaPlayer error: what=$what, extra=$extra")
                    try {
                        player.release()
                        if (tempFile.exists()) {
                            tempFile.delete()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in error listener", e)
                    }
                    true
                }

                prepareAsync()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to play audio data", e)
        }
    }

    /**
     * 释放MediaPlayer资源
     */
    private fun releaseMediaPlayer() {
        try {
            mediaPlayer?.let { player ->
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            }
            mediaPlayer = null
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing MediaPlayer", e)
        }
    }
    
    /**
     * 停止朗读
     */
    fun stop() {
        try {
            systemTTS?.stop()
            releaseMediaPlayer()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop TTS", e)
        }
    }
    
    /**
     * 检查TTS是否可用
     */
    fun isTTSAvailable(): Boolean {
        return isSystemTTSReady || true // 在线TTS总是可用（假设网络正常）
    }
    
    /**
     * 获取TTS状态信息
     */
    fun getTTSStatus(): String {
        return when {
            isSystemTTSReady -> "系统TTS可用"
            else -> "使用在线TTS"
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            isReleased = true
            coroutineScope.cancel()
            systemTTS?.stop()
            systemTTS?.shutdown()
            systemTTS = null
            releaseMediaPlayer()

            // 安全地关闭HTTP客户端
            try {
                httpClient.dispatcher.executorService.shutdown()
                httpClient.connectionPool.evictAll()
            } catch (e: Exception) {
                Log.w(TAG, "Error shutting down HTTP client", e)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to release TTS resources", e)
        }
    }
}
