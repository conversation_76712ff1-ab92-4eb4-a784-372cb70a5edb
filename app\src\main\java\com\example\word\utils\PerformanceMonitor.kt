package com.example.word.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 性能监控器
 * 监控应用性能并自动优化
 */
object PerformanceMonitor {
    
    private const val TAG = "PerformanceMonitor"
    private val isMonitoring = AtomicBoolean(false)
    private var monitoringJob: Job? = null
    
    /**
     * 开始性能监控
     */
    fun startMonitoring(context: Context) {
        if (isMonitoring.getAndSet(true)) {
            Log.d(TAG, "Performance monitoring already started")
            return
        }
        
        Log.d(TAG, "Starting performance monitoring...")
        
        monitoringJob = CoroutineScope(Dispatchers.Default).launch {
            try {
                while (isActive && isMonitoring.get()) {
                    // 监控内存使用
                    monitorMemoryUsage()

                    // 监控主线程状态
                    monitorMainThread()

                    // 检查资源泄漏
                    checkResourceLeaks()

                    // 每5秒检查一次
                    delay(5000)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in performance monitoring", e)
            }
        }
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        if (!isMonitoring.getAndSet(false)) {
            Log.d(TAG, "Performance monitoring already stopped")
            return
        }
        
        Log.d(TAG, "Stopping performance monitoring...")
        monitoringJob?.cancel()
        monitoringJob = null
    }
    
    /**
     * 监控内存使用
     */
    private fun monitorMemoryUsage() {
        try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()
            
            val usedPercentage = (usedMemory * 100) / maxMemory
            
            if (usedPercentage > 80) {
                Log.w(TAG, "High memory usage: ${usedPercentage}% (${usedMemory / 1024 / 1024}MB)")
                
                // 建议垃圾回收
                System.gc()
                Log.d(TAG, "Suggested garbage collection")
            } else {
                Log.d(TAG, "Memory usage: ${usedPercentage}% (${usedMemory / 1024 / 1024}MB)")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error monitoring memory", e)
        }
    }
    
    /**
     * 监控主线程状态
     */
    private fun monitorMainThread() {
        try {
            val startTime = System.currentTimeMillis()

            Handler(Looper.getMainLooper()).post {
                val endTime = System.currentTimeMillis()
                val delay = endTime - startTime

                if (delay > 100) {
                    Log.w(TAG, "Main thread blocked for ${delay}ms")
                } else {
                    Log.d(TAG, "Main thread responsive (${delay}ms)")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error monitoring main thread", e)
        }
    }

    /**
     * 检查资源泄漏
     */
    private fun checkResourceLeaks() {
        try {
            val leakedResources = ResourceMonitor.checkResourceLeaks()
            if (leakedResources.isNotEmpty()) {
                Log.w(TAG, "Detected ${leakedResources.size} potential resource leaks")

                // 如果泄漏严重，强制垃圾回收
                if (leakedResources.size > 10) {
                    Log.w(TAG, "High number of resource leaks detected, forcing GC")
                    ResourceMonitor.forceGarbageCollection()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking resource leaks", e)
        }
    }
    
    /**
     * 执行性能优化操作
     */
    fun performOptimizations() {
        Log.d(TAG, "Performing optimizations...")
        
        try {
            // 清理内存
            System.gc()
            
            // 清理临时文件（如果有的话）
            // cleanTempFiles()
            
            Log.d(TAG, "Optimizations completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error performing optimizations", e)
        }
    }
    
    /**
     * 获取性能报告
     */
    fun getPerformanceReport(): PerformanceReport {
        return try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()
            
            PerformanceReport(
                usedMemoryMB = usedMemory / 1024 / 1024,
                totalMemoryMB = totalMemory / 1024 / 1024,
                maxMemoryMB = maxMemory / 1024 / 1024,
                memoryUsagePercentage = (usedMemory * 100) / maxMemory,
                isMonitoring = isMonitoring.get()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting performance report", e)
            PerformanceReport(0, 0, 0, 0, false)
        }
    }
    
    /**
     * 安全执行UI操作
     */
    fun safeUIOperation(operation: () -> Unit) {
        try {
            if (Looper.myLooper() == Looper.getMainLooper()) {
                // 已在主线程
                operation()
            } else {
                // 切换到主线程
                Handler(Looper.getMainLooper()).post {
                    try {
                        operation()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in UI operation", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in safe UI operation", e)
        }
    }
    
    /**
     * 安全执行后台操作
     */
    fun safeBackgroundOperation(operation: suspend () -> Unit) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                operation()
            } catch (e: Exception) {
                Log.e(TAG, "Error in background operation", e)
            }
        }
    }
    
    /**
     * 性能报告数据类
     */
    data class PerformanceReport(
        val usedMemoryMB: Long,
        val totalMemoryMB: Long,
        val maxMemoryMB: Long,
        val memoryUsagePercentage: Long,
        val isMonitoring: Boolean
    ) {
        override fun toString(): String {
            return "Performance Report:\n" +
                    "Memory: ${usedMemoryMB}MB / ${maxMemoryMB}MB (${memoryUsagePercentage}%)\n" +
                    "Monitoring: $isMonitoring"
        }
    }
}
