package com.example.word.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log

/**
 * 应用性能优化器
 * 减少重复操作，提升性能
 */
object AppOptimizer {
    
    private const val TAG = "AppOptimizer"
    private const val PREFS_NAME = "app_optimizer"
    
    // 优化标志
    private const val KEY_HEALTH_CHECK_DONE = "health_check_done"
    private const val KEY_TTS_INIT_DONE = "tts_init_done"
    private const val KEY_DATABASE_CHECK_DONE = "database_check_done"
    private const val KEY_LAST_CHECK_TIME = "last_check_time"
    
    // 检查间隔（毫秒）
    private const val CHECK_INTERVAL = 24 * 60 * 60 * 1000L // 24小时
    
    /**
     * 检查是否需要执行健康检查
     */
    fun shouldPerformHealthCheck(context: Context): Boolean {
        val prefs = getPrefs(context)
        val lastCheckTime = prefs.getLong(KEY_LAST_CHECK_TIME, 0)
        val currentTime = System.currentTimeMillis()
        
        val shouldCheck = (currentTime - lastCheckTime) > CHECK_INTERVAL
        
        Log.d(TAG, "Health check needed: $shouldCheck (last: ${lastCheckTime}, current: ${currentTime})")
        return shouldCheck
    }
    
    /**
     * 标记健康检查已完成
     */
    fun markHealthCheckDone(context: Context) {
        val prefs = getPrefs(context)
        prefs.edit()
            .putBoolean(KEY_HEALTH_CHECK_DONE, true)
            .putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis())
            .apply()
        
        Log.d(TAG, "Health check marked as done")
    }
    
    /**
     * 检查是否需要初始化TTS
     */
    fun shouldInitializeTTS(context: Context): Boolean {
        val prefs = getPrefs(context)
        val shouldInit = !prefs.getBoolean(KEY_TTS_INIT_DONE, false)
        
        Log.d(TAG, "TTS initialization needed: $shouldInit")
        return shouldInit
    }
    
    /**
     * 标记TTS初始化已完成
     */
    fun markTTSInitDone(context: Context) {
        val prefs = getPrefs(context)
        prefs.edit()
            .putBoolean(KEY_TTS_INIT_DONE, true)
            .apply()
        
        Log.d(TAG, "TTS initialization marked as done")
    }
    
    /**
     * 检查是否需要执行数据库检查
     */
    fun shouldPerformDatabaseCheck(context: Context): Boolean {
        val prefs = getPrefs(context)
        val shouldCheck = !prefs.getBoolean(KEY_DATABASE_CHECK_DONE, false)
        
        Log.d(TAG, "Database check needed: $shouldCheck")
        return shouldCheck
    }
    
    /**
     * 标记数据库检查已完成
     */
    fun markDatabaseCheckDone(context: Context) {
        val prefs = getPrefs(context)
        prefs.edit()
            .putBoolean(KEY_DATABASE_CHECK_DONE, true)
            .apply()
        
        Log.d(TAG, "Database check marked as done")
    }
    
    /**
     * 重置所有优化标志（用于调试或强制重新检查）
     */
    fun resetOptimizationFlags(context: Context) {
        val prefs = getPrefs(context)
        prefs.edit().clear().apply()
        
        Log.d(TAG, "All optimization flags reset")
    }
    
    /**
     * 获取优化状态摘要
     */
    fun getOptimizationSummary(context: Context): String {
        val prefs = getPrefs(context)
        
        return buildString {
            appendLine("=== 应用优化状态 ===")
            appendLine("健康检查完成: ${prefs.getBoolean(KEY_HEALTH_CHECK_DONE, false)}")
            appendLine("TTS初始化完成: ${prefs.getBoolean(KEY_TTS_INIT_DONE, false)}")
            appendLine("数据库检查完成: ${prefs.getBoolean(KEY_DATABASE_CHECK_DONE, false)}")
            
            val lastCheckTime = prefs.getLong(KEY_LAST_CHECK_TIME, 0)
            if (lastCheckTime > 0) {
                val timeAgo = (System.currentTimeMillis() - lastCheckTime) / 1000 / 60 // 分钟
                appendLine("上次检查: ${timeAgo}分钟前")
            } else {
                appendLine("上次检查: 从未")
            }
        }
    }
    
    /**
     * 获取SharedPreferences
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 应用启动优化
     */
    fun optimizeAppStartup(context: Context): StartupOptimizationResult {
        Log.d(TAG, "Performing startup optimization...")
        
        val needsHealthCheck = shouldPerformHealthCheck(context)
        val needsTTSInit = shouldInitializeTTS(context)
        val needsDatabaseCheck = shouldPerformDatabaseCheck(context)
        
        val optimizations = mutableListOf<String>()
        
        if (!needsHealthCheck) {
            optimizations.add("跳过健康检查（24小时内已检查）")
        }
        
        if (!needsTTSInit) {
            optimizations.add("跳过TTS初始化（已完成）")
        }
        
        if (!needsDatabaseCheck) {
            optimizations.add("跳过数据库检查（已完成）")
        }
        
        val result = StartupOptimizationResult(
            needsHealthCheck = needsHealthCheck,
            needsTTSInit = needsTTSInit,
            needsDatabaseCheck = needsDatabaseCheck,
            optimizations = optimizations
        )
        
        Log.d(TAG, "Startup optimization result: $result")
        return result
    }
    
    /**
     * 启动优化结果
     */
    data class StartupOptimizationResult(
        val needsHealthCheck: Boolean,
        val needsTTSInit: Boolean,
        val needsDatabaseCheck: Boolean,
        val optimizations: List<String>
    ) {
        val hasOptimizations: Boolean
            get() = optimizations.isNotEmpty()
            
        val summary: String
            get() = if (hasOptimizations) {
                "已优化: ${optimizations.joinToString(", ")}"
            } else {
                "需要执行完整初始化"
            }
    }
}
