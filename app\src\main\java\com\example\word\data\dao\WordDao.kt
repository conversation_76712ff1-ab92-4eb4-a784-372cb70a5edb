package com.example.word.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.word.data.entities.Word

/**
 * 单词数据访问对象
 */
@Dao
interface WordDao {
    
    // 获取所有单词
    @Query("SELECT * FROM words ORDER BY frequencyRank ASC")
    fun getAllWords(): LiveData<List<Word>>
    
    // 根据ID获取单词
    @Query("SELECT * FROM words WHERE id = :id")
    suspend fun getWordById(id: Long): Word?
    
    // 搜索单词（支持英文和中文）
    @Query("SELECT * FROM words WHERE word LIKE '%' || :query || '%' OR translation LIKE '%' || :query || '%' ORDER BY frequencyRank ASC")
    fun searchWords(query: String): LiveData<List<Word>>
    
    // 根据难度等级获取单词
    @Query("SELECT * FROM words WHERE difficultyLevel = :level ORDER BY frequencyRank ASC")
    fun getWordsByDifficulty(level: Int): LiveData<List<Word>>
    
    // 根据词性获取单词
    @Query("SELECT * FROM words WHERE partOfSpeech = :pos ORDER BY frequencyRank ASC")
    fun getWordsByPartOfSpeech(pos: String): LiveData<List<Word>>
    
    // 获取收藏的单词
    @Query("SELECT * FROM words WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC")
    fun getBookmarkedWords(): LiveData<List<Word>>
    
    // 获取需要复习的单词
    @Query("SELECT * FROM words WHERE nextReviewTime <= :currentTime AND studyCount > 0 ORDER BY nextReviewTime ASC")
    fun getWordsForReview(currentTime: Long): LiveData<List<Word>>
    
    // 获取随机单词（用于测验）
    @Query("SELECT * FROM words ORDER BY RANDOM() LIMIT :count")
    suspend fun getRandomWords(count: Int): List<Word>
    
    // 根据频率排名获取单词
    @Query("SELECT * FROM words WHERE frequencyRank BETWEEN :startRank AND :endRank ORDER BY frequencyRank ASC")
    fun getWordsByFrequencyRange(startRank: Int, endRank: Int): LiveData<List<Word>>
    
    // 获取学习进度统计
    @Query("SELECT COUNT(*) FROM words WHERE studyCount > 0")
    suspend fun getStudiedWordsCount(): Int
    
    @Query("SELECT COUNT(*) FROM words WHERE isBookmarked = 1")
    suspend fun getBookmarkedWordsCount(): Int
    
    @Query("SELECT AVG(accuracyRate) FROM words WHERE studyCount > 0")
    suspend fun getAverageAccuracy(): Float

    // 获取总单词数
    @Query("SELECT COUNT(*) FROM words")
    suspend fun getWordCount(): Int
    
    // 插入单词
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWord(word: Word): Long
    
    // 批量插入单词
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWords(words: List<Word>)
    
    // 更新单词
    @Update
    suspend fun updateWord(word: Word)
    
    // 更新收藏状态
    @Query("UPDATE words SET isBookmarked = :isBookmarked WHERE id = :id")
    suspend fun updateBookmarkStatus(id: Long, isBookmarked: Boolean)
    
    // 更新学习统计
    @Query("UPDATE words SET studyCount = studyCount + 1, lastStudiedTime = :time, accuracyRate = :accuracy WHERE id = :id")
    suspend fun updateStudyStats(id: Long, time: Long, accuracy: Int)
    
    // 更新复习信息
    @Query("UPDATE words SET nextReviewTime = :nextTime, reviewInterval = :interval, memoryStrength = :strength WHERE id = :id")
    suspend fun updateReviewInfo(id: Long, nextTime: Long, interval: Int, strength: Float)
    
    // 删除单词
    @Delete
    suspend fun deleteWord(word: Word)
    
    // 清空所有单词
    @Query("DELETE FROM words")
    suspend fun deleteAllWords()
}
