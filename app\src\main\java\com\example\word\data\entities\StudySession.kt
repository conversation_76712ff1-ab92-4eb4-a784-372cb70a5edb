package com.example.word.data.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 学习记录实体类
 * 记录用户的学习历史和进度
 */
@Entity(tableName = "study_sessions")
data class StudySession(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // 学习日期
    val studyDate: Long,
    
    // 学习类型（vocabulary: 词汇, phrase: 短语, quiz: 测验, essay: 作文）
    val studyType: String,
    
    // 学习的单词/短语ID
    val itemId: Long,
    
    // 学习结果（correct: 正确, incorrect: 错误, skipped: 跳过）
    val result: String,
    
    // 反应时间（毫秒）
    val responseTime: Long,
    
    // 学习模式（study: 学习模式, review: 复习模式, test: 测试模式）
    val studyMode: String
)


