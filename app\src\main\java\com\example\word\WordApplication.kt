package com.example.word

import android.app.Application
import android.util.Log
import com.example.word.utils.StudyNotificationManager
import com.example.word.utils.ErrorHandler

/**
 * 应用程序类
 * 处理全局初始化和异常处理
 */
class WordApplication : Application() {
    
    companion object {
        private const val TAG = "WordApplication"
    }
    
    override fun onCreate() {
        super.onCreate()

        try {
            Log.d(TAG, "Application starting")

            // 初始化错误处理器
            ErrorHandler.initialize(this)

            // 创建通知渠道
            StudyNotificationManager.createNotificationChannel(this)

            // 检查崩溃统计
            val crashStats = ErrorHandler.getCrashStats(this)
            if (crashStats.crashCount > 0) {
                Log.w(TAG, "Application has crashed ${crashStats.crashCount} times. Last crash: ${crashStats.lastCrashTime}")
            }

            Log.d(TAG, "Application initialized successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error during application initialization", e)
            ErrorHandler.logError(this, TAG, "Application initialization failed", e)
        }
    }
    

}
