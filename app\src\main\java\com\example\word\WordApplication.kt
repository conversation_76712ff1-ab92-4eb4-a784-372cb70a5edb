package com.example.word

import android.app.Application
import android.util.Log
import com.example.word.utils.StudyNotificationManager

/**
 * 应用程序类
 * 处理全局初始化和异常处理
 */
class WordApplication : Application() {
    
    companion object {
        private const val TAG = "WordApplication"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        try {
            Log.d(TAG, "Application starting")
            
            // 设置全局异常处理器
            setupGlobalExceptionHandler()
            
            // 创建通知渠道
            StudyNotificationManager.createNotificationChannel(this)
            
            Log.d(TAG, "Application initialized successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during application initialization", e)
        }
    }
    
    /**
     * 设置全局异常处理器
     */
    private fun setupGlobalExceptionHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            try {
                Log.e(TAG, "Uncaught exception in thread ${thread.name}", exception)
                
                // 记录崩溃信息
                logCrashInfo(exception)
                
                // 调用默认处理器
                defaultHandler?.uncaughtException(thread, exception)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in exception handler", e)
                // 如果异常处理器本身出错，调用默认处理器
                defaultHandler?.uncaughtException(thread, exception)
            }
        }
    }
    
    /**
     * 记录崩溃信息
     */
    private fun logCrashInfo(exception: Throwable) {
        try {
            val crashInfo = buildString {
                appendLine("=== CRASH REPORT ===")
                appendLine("Time: ${System.currentTimeMillis()}")
                appendLine("Exception: ${exception.javaClass.simpleName}")
                appendLine("Message: ${exception.message}")
                appendLine("Stack trace:")
                exception.stackTrace.forEach { element ->
                    appendLine("  at $element")
                }
                if (exception.cause != null) {
                    appendLine("Caused by: ${exception.cause}")
                }
                appendLine("=== END CRASH REPORT ===")
            }
            
            Log.e(TAG, crashInfo)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to log crash info", e)
        }
    }
}
