<EMAIL>'com.example.word.ui.essay.EssayFragment)com.example.word.ui.phrases.PhraseAdapter:com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder<com.example.word.ui.phrases.PhraseAdapter.PhraseDiffCallback+com.example.word.ui.phrases.PhrasesFragment-com.example.word.ui.progress.ProgressFragment%com.example.word.ui.quiz.QuizFragment-com.example.word.ui.viewmodel.PhraseViewModel+com.example.word.ui.viewmodel.WordViewModel1com.example.word.ui.vocabulary.VocabularyFragment*com.example.word.ui.vocabulary.WordAdapter9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder;com.example.word.ui.vocabulary.WordAdapter.WordDiffCallback.com.example.word.databinding.ItemPhraseBinding,com.example.word.databinding.ItemWordBinding.com.example.word.ui.essay.EssayTemplateAdapterAcom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolderCcom.example.word.ui.essay.EssayTemplateAdapter.TemplateDiffCallback(com.example.word.ui.essay.EssayViewModel/com.example.word.ui.progress.AchievementAdapterEcom.example.word.ui.progress.AchievementAdapter.AchievementViewHolderGcom.example.word.ui.progress.AchievementAdapter.AchievementDiffCallback.com.example.word.ui.progress.ProgressViewModel&com.example.word.ui.quiz.QuizViewModel"com.example.word.ui.quiz.QuizState%com.example.word.ui.quiz.QuestionType#com.example.word.ui.quiz.QuizSource1com.example.word.databinding.FragmentEssayBinding0com.example.word.databinding.FragmentQuizBinding4com.example.word.databinding.FragmentProgressBinding5com.example.word.databinding.ItemEssayTemplateBinding3com.example.word.databinding.ItemAchievementBinding2com.example.word.data.entities.AchievementCategory-com.example.word.data.entities.ExperienceType-com.example.word.ui.settings.SettingsFragment)com.example.word.utils.StudyProgressChart,com.example.word.utils.CircularProgressChart,com.example.word.utils.StudyReminderReceiver&com.example.word.utils.DifficultyLevel4com.example.word.databinding.FragmentSettingsBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  