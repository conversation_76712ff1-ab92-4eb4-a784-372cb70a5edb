package com.example.word.data.organizer;

/**
 * 数据整理器
 * 将1.txt的内容整理成结构化的数据
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\u0015B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0002J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bJ\u0012\u0010\f\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000e\u001a\u00020\u0004H\u0002J\u001a\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u0004H\u0002J\u0012\u0010\u0012\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000e\u001a\u00020\u0004H\u0002J\u0012\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u000e\u001a\u00020\u0004H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/word/data/organizer/DataOrganizer;", "", "()V", "TAG", "", "calculateDifficultyFromFrequency", "", "frequency", "organizeAllData", "Lcom/example/word/data/organizer/DataOrganizer$OrganizedData;", "context", "Landroid/content/Context;", "parseCET4Word", "Lcom/example/word/data/entities/Word;", "line", "parseEssayTemplate", "Lcom/example/word/data/entities/EssayTemplate;", "section", "parseHighFreqWord", "parsePhrase", "Lcom/example/word/data/entities/Phrase;", "OrganizedData", "app_debug"})
public final class DataOrganizer {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DataOrganizer";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.data.organizer.DataOrganizer INSTANCE = null;
    
    private DataOrganizer() {
        super();
    }
    
    /**
     * 整理1.txt中的所有数据
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.word.data.organizer.DataOrganizer.OrganizedData organizeAllData(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 解析高频词汇
     * 格式: "1. available (414 次)a.可利用的，可得到"
     */
    private final com.example.word.data.entities.Word parseHighFreqWord(java.lang.String line) {
        return null;
    }
    
    /**
     * 解析CET-4词汇
     * 格式: "1. evaluate [ɪˈvæljueɪt]（考频 8 次）v.估值；评价"
     */
    private final com.example.word.data.entities.Word parseCET4Word(java.lang.String line) {
        return null;
    }
    
    /**
     * 解析短语
     * 格式: "a series of 一系列,一连串"
     */
    private final com.example.word.data.entities.Phrase parsePhrase(java.lang.String line) {
        return null;
    }
    
    /**
     * 解析作文模板
     */
    private final com.example.word.data.entities.EssayTemplate parseEssayTemplate(java.lang.String line, java.lang.String section) {
        return null;
    }
    
    /**
     * 根据频率计算难度等级
     */
    private final int calculateDifficultyFromFrequency(int frequency) {
        return 0;
    }
    
    /**
     * 整理后的数据
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\u0002\u0010\tJ\u000f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J9\u0010\u0011\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000b\u00a8\u0006\u0019"}, d2 = {"Lcom/example/word/data/organizer/DataOrganizer$OrganizedData;", "", "words", "", "Lcom/example/word/data/entities/Word;", "phrases", "Lcom/example/word/data/entities/Phrase;", "essayTemplates", "Lcom/example/word/data/entities/EssayTemplate;", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getEssayTemplates", "()Ljava/util/List;", "getPhrases", "getWords", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class OrganizedData {
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.word.data.entities.Word> words = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.word.data.entities.Phrase> phrases = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.example.word.data.entities.EssayTemplate> essayTemplates = null;
        
        public OrganizedData(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.Word> words, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.Phrase> phrases, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.EssayTemplate> essayTemplates) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.Word> getWords() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.Phrase> getPhrases() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.EssayTemplate> getEssayTemplates() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.Word> component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.Phrase> component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.example.word.data.entities.EssayTemplate> component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.data.organizer.DataOrganizer.OrganizedData copy(@org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.Word> words, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.Phrase> phrases, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.word.data.entities.EssayTemplate> essayTemplates) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}