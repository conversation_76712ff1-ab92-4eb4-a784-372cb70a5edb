  Application android.app  ActivityMainBinding android.app.Activity  Bundle android.app.Activity  Context android.content  ActivityMainBinding android.content.Context  Bundle android.content.Context  ActivityMainBinding android.content.ContextWrapper  Bundle android.content.ContextWrapper  Bundle 
android.os  LayoutInflater android.view  View android.view  	ViewGroup android.view  ActivityMainBinding  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Toast android.widget  ActivityMainBinding #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Fragment androidx.fragment.app  
viewModels androidx.fragment.app  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  FragmentEssayBinding androidx.fragment.app.Fragment  FragmentPhrasesBinding androidx.fragment.app.Fragment  FragmentProgressBinding androidx.fragment.app.Fragment  FragmentQuizBinding androidx.fragment.app.Fragment  FragmentVocabularyBinding androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  
PhraseAdapter androidx.fragment.app.Fragment  PhraseViewModel androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  WordAdapter androidx.fragment.app.Fragment  
WordViewModel androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  
viewModels androidx.fragment.app.Fragment  ActivityMainBinding &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  Int #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  Long #androidx.lifecycle.AndroidViewModel  MutableLiveData #androidx.lifecycle.AndroidViewModel  Phrase #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Word #androidx.lifecycle.AndroidViewModel  WordRepository #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  Phrase androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Word androidx.lifecycle.ViewModel  WordRepository androidx.lifecycle.ViewModel  NavHostFragment androidx.navigation.fragment  setupWithNavController androidx.navigation.ui  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Phrase 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Word 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Boolean (androidx.recyclerview.widget.ListAdapter  DiffUtil (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  ItemPhraseBinding (androidx.recyclerview.widget.ListAdapter  ItemWordBinding (androidx.recyclerview.widget.ListAdapter  Phrase (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  Word (androidx.recyclerview.widget.ListAdapter  
ViewHolder )androidx.recyclerview.widget.RecyclerView  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  DiffUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemPhraseBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemWordBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  Phrase 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  Word 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemPhraseBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemWordBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Phrase 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Word 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Callback androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  EssayTemplateDao androidx.room.RoomDatabase  	PhraseDao androidx.room.RoomDatabase  RoomDatabase androidx.room.RoomDatabase  StudySessionDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  UserProgressDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  WordDao androidx.room.RoomDatabase  WordDatabase androidx.room.RoomDatabase  EssayTemplateDao #androidx.room.RoomDatabase.Callback  	PhraseDao #androidx.room.RoomDatabase.Callback  SupportSQLiteDatabase #androidx.room.RoomDatabase.Callback  WordDao #androidx.room.RoomDatabase.Callback  WordDatabase #androidx.room.RoomDatabase.Callback  SupportSQLiteDatabase androidx.sqlite.db  MainActivity com.example.word  R com.example.word  ActivityMainBinding com.example.word.MainActivity  Bundle com.example.word.MainActivity  Boolean com.example.word.data.dao  Dao com.example.word.data.dao  Delete com.example.word.data.dao  
EssayTemplate com.example.word.data.dao  EssayTemplateDao com.example.word.data.dao  Float com.example.word.data.dao  Insert com.example.word.data.dao  Int com.example.word.data.dao  List com.example.word.data.dao  Long com.example.word.data.dao  OnConflictStrategy com.example.word.data.dao  Phrase com.example.word.data.dao  	PhraseDao com.example.word.data.dao  Query com.example.word.data.dao  String com.example.word.data.dao  StudySession com.example.word.data.dao  StudySessionDao com.example.word.data.dao  Update com.example.word.data.dao  UserProgress com.example.word.data.dao  UserProgressDao com.example.word.data.dao  Volatile com.example.word.data.dao  Word com.example.word.data.dao  WordDao com.example.word.data.dao  Boolean *com.example.word.data.dao.EssayTemplateDao  Delete *com.example.word.data.dao.EssayTemplateDao  
EssayTemplate *com.example.word.data.dao.EssayTemplateDao  Insert *com.example.word.data.dao.EssayTemplateDao  Int *com.example.word.data.dao.EssayTemplateDao  List *com.example.word.data.dao.EssayTemplateDao  LiveData *com.example.word.data.dao.EssayTemplateDao  Long *com.example.word.data.dao.EssayTemplateDao  OnConflictStrategy *com.example.word.data.dao.EssayTemplateDao  Query *com.example.word.data.dao.EssayTemplateDao  String *com.example.word.data.dao.EssayTemplateDao  Update *com.example.word.data.dao.EssayTemplateDao  incrementUsageCount *com.example.word.data.dao.EssayTemplateDao  updateBookmarkStatus *com.example.word.data.dao.EssayTemplateDao  updateTemplate *com.example.word.data.dao.EssayTemplateDao  Boolean #com.example.word.data.dao.PhraseDao  Delete #com.example.word.data.dao.PhraseDao  Insert #com.example.word.data.dao.PhraseDao  Int #com.example.word.data.dao.PhraseDao  List #com.example.word.data.dao.PhraseDao  LiveData #com.example.word.data.dao.PhraseDao  Long #com.example.word.data.dao.PhraseDao  OnConflictStrategy #com.example.word.data.dao.PhraseDao  Phrase #com.example.word.data.dao.PhraseDao  Query #com.example.word.data.dao.PhraseDao  String #com.example.word.data.dao.PhraseDao  Update #com.example.word.data.dao.PhraseDao  updateBookmarkStatus #com.example.word.data.dao.PhraseDao  updatePhrase #com.example.word.data.dao.PhraseDao  updateStudyStats #com.example.word.data.dao.PhraseDao  Float )com.example.word.data.dao.StudySessionDao  Insert )com.example.word.data.dao.StudySessionDao  Int )com.example.word.data.dao.StudySessionDao  List )com.example.word.data.dao.StudySessionDao  LiveData )com.example.word.data.dao.StudySessionDao  Long )com.example.word.data.dao.StudySessionDao  Query )com.example.word.data.dao.StudySessionDao  String )com.example.word.data.dao.StudySessionDao  StudySession )com.example.word.data.dao.StudySessionDao  insertStudySession )com.example.word.data.dao.StudySessionDao  Float )com.example.word.data.dao.UserProgressDao  Insert )com.example.word.data.dao.UserProgressDao  Int )com.example.word.data.dao.UserProgressDao  LiveData )com.example.word.data.dao.UserProgressDao  Long )com.example.word.data.dao.UserProgressDao  OnConflictStrategy )com.example.word.data.dao.UserProgressDao  Query )com.example.word.data.dao.UserProgressDao  UserProgress )com.example.word.data.dao.UserProgressDao  addExperiencePoints )com.example.word.data.dao.UserProgressDao  insertOrUpdateUserProgress )com.example.word.data.dao.UserProgressDao  updateAverageAccuracy )com.example.word.data.dao.UserProgressDao  updateLearnedPhrasesCount )com.example.word.data.dao.UserProgressDao  updateLearnedWordsCount )com.example.word.data.dao.UserProgressDao  updateLevelAndExp )com.example.word.data.dao.UserProgressDao  updateStudyDays )com.example.word.data.dao.UserProgressDao  updateStudyTime )com.example.word.data.dao.UserProgressDao  Boolean !com.example.word.data.dao.WordDao  Delete !com.example.word.data.dao.WordDao  Float !com.example.word.data.dao.WordDao  Insert !com.example.word.data.dao.WordDao  Int !com.example.word.data.dao.WordDao  List !com.example.word.data.dao.WordDao  LiveData !com.example.word.data.dao.WordDao  Long !com.example.word.data.dao.WordDao  OnConflictStrategy !com.example.word.data.dao.WordDao  Query !com.example.word.data.dao.WordDao  String !com.example.word.data.dao.WordDao  Update !com.example.word.data.dao.WordDao  Word !com.example.word.data.dao.WordDao  updateBookmarkStatus !com.example.word.data.dao.WordDao  updateReviewInfo !com.example.word.data.dao.WordDao  updateStudyStats !com.example.word.data.dao.WordDao  
updateWord !com.example.word.data.dao.WordDao  
EssayTemplate com.example.word.data.database  EssayTemplateDao com.example.word.data.database  Phrase com.example.word.data.database  	PhraseDao com.example.word.data.database  StudySession com.example.word.data.database  StudySessionDao com.example.word.data.database  UserProgress com.example.word.data.database  UserProgressDao com.example.word.data.database  Volatile com.example.word.data.database  Word com.example.word.data.database  WordDao com.example.word.data.database  WordDatabase com.example.word.data.database  Context +com.example.word.data.database.WordDatabase  EssayTemplateDao +com.example.word.data.database.WordDatabase  	PhraseDao +com.example.word.data.database.WordDatabase  RoomDatabase +com.example.word.data.database.WordDatabase  StudySessionDao +com.example.word.data.database.WordDatabase  SupportSQLiteDatabase +com.example.word.data.database.WordDatabase  UserProgressDao +com.example.word.data.database.WordDatabase  Volatile +com.example.word.data.database.WordDatabase  WordDao +com.example.word.data.database.WordDatabase  WordDatabase +com.example.word.data.database.WordDatabase  Context 5com.example.word.data.database.WordDatabase.Companion  EssayTemplateDao 5com.example.word.data.database.WordDatabase.Companion  	PhraseDao 5com.example.word.data.database.WordDatabase.Companion  RoomDatabase 5com.example.word.data.database.WordDatabase.Companion  StudySessionDao 5com.example.word.data.database.WordDatabase.Companion  SupportSQLiteDatabase 5com.example.word.data.database.WordDatabase.Companion  UserProgressDao 5com.example.word.data.database.WordDatabase.Companion  Volatile 5com.example.word.data.database.WordDatabase.Companion  WordDao 5com.example.word.data.database.WordDatabase.Companion  WordDatabase 5com.example.word.data.database.WordDatabase.Companion  EssayTemplateDao @com.example.word.data.database.WordDatabase.WordDatabaseCallback  	PhraseDao @com.example.word.data.database.WordDatabase.WordDatabaseCallback  SupportSQLiteDatabase @com.example.word.data.database.WordDatabase.WordDatabaseCallback  WordDao @com.example.word.data.database.WordDatabase.WordDatabaseCallback  WordDatabase @com.example.word.data.database.WordDatabase.WordDatabaseCallback  Boolean com.example.word.data.entities  
EssayTemplate com.example.word.data.entities  EssayTemplateDao com.example.word.data.entities  Float com.example.word.data.entities  Int com.example.word.data.entities  Long com.example.word.data.entities  Phrase com.example.word.data.entities  	PhraseDao com.example.word.data.entities  String com.example.word.data.entities  StudySession com.example.word.data.entities  StudySessionDao com.example.word.data.entities  UserProgress com.example.word.data.entities  UserProgressDao com.example.word.data.entities  Volatile com.example.word.data.entities  Word com.example.word.data.entities  WordDao com.example.word.data.entities  Boolean ,com.example.word.data.entities.EssayTemplate  Int ,com.example.word.data.entities.EssayTemplate  Long ,com.example.word.data.entities.EssayTemplate  
PrimaryKey ,com.example.word.data.entities.EssayTemplate  String ,com.example.word.data.entities.EssayTemplate  Boolean %com.example.word.data.entities.Phrase  Int %com.example.word.data.entities.Phrase  Long %com.example.word.data.entities.Phrase  
PrimaryKey %com.example.word.data.entities.Phrase  String %com.example.word.data.entities.Phrase  Long +com.example.word.data.entities.StudySession  
PrimaryKey +com.example.word.data.entities.StudySession  String +com.example.word.data.entities.StudySession  Float +com.example.word.data.entities.UserProgress  Int +com.example.word.data.entities.UserProgress  Long +com.example.word.data.entities.UserProgress  
PrimaryKey +com.example.word.data.entities.UserProgress  Boolean #com.example.word.data.entities.Word  Float #com.example.word.data.entities.Word  Int #com.example.word.data.entities.Word  Long #com.example.word.data.entities.Word  
PrimaryKey #com.example.word.data.entities.Word  String #com.example.word.data.entities.Word  Boolean  com.example.word.data.repository  
EssayTemplate  com.example.word.data.repository  EssayTemplateDao  com.example.word.data.repository  Float  com.example.word.data.repository  Int  com.example.word.data.repository  List  com.example.word.data.repository  Long  com.example.word.data.repository  Phrase  com.example.word.data.repository  	PhraseDao  com.example.word.data.repository  String  com.example.word.data.repository  StudySession  com.example.word.data.repository  StudySessionDao  com.example.word.data.repository  UserProgress  com.example.word.data.repository  UserProgressDao  com.example.word.data.repository  Word  com.example.word.data.repository  WordDao  com.example.word.data.repository  WordRepository  com.example.word.data.repository  Boolean /com.example.word.data.repository.WordRepository  
EssayTemplate /com.example.word.data.repository.WordRepository  EssayTemplateDao /com.example.word.data.repository.WordRepository  Float /com.example.word.data.repository.WordRepository  Int /com.example.word.data.repository.WordRepository  List /com.example.word.data.repository.WordRepository  LiveData /com.example.word.data.repository.WordRepository  Long /com.example.word.data.repository.WordRepository  Phrase /com.example.word.data.repository.WordRepository  	PhraseDao /com.example.word.data.repository.WordRepository  String /com.example.word.data.repository.WordRepository  StudySession /com.example.word.data.repository.WordRepository  StudySessionDao /com.example.word.data.repository.WordRepository  UserProgress /com.example.word.data.repository.WordRepository  UserProgressDao /com.example.word.data.repository.WordRepository  Word /com.example.word.data.repository.WordRepository  WordDao /com.example.word.data.repository.WordRepository  essayTemplateDao /com.example.word.data.repository.WordRepository  	phraseDao /com.example.word.data.repository.WordRepository  studySessionDao /com.example.word.data.repository.WordRepository  userProgressDao /com.example.word.data.repository.WordRepository  wordDao /com.example.word.data.repository.WordRepository  ActivityMainBinding com.example.word.databinding  FragmentEssayBinding com.example.word.databinding  FragmentPhrasesBinding com.example.word.databinding  FragmentProgressBinding com.example.word.databinding  FragmentQuizBinding com.example.word.databinding  FragmentVocabularyBinding com.example.word.databinding  ItemPhraseBinding com.example.word.databinding  ItemWordBinding com.example.word.databinding  getROOT .com.example.word.databinding.ItemPhraseBinding  getRoot .com.example.word.databinding.ItemPhraseBinding  root .com.example.word.databinding.ItemPhraseBinding  setRoot .com.example.word.databinding.ItemPhraseBinding  getROOT ,com.example.word.databinding.ItemWordBinding  getRoot ,com.example.word.databinding.ItemWordBinding  root ,com.example.word.databinding.ItemWordBinding  setRoot ,com.example.word.databinding.ItemWordBinding  
EssayFragment com.example.word.ui.essay  Bundle 'com.example.word.ui.essay.EssayFragment  FragmentEssayBinding 'com.example.word.ui.essay.EssayFragment  LayoutInflater 'com.example.word.ui.essay.EssayFragment  View 'com.example.word.ui.essay.EssayFragment  	ViewGroup 'com.example.word.ui.essay.EssayFragment  _binding 'com.example.word.ui.essay.EssayFragment  Boolean com.example.word.ui.phrases  Int com.example.word.ui.phrases  
PhraseAdapter com.example.word.ui.phrases  PhrasesFragment com.example.word.ui.phrases  Unit com.example.word.ui.phrases  getValue com.example.word.ui.phrases  provideDelegate com.example.word.ui.phrases  
viewModels com.example.word.ui.phrases  Boolean )com.example.word.ui.phrases.PhraseAdapter  DiffUtil )com.example.word.ui.phrases.PhraseAdapter  Int )com.example.word.ui.phrases.PhraseAdapter  ItemPhraseBinding )com.example.word.ui.phrases.PhraseAdapter  Phrase )com.example.word.ui.phrases.PhraseAdapter  PhraseDiffCallback )com.example.word.ui.phrases.PhraseAdapter  PhraseViewHolder )com.example.word.ui.phrases.PhraseAdapter  RecyclerView )com.example.word.ui.phrases.PhraseAdapter  Unit )com.example.word.ui.phrases.PhraseAdapter  	ViewGroup )com.example.word.ui.phrases.PhraseAdapter  Boolean <com.example.word.ui.phrases.PhraseAdapter.PhraseDiffCallback  Phrase <com.example.word.ui.phrases.PhraseAdapter.PhraseDiffCallback  ItemPhraseBinding :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  Phrase :com.example.word.ui.phrases.PhraseAdapter.PhraseViewHolder  Boolean +com.example.word.ui.phrases.PhrasesFragment  Bundle +com.example.word.ui.phrases.PhrasesFragment  FragmentPhrasesBinding +com.example.word.ui.phrases.PhrasesFragment  LayoutInflater +com.example.word.ui.phrases.PhrasesFragment  
PhraseAdapter +com.example.word.ui.phrases.PhrasesFragment  PhraseViewModel +com.example.word.ui.phrases.PhrasesFragment  View +com.example.word.ui.phrases.PhrasesFragment  	ViewGroup +com.example.word.ui.phrases.PhrasesFragment  _binding +com.example.word.ui.phrases.PhrasesFragment  getGETValue +com.example.word.ui.phrases.PhrasesFragment  getGetValue +com.example.word.ui.phrases.PhrasesFragment  getPROVIDEDelegate +com.example.word.ui.phrases.PhrasesFragment  getProvideDelegate +com.example.word.ui.phrases.PhrasesFragment  
getVIEWModels +com.example.word.ui.phrases.PhrasesFragment  getValue +com.example.word.ui.phrases.PhrasesFragment  
getViewModels +com.example.word.ui.phrases.PhrasesFragment  provideDelegate +com.example.word.ui.phrases.PhrasesFragment  
viewModels +com.example.word.ui.phrases.PhrasesFragment  ProgressFragment com.example.word.ui.progress  Bundle -com.example.word.ui.progress.ProgressFragment  FragmentProgressBinding -com.example.word.ui.progress.ProgressFragment  LayoutInflater -com.example.word.ui.progress.ProgressFragment  View -com.example.word.ui.progress.ProgressFragment  	ViewGroup -com.example.word.ui.progress.ProgressFragment  _binding -com.example.word.ui.progress.ProgressFragment  QuizFragment com.example.word.ui.quiz  Bundle %com.example.word.ui.quiz.QuizFragment  FragmentQuizBinding %com.example.word.ui.quiz.QuizFragment  LayoutInflater %com.example.word.ui.quiz.QuizFragment  View %com.example.word.ui.quiz.QuizFragment  	ViewGroup %com.example.word.ui.quiz.QuizFragment  _binding %com.example.word.ui.quiz.QuizFragment  Boolean com.example.word.ui.viewmodel  Int com.example.word.ui.viewmodel  List com.example.word.ui.viewmodel  Long com.example.word.ui.viewmodel  MutableLiveData com.example.word.ui.viewmodel  PhraseViewModel com.example.word.ui.viewmodel  String com.example.word.ui.viewmodel  
WordViewModel com.example.word.ui.viewmodel  Application -com.example.word.ui.viewmodel.PhraseViewModel  Boolean -com.example.word.ui.viewmodel.PhraseViewModel  Int -com.example.word.ui.viewmodel.PhraseViewModel  List -com.example.word.ui.viewmodel.PhraseViewModel  LiveData -com.example.word.ui.viewmodel.PhraseViewModel  Long -com.example.word.ui.viewmodel.PhraseViewModel  MutableLiveData -com.example.word.ui.viewmodel.PhraseViewModel  Phrase -com.example.word.ui.viewmodel.PhraseViewModel  String -com.example.word.ui.viewmodel.PhraseViewModel  WordRepository -com.example.word.ui.viewmodel.PhraseViewModel  _currentPhrase -com.example.word.ui.viewmodel.PhraseViewModel  
_errorMessage -com.example.word.ui.viewmodel.PhraseViewModel  _filteredPhrases -com.example.word.ui.viewmodel.PhraseViewModel  
_isLoading -com.example.word.ui.viewmodel.PhraseViewModel  _phraseCategories -com.example.word.ui.viewmodel.PhraseViewModel  _phraseTypes -com.example.word.ui.viewmodel.PhraseViewModel  _searchResults -com.example.word.ui.viewmodel.PhraseViewModel  _selectedCategory -com.example.word.ui.viewmodel.PhraseViewModel  _selectedDifficulty -com.example.word.ui.viewmodel.PhraseViewModel  
_selectedType -com.example.word.ui.viewmodel.PhraseViewModel  Application +com.example.word.ui.viewmodel.WordViewModel  Boolean +com.example.word.ui.viewmodel.WordViewModel  Int +com.example.word.ui.viewmodel.WordViewModel  List +com.example.word.ui.viewmodel.WordViewModel  LiveData +com.example.word.ui.viewmodel.WordViewModel  Long +com.example.word.ui.viewmodel.WordViewModel  MutableLiveData +com.example.word.ui.viewmodel.WordViewModel  String +com.example.word.ui.viewmodel.WordViewModel  Word +com.example.word.ui.viewmodel.WordViewModel  WordRepository +com.example.word.ui.viewmodel.WordViewModel  _currentQuizIndex +com.example.word.ui.viewmodel.WordViewModel  _currentWord +com.example.word.ui.viewmodel.WordViewModel  
_errorMessage +com.example.word.ui.viewmodel.WordViewModel  
_isLoading +com.example.word.ui.viewmodel.WordViewModel  
_quizScore +com.example.word.ui.viewmodel.WordViewModel  
_quizWords +com.example.word.ui.viewmodel.WordViewModel  _searchResults +com.example.word.ui.viewmodel.WordViewModel  
_studyMode +com.example.word.ui.viewmodel.WordViewModel  _wordsForReview +com.example.word.ui.viewmodel.WordViewModel  Boolean com.example.word.ui.vocabulary  Int com.example.word.ui.vocabulary  Unit com.example.word.ui.vocabulary  VocabularyFragment com.example.word.ui.vocabulary  WordAdapter com.example.word.ui.vocabulary  getValue com.example.word.ui.vocabulary  provideDelegate com.example.word.ui.vocabulary  
viewModels com.example.word.ui.vocabulary  Boolean 1com.example.word.ui.vocabulary.VocabularyFragment  Bundle 1com.example.word.ui.vocabulary.VocabularyFragment  FragmentVocabularyBinding 1com.example.word.ui.vocabulary.VocabularyFragment  LayoutInflater 1com.example.word.ui.vocabulary.VocabularyFragment  View 1com.example.word.ui.vocabulary.VocabularyFragment  	ViewGroup 1com.example.word.ui.vocabulary.VocabularyFragment  WordAdapter 1com.example.word.ui.vocabulary.VocabularyFragment  
WordViewModel 1com.example.word.ui.vocabulary.VocabularyFragment  _binding 1com.example.word.ui.vocabulary.VocabularyFragment  getGETValue 1com.example.word.ui.vocabulary.VocabularyFragment  getGetValue 1com.example.word.ui.vocabulary.VocabularyFragment  getPROVIDEDelegate 1com.example.word.ui.vocabulary.VocabularyFragment  getProvideDelegate 1com.example.word.ui.vocabulary.VocabularyFragment  
getVIEWModels 1com.example.word.ui.vocabulary.VocabularyFragment  getValue 1com.example.word.ui.vocabulary.VocabularyFragment  
getViewModels 1com.example.word.ui.vocabulary.VocabularyFragment  provideDelegate 1com.example.word.ui.vocabulary.VocabularyFragment  
viewModels 1com.example.word.ui.vocabulary.VocabularyFragment  Boolean *com.example.word.ui.vocabulary.WordAdapter  DiffUtil *com.example.word.ui.vocabulary.WordAdapter  Int *com.example.word.ui.vocabulary.WordAdapter  ItemWordBinding *com.example.word.ui.vocabulary.WordAdapter  RecyclerView *com.example.word.ui.vocabulary.WordAdapter  Unit *com.example.word.ui.vocabulary.WordAdapter  	ViewGroup *com.example.word.ui.vocabulary.WordAdapter  Word *com.example.word.ui.vocabulary.WordAdapter  WordDiffCallback *com.example.word.ui.vocabulary.WordAdapter  WordViewHolder *com.example.word.ui.vocabulary.WordAdapter  Boolean ;com.example.word.ui.vocabulary.WordAdapter.WordDiffCallback  Word ;com.example.word.ui.vocabulary.WordAdapter.WordDiffCallback  ItemWordBinding 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  Word 9com.example.word.ui.vocabulary.WordAdapter.WordViewHolder  BottomNavigationView ,com.google.android.material.bottomnavigation  MaterialCardView  com.google.android.material.card  
EssayTemplate 	java.lang  MutableLiveData 	java.lang  OnConflictStrategy 	java.lang  Phrase 	java.lang  StudySession 	java.lang  UserProgress 	java.lang  Word 	java.lang  getValue 	java.lang  provideDelegate 	java.lang  MutableLiveData 	java.util  Array kotlin  Boolean kotlin  
EssayTemplate kotlin  Float kotlin  Int kotlin  Lazy kotlin  Long kotlin  MutableLiveData kotlin  Nothing kotlin  OnConflictStrategy kotlin  Phrase kotlin  String kotlin  StudySession kotlin  Unit kotlin  UserProgress kotlin  Volatile kotlin  Word kotlin  arrayOf kotlin  getValue kotlin  provideDelegate kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
EssayTemplate kotlin.annotation  MutableLiveData kotlin.annotation  OnConflictStrategy kotlin.annotation  Phrase kotlin.annotation  StudySession kotlin.annotation  UserProgress kotlin.annotation  Volatile kotlin.annotation  Word kotlin.annotation  getValue kotlin.annotation  provideDelegate kotlin.annotation  
EssayTemplate kotlin.collections  List kotlin.collections  MutableLiveData kotlin.collections  OnConflictStrategy kotlin.collections  Phrase kotlin.collections  StudySession kotlin.collections  UserProgress kotlin.collections  Volatile kotlin.collections  Word kotlin.collections  getValue kotlin.collections  provideDelegate kotlin.collections  
EssayTemplate kotlin.comparisons  MutableLiveData kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Phrase kotlin.comparisons  StudySession kotlin.comparisons  UserProgress kotlin.comparisons  Volatile kotlin.comparisons  Word kotlin.comparisons  getValue kotlin.comparisons  provideDelegate kotlin.comparisons  
EssayTemplate 	kotlin.io  MutableLiveData 	kotlin.io  OnConflictStrategy 	kotlin.io  Phrase 	kotlin.io  StudySession 	kotlin.io  UserProgress 	kotlin.io  Volatile 	kotlin.io  Word 	kotlin.io  getValue 	kotlin.io  provideDelegate 	kotlin.io  
EssayTemplate 
kotlin.jvm  MutableLiveData 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Phrase 
kotlin.jvm  StudySession 
kotlin.jvm  UserProgress 
kotlin.jvm  Volatile 
kotlin.jvm  Word 
kotlin.jvm  getValue 
kotlin.jvm  provideDelegate 
kotlin.jvm  
EssayTemplate 
kotlin.ranges  MutableLiveData 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Phrase 
kotlin.ranges  StudySession 
kotlin.ranges  UserProgress 
kotlin.ranges  Volatile 
kotlin.ranges  Word 
kotlin.ranges  getValue 
kotlin.ranges  provideDelegate 
kotlin.ranges  KClass kotlin.reflect  
EssayTemplate kotlin.sequences  MutableLiveData kotlin.sequences  OnConflictStrategy kotlin.sequences  Phrase kotlin.sequences  StudySession kotlin.sequences  UserProgress kotlin.sequences  Volatile kotlin.sequences  Word kotlin.sequences  getValue kotlin.sequences  provideDelegate kotlin.sequences  
EssayTemplate kotlin.text  MutableLiveData kotlin.text  OnConflictStrategy kotlin.text  Phrase kotlin.text  StudySession kotlin.text  UserProgress kotlin.text  Volatile kotlin.text  Word kotlin.text  getValue kotlin.text  provideDelegate kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  launch kotlinx.coroutines  LinearLayout android.widget  AchievementAdapter androidx.fragment.app.Fragment  DifficultyStats androidx.fragment.app.Fragment  EssayTemplateAdapter androidx.fragment.app.Fragment  EssayViewModel androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  Long androidx.fragment.app.Fragment  Map androidx.fragment.app.Fragment  ProgressViewModel androidx.fragment.app.Fragment  QuizProgress androidx.fragment.app.Fragment  QuizQuestion androidx.fragment.app.Fragment  
QuizResult androidx.fragment.app.Fragment  
QuizViewModel androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  StudyStatistics androidx.fragment.app.Fragment  VocabularyMastery androidx.fragment.app.Fragment  com androidx.fragment.app.Fragment  Achievement #androidx.lifecycle.AndroidViewModel  DailyStudyRecord #androidx.lifecycle.AndroidViewModel  Date #androidx.lifecycle.AndroidViewModel  
EssayTemplate #androidx.lifecycle.AndroidViewModel  QuestionType #androidx.lifecycle.AndroidViewModel  
QuizConfig #androidx.lifecycle.AndroidViewModel  QuizProgress #androidx.lifecycle.AndroidViewModel  QuizQuestion #androidx.lifecycle.AndroidViewModel  
QuizResult #androidx.lifecycle.AndroidViewModel  	QuizState #androidx.lifecycle.AndroidViewModel  StudyStatistics #androidx.lifecycle.AndroidViewModel  UserProgress #androidx.lifecycle.AndroidViewModel  VocabularyMastery #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  Achievement androidx.lifecycle.ViewModel  DailyStudyRecord androidx.lifecycle.ViewModel  Date androidx.lifecycle.ViewModel  
EssayTemplate androidx.lifecycle.ViewModel  QuestionType androidx.lifecycle.ViewModel  
QuizConfig androidx.lifecycle.ViewModel  QuizProgress androidx.lifecycle.ViewModel  QuizQuestion androidx.lifecycle.ViewModel  
QuizResult androidx.lifecycle.ViewModel  	QuizState androidx.lifecycle.ViewModel  StudyStatistics androidx.lifecycle.ViewModel  UserProgress androidx.lifecycle.ViewModel  VocabularyMastery androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  Achievement 2androidx.recyclerview.widget.DiffUtil.ItemCallback  
EssayTemplate 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Achievement (androidx.recyclerview.widget.ListAdapter  
EssayTemplate (androidx.recyclerview.widget.ListAdapter  ItemAchievementBinding (androidx.recyclerview.widget.ListAdapter  ItemEssayTemplateBinding (androidx.recyclerview.widget.ListAdapter  String (androidx.recyclerview.widget.ListAdapter  Achievement 1androidx.recyclerview.widget.RecyclerView.Adapter  
EssayTemplate 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemAchievementBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemEssayTemplateBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  Achievement 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
EssayTemplate 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Int 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemAchievementBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemEssayTemplateBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  String 4androidx.recyclerview.widget.RecyclerView.ViewHolder  List com.example.word.data.database  VocabularyDataProvider com.example.word.data.database  
EssayTemplate 5com.example.word.data.database.VocabularyDataProvider  List 5com.example.word.data.database.VocabularyDataProvider  Phrase 5com.example.word.data.database.VocabularyDataProvider  Word 5com.example.word.data.database.VocabularyDataProvider  ItemAchievementBinding com.example.word.databinding  ItemEssayTemplateBinding com.example.word.databinding  getROOT 3com.example.word.databinding.ItemAchievementBinding  getRoot 3com.example.word.databinding.ItemAchievementBinding  root 3com.example.word.databinding.ItemAchievementBinding  setRoot 3com.example.word.databinding.ItemAchievementBinding  getROOT 5com.example.word.databinding.ItemEssayTemplateBinding  getRoot 5com.example.word.databinding.ItemEssayTemplateBinding  root 5com.example.word.databinding.ItemEssayTemplateBinding  setRoot 5com.example.word.databinding.ItemEssayTemplateBinding  Boolean com.example.word.ui.essay  EssayTemplateAdapter com.example.word.ui.essay  EssayViewModel com.example.word.ui.essay  Int com.example.word.ui.essay  List com.example.word.ui.essay  MutableLiveData com.example.word.ui.essay  String com.example.word.ui.essay  Unit com.example.word.ui.essay  com com.example.word.ui.essay  getValue com.example.word.ui.essay  provideDelegate com.example.word.ui.essay  
viewModels com.example.word.ui.essay  Boolean 'com.example.word.ui.essay.EssayFragment  EssayTemplateAdapter 'com.example.word.ui.essay.EssayFragment  EssayViewModel 'com.example.word.ui.essay.EssayFragment  com 'com.example.word.ui.essay.EssayFragment  getGETValue 'com.example.word.ui.essay.EssayFragment  getGetValue 'com.example.word.ui.essay.EssayFragment  getPROVIDEDelegate 'com.example.word.ui.essay.EssayFragment  getProvideDelegate 'com.example.word.ui.essay.EssayFragment  
getVIEWModels 'com.example.word.ui.essay.EssayFragment  getValue 'com.example.word.ui.essay.EssayFragment  
getViewModels 'com.example.word.ui.essay.EssayFragment  provideDelegate 'com.example.word.ui.essay.EssayFragment  
viewModels 'com.example.word.ui.essay.EssayFragment  Boolean .com.example.word.ui.essay.EssayTemplateAdapter  DiffUtil .com.example.word.ui.essay.EssayTemplateAdapter  
EssayTemplate .com.example.word.ui.essay.EssayTemplateAdapter  Int .com.example.word.ui.essay.EssayTemplateAdapter  ItemEssayTemplateBinding .com.example.word.ui.essay.EssayTemplateAdapter  RecyclerView .com.example.word.ui.essay.EssayTemplateAdapter  String .com.example.word.ui.essay.EssayTemplateAdapter  TemplateDiffCallback .com.example.word.ui.essay.EssayTemplateAdapter  TemplateViewHolder .com.example.word.ui.essay.EssayTemplateAdapter  Unit .com.example.word.ui.essay.EssayTemplateAdapter  	ViewGroup .com.example.word.ui.essay.EssayTemplateAdapter  Boolean Ccom.example.word.ui.essay.EssayTemplateAdapter.TemplateDiffCallback  
EssayTemplate Ccom.example.word.ui.essay.EssayTemplateAdapter.TemplateDiffCallback  
EssayTemplate Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  Int Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  ItemEssayTemplateBinding Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  String Acom.example.word.ui.essay.EssayTemplateAdapter.TemplateViewHolder  Application (com.example.word.ui.essay.EssayViewModel  Boolean (com.example.word.ui.essay.EssayViewModel  
EssayTemplate (com.example.word.ui.essay.EssayViewModel  Int (com.example.word.ui.essay.EssayViewModel  List (com.example.word.ui.essay.EssayViewModel  LiveData (com.example.word.ui.essay.EssayViewModel  MutableLiveData (com.example.word.ui.essay.EssayViewModel  String (com.example.word.ui.essay.EssayViewModel  WordRepository (com.example.word.ui.essay.EssayViewModel  _currentTemplate (com.example.word.ui.essay.EssayViewModel  
_errorMessage (com.example.word.ui.essay.EssayViewModel  _filteredTemplates (com.example.word.ui.essay.EssayViewModel  
_isLoading (com.example.word.ui.essay.EssayViewModel  _searchResults (com.example.word.ui.essay.EssayViewModel  _selectedCategory (com.example.word.ui.essay.EssayViewModel  _selectedDifficulty (com.example.word.ui.essay.EssayViewModel  
_selectedType (com.example.word.ui.essay.EssayViewModel  _templateCategories (com.example.word.ui.essay.EssayViewModel  _templateTypes (com.example.word.ui.essay.EssayViewModel  Achievement com.example.word.ui.progress  AchievementAdapter com.example.word.ui.progress  Boolean com.example.word.ui.progress  DailyStudyRecord com.example.word.ui.progress  Date com.example.word.ui.progress  DifficultyStats com.example.word.ui.progress  Float com.example.word.ui.progress  Int com.example.word.ui.progress  List com.example.word.ui.progress  Long com.example.word.ui.progress  Map com.example.word.ui.progress  MutableLiveData com.example.word.ui.progress  ProgressViewModel com.example.word.ui.progress  String com.example.word.ui.progress  StudyStatistics com.example.word.ui.progress  VocabularyMastery com.example.word.ui.progress  com com.example.word.ui.progress  getValue com.example.word.ui.progress  provideDelegate com.example.word.ui.progress  
viewModels com.example.word.ui.progress  Boolean (com.example.word.ui.progress.Achievement  Int (com.example.word.ui.progress.Achievement  String (com.example.word.ui.progress.Achievement  Achievement /com.example.word.ui.progress.AchievementAdapter  AchievementDiffCallback /com.example.word.ui.progress.AchievementAdapter  AchievementViewHolder /com.example.word.ui.progress.AchievementAdapter  Boolean /com.example.word.ui.progress.AchievementAdapter  DiffUtil /com.example.word.ui.progress.AchievementAdapter  Int /com.example.word.ui.progress.AchievementAdapter  ItemAchievementBinding /com.example.word.ui.progress.AchievementAdapter  RecyclerView /com.example.word.ui.progress.AchievementAdapter  	ViewGroup /com.example.word.ui.progress.AchievementAdapter  Achievement Gcom.example.word.ui.progress.AchievementAdapter.AchievementDiffCallback  Boolean Gcom.example.word.ui.progress.AchievementAdapter.AchievementDiffCallback  Achievement Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  ItemAchievementBinding Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  Date -com.example.word.ui.progress.DailyStudyRecord  Int -com.example.word.ui.progress.DailyStudyRecord  Int ,com.example.word.ui.progress.DifficultyStats  AchievementAdapter -com.example.word.ui.progress.ProgressFragment  DifficultyStats -com.example.word.ui.progress.ProgressFragment  Int -com.example.word.ui.progress.ProgressFragment  Long -com.example.word.ui.progress.ProgressFragment  Map -com.example.word.ui.progress.ProgressFragment  ProgressViewModel -com.example.word.ui.progress.ProgressFragment  String -com.example.word.ui.progress.ProgressFragment  StudyStatistics -com.example.word.ui.progress.ProgressFragment  VocabularyMastery -com.example.word.ui.progress.ProgressFragment  com -com.example.word.ui.progress.ProgressFragment  getGETValue -com.example.word.ui.progress.ProgressFragment  getGetValue -com.example.word.ui.progress.ProgressFragment  getPROVIDEDelegate -com.example.word.ui.progress.ProgressFragment  getProvideDelegate -com.example.word.ui.progress.ProgressFragment  
getVIEWModels -com.example.word.ui.progress.ProgressFragment  getValue -com.example.word.ui.progress.ProgressFragment  
getViewModels -com.example.word.ui.progress.ProgressFragment  provideDelegate -com.example.word.ui.progress.ProgressFragment  
viewModels -com.example.word.ui.progress.ProgressFragment  Achievement .com.example.word.ui.progress.ProgressViewModel  Application .com.example.word.ui.progress.ProgressViewModel  Boolean .com.example.word.ui.progress.ProgressViewModel  DailyStudyRecord .com.example.word.ui.progress.ProgressViewModel  Date .com.example.word.ui.progress.ProgressViewModel  Int .com.example.word.ui.progress.ProgressViewModel  List .com.example.word.ui.progress.ProgressViewModel  LiveData .com.example.word.ui.progress.ProgressViewModel  Long .com.example.word.ui.progress.ProgressViewModel  MutableLiveData .com.example.word.ui.progress.ProgressViewModel  String .com.example.word.ui.progress.ProgressViewModel  StudyStatistics .com.example.word.ui.progress.ProgressViewModel  UserProgress .com.example.word.ui.progress.ProgressViewModel  VocabularyMastery .com.example.word.ui.progress.ProgressViewModel  WordRepository .com.example.word.ui.progress.ProgressViewModel  
_achievements .com.example.word.ui.progress.ProgressViewModel  
_errorMessage .com.example.word.ui.progress.ProgressViewModel  
_isLoading .com.example.word.ui.progress.ProgressViewModel  
_studyHistory .com.example.word.ui.progress.ProgressViewModel  _studyStatistics .com.example.word.ui.progress.ProgressViewModel  _vocabularyMastery .com.example.word.ui.progress.ProgressViewModel  Float ,com.example.word.ui.progress.StudyStatistics  Int ,com.example.word.ui.progress.StudyStatistics  Long ,com.example.word.ui.progress.StudyStatistics  DifficultyStats .com.example.word.ui.progress.VocabularyMastery  Int .com.example.word.ui.progress.VocabularyMastery  Map .com.example.word.ui.progress.VocabularyMastery  Boolean com.example.word.ui.quiz  Int com.example.word.ui.quiz  List com.example.word.ui.quiz  Long com.example.word.ui.quiz  MutableLiveData com.example.word.ui.quiz  QuestionType com.example.word.ui.quiz  
QuizConfig com.example.word.ui.quiz  QuizProgress com.example.word.ui.quiz  QuizQuestion com.example.word.ui.quiz  
QuizResult com.example.word.ui.quiz  
QuizSource com.example.word.ui.quiz  	QuizState com.example.word.ui.quiz  
QuizViewModel com.example.word.ui.quiz  String com.example.word.ui.quiz  	emptyList com.example.word.ui.quiz  getValue com.example.word.ui.quiz  provideDelegate com.example.word.ui.quiz  
viewModels com.example.word.ui.quiz  Int #com.example.word.ui.quiz.QuizConfig  QuestionType #com.example.word.ui.quiz.QuizConfig  
QuizSource #com.example.word.ui.quiz.QuizConfig  Long %com.example.word.ui.quiz.QuizFragment  QuizProgress %com.example.word.ui.quiz.QuizFragment  QuizQuestion %com.example.word.ui.quiz.QuizFragment  
QuizResult %com.example.word.ui.quiz.QuizFragment  
QuizViewModel %com.example.word.ui.quiz.QuizFragment  String %com.example.word.ui.quiz.QuizFragment  getGETValue %com.example.word.ui.quiz.QuizFragment  getGetValue %com.example.word.ui.quiz.QuizFragment  getPROVIDEDelegate %com.example.word.ui.quiz.QuizFragment  getProvideDelegate %com.example.word.ui.quiz.QuizFragment  
getVIEWModels %com.example.word.ui.quiz.QuizFragment  getValue %com.example.word.ui.quiz.QuizFragment  
getViewModels %com.example.word.ui.quiz.QuizFragment  provideDelegate %com.example.word.ui.quiz.QuizFragment  
viewModels %com.example.word.ui.quiz.QuizFragment  Int %com.example.word.ui.quiz.QuizProgress  Int %com.example.word.ui.quiz.QuizQuestion  List %com.example.word.ui.quiz.QuizQuestion  Long %com.example.word.ui.quiz.QuizQuestion  QuestionType %com.example.word.ui.quiz.QuizQuestion  String %com.example.word.ui.quiz.QuizQuestion  Word %com.example.word.ui.quiz.QuizQuestion  Int #com.example.word.ui.quiz.QuizResult  Long #com.example.word.ui.quiz.QuizResult  Application &com.example.word.ui.quiz.QuizViewModel  Boolean &com.example.word.ui.quiz.QuizViewModel  List &com.example.word.ui.quiz.QuizViewModel  LiveData &com.example.word.ui.quiz.QuizViewModel  Long &com.example.word.ui.quiz.QuizViewModel  MutableLiveData &com.example.word.ui.quiz.QuizViewModel  QuestionType &com.example.word.ui.quiz.QuizViewModel  
QuizConfig &com.example.word.ui.quiz.QuizViewModel  QuizProgress &com.example.word.ui.quiz.QuizViewModel  QuizQuestion &com.example.word.ui.quiz.QuizViewModel  
QuizResult &com.example.word.ui.quiz.QuizViewModel  	QuizState &com.example.word.ui.quiz.QuizViewModel  String &com.example.word.ui.quiz.QuizViewModel  Word &com.example.word.ui.quiz.QuizViewModel  WordRepository &com.example.word.ui.quiz.QuizViewModel  _currentQuestion &com.example.word.ui.quiz.QuizViewModel  
_errorMessage &com.example.word.ui.quiz.QuizViewModel  
_isLoading &com.example.word.ui.quiz.QuizViewModel  
_quizProgress &com.example.word.ui.quiz.QuizViewModel  _quizResult &com.example.word.ui.quiz.QuizViewModel  
_quizState &com.example.word.ui.quiz.QuizViewModel  	emptyList &com.example.word.ui.quiz.QuizViewModel  getEMPTYList &com.example.word.ui.quiz.QuizViewModel  getEmptyList &com.example.word.ui.quiz.QuizViewModel  com 	java.lang  	emptyList 	java.lang  SimpleDateFormat 	java.text  Date 	java.util  com 	java.util  getValue 	java.util  provideDelegate 	java.util  
viewModels 	java.util  com kotlin  	emptyList kotlin  com kotlin.annotation  	emptyList kotlin.annotation  Map kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  com kotlin.comparisons  	emptyList kotlin.comparisons  com 	kotlin.io  	emptyList 	kotlin.io  com 
kotlin.jvm  	emptyList 
kotlin.jvm  Random 
kotlin.random  com 
kotlin.ranges  	emptyList 
kotlin.ranges  com kotlin.sequences  	emptyList kotlin.sequences  com kotlin.text  	emptyList kotlin.text  AlarmManager android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  TimePickerDialog android.app  BroadcastReceiver android.content  Intent android.content  SharedPreferences android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  Canvas android.graphics  JvmOverloads android.graphics  Paint android.graphics  	emptyList android.graphics  ANTI_ALIAS_FLAG android.graphics.Paint  Build 
android.os  TextToSpeech android.speech.tts  OnInitListener android.speech.tts.TextToSpeech  AttributeSet android.util  AttributeSet android.view.View  Canvas android.view.View  Context android.view.View  Float android.view.View  Int android.view.View  JvmOverloads android.view.View  List android.view.View  Paint android.view.View  String android.view.View  StudyDataPoint android.view.View  	emptyList android.view.View  AppCompatDelegate androidx.appcompat.app  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  
ContextCompat androidx.core.content  FragmentSettingsBinding androidx.fragment.app.Fragment  List androidx.fragment.app.Fragment  SharedPreferences androidx.fragment.app.Fragment  StudyDataPoint androidx.fragment.app.Fragment  TextToSpeech androidx.fragment.app.Fragment  Achievement com.example.word.data.entities  AchievementCategory com.example.word.data.entities  ExperienceType com.example.word.data.entities  
StudyStats com.example.word.data.entities  	UserLevel com.example.word.data.entities  
coerceAtLeast com.example.word.data.entities  coerceAtMost com.example.word.data.entities  AchievementCategory *com.example.word.data.entities.Achievement  Boolean *com.example.word.data.entities.Achievement  Float *com.example.word.data.entities.Achievement  Int *com.example.word.data.entities.Achievement  String *com.example.word.data.entities.Achievement  coerceAtMost *com.example.word.data.entities.Achievement  currentValue *com.example.word.data.entities.Achievement  getCOERCEAtMost *com.example.word.data.entities.Achievement  getCoerceAtMost *com.example.word.data.entities.Achievement  targetValue *com.example.word.data.entities.Achievement  ExperienceType -com.example.word.data.entities.ExperienceType  Int -com.example.word.data.entities.ExperienceType  Float )com.example.word.data.entities.StudyStats  Int )com.example.word.data.entities.StudyStats  Float (com.example.word.data.entities.UserLevel  Int (com.example.word.data.entities.UserLevel  String (com.example.word.data.entities.UserLevel  
coerceAtLeast (com.example.word.data.entities.UserLevel  coerceAtMost (com.example.word.data.entities.UserLevel  currentExperience (com.example.word.data.entities.UserLevel  experienceRequired (com.example.word.data.entities.UserLevel  getCOERCEAtLeast (com.example.word.data.entities.UserLevel  getCOERCEAtMost (com.example.word.data.entities.UserLevel  getCoerceAtLeast (com.example.word.data.entities.UserLevel  getCoerceAtMost (com.example.word.data.entities.UserLevel  FragmentSettingsBinding com.example.word.databinding  List -com.example.word.ui.progress.ProgressFragment  StudyDataPoint -com.example.word.ui.progress.ProgressFragment  Int com.example.word.ui.settings  SettingsFragment com.example.word.ui.settings  Bundle -com.example.word.ui.settings.SettingsFragment  FragmentSettingsBinding -com.example.word.ui.settings.SettingsFragment  Int -com.example.word.ui.settings.SettingsFragment  LayoutInflater -com.example.word.ui.settings.SettingsFragment  SharedPreferences -com.example.word.ui.settings.SettingsFragment  View -com.example.word.ui.settings.SettingsFragment  	ViewGroup -com.example.word.ui.settings.SettingsFragment  _binding -com.example.word.ui.settings.SettingsFragment  String com.example.word.ui.vocabulary  Int 1com.example.word.ui.vocabulary.VocabularyFragment  String 1com.example.word.ui.vocabulary.VocabularyFragment  TextToSpeech 1com.example.word.ui.vocabulary.VocabularyFragment  String *com.example.word.ui.vocabulary.WordAdapter  Achievement com.example.word.utils  Boolean com.example.word.utils  Canvas com.example.word.utils  CircularProgressChart com.example.word.utils  DifficultyLevel com.example.word.utils  ExperienceType com.example.word.utils  Float com.example.word.utils  GameificationManager com.example.word.utils  Int com.example.word.utils  JvmOverloads com.example.word.utils  List com.example.word.utils  Long com.example.word.utils  Paint com.example.word.utils  ReviewResult com.example.word.utils  SpacedRepetitionAlgorithm com.example.word.utils  String com.example.word.utils  StudyDataPoint com.example.word.utils  StudyNotificationManager com.example.word.utils  StudyProgressChart com.example.word.utils  StudyReminderReceiver com.example.word.utils  StudySession com.example.word.utils  
StudyStats com.example.word.utils  	UserLevel com.example.word.utils  	emptyList com.example.word.utils  AttributeSet ,com.example.word.utils.CircularProgressChart  Canvas ,com.example.word.utils.CircularProgressChart  Context ,com.example.word.utils.CircularProgressChart  Float ,com.example.word.utils.CircularProgressChart  Int ,com.example.word.utils.CircularProgressChart  JvmOverloads ,com.example.word.utils.CircularProgressChart  Paint ,com.example.word.utils.CircularProgressChart  String ,com.example.word.utils.CircularProgressChart  DifficultyLevel &com.example.word.utils.DifficultyLevel  Int &com.example.word.utils.DifficultyLevel  String &com.example.word.utils.DifficultyLevel  Achievement +com.example.word.utils.GameificationManager  ExperienceType +com.example.word.utils.GameificationManager  Float +com.example.word.utils.GameificationManager  Int +com.example.word.utils.GameificationManager  List +com.example.word.utils.GameificationManager  String +com.example.word.utils.GameificationManager  
StudyStats +com.example.word.utils.GameificationManager  	UserLevel +com.example.word.utils.GameificationManager  Boolean #com.example.word.utils.ReviewResult  Float #com.example.word.utils.ReviewResult  Int #com.example.word.utils.ReviewResult  Long #com.example.word.utils.ReviewResult  Boolean 0com.example.word.utils.SpacedRepetitionAlgorithm  Float 0com.example.word.utils.SpacedRepetitionAlgorithm  Int 0com.example.word.utils.SpacedRepetitionAlgorithm  Long 0com.example.word.utils.SpacedRepetitionAlgorithm  ReviewResult 0com.example.word.utils.SpacedRepetitionAlgorithm  Float %com.example.word.utils.StudyDataPoint  String %com.example.word.utils.StudyDataPoint  Context /com.example.word.utils.StudyNotificationManager  Int /com.example.word.utils.StudyNotificationManager  String /com.example.word.utils.StudyNotificationManager  AttributeSet )com.example.word.utils.StudyProgressChart  Canvas )com.example.word.utils.StudyProgressChart  Context )com.example.word.utils.StudyProgressChart  Int )com.example.word.utils.StudyProgressChart  JvmOverloads )com.example.word.utils.StudyProgressChart  List )com.example.word.utils.StudyProgressChart  Paint )com.example.word.utils.StudyProgressChart  StudyDataPoint )com.example.word.utils.StudyProgressChart  	emptyList )com.example.word.utils.StudyProgressChart  getEMPTYList )com.example.word.utils.StudyProgressChart  getEmptyList )com.example.word.utils.StudyProgressChart  Context ,com.example.word.utils.StudyReminderReceiver  Intent ,com.example.word.utils.StudyReminderReceiver  Boolean #com.example.word.utils.StudySession  DifficultyLevel #com.example.word.utils.StudySession  Float #com.example.word.utils.StudySession  Long #com.example.word.utils.StudySession  endTime #com.example.word.utils.StudySession  	startTime #com.example.word.utils.StudySession  Paint 	java.lang  
coerceAtLeast 	java.lang  coerceAtMost 	java.lang  JvmOverloads kotlin  Paint kotlin  
coerceAtLeast kotlin  coerceAtMost kotlin  getCOERCEAtMost kotlin.Float  getCoerceAtMost kotlin.Float  getCOERCEAtLeast 
kotlin.Int  getCoerceAtLeast 
kotlin.Int  JvmOverloads kotlin.annotation  Paint kotlin.annotation  
coerceAtLeast kotlin.annotation  coerceAtMost kotlin.annotation  JvmOverloads kotlin.collections  Paint kotlin.collections  
coerceAtLeast kotlin.collections  coerceAtMost kotlin.collections  JvmOverloads kotlin.comparisons  Paint kotlin.comparisons  
coerceAtLeast kotlin.comparisons  coerceAtMost kotlin.comparisons  JvmOverloads 	kotlin.io  Paint 	kotlin.io  
coerceAtLeast 	kotlin.io  coerceAtMost 	kotlin.io  JvmOverloads 
kotlin.jvm  Paint 
kotlin.jvm  
coerceAtLeast 
kotlin.jvm  coerceAtMost 
kotlin.jvm  max kotlin.math  min kotlin.math  pow kotlin.math  JvmOverloads 
kotlin.ranges  Paint 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  JvmOverloads kotlin.sequences  Paint kotlin.sequences  
coerceAtLeast kotlin.sequences  coerceAtMost kotlin.sequences  JvmOverloads kotlin.text  Paint kotlin.text  
coerceAtLeast kotlin.text  coerceAtMost kotlin.text  lifecycleScope androidx.lifecycle  Boolean com.example.word.data.database  DatabaseInitializer com.example.word.data.database  Int com.example.word.data.database  String com.example.word.data.database  VocabularyJsonLoader com.example.word.data.database  VocabularyMetadata com.example.word.data.database  Boolean 2com.example.word.data.database.DatabaseInitializer  Context 2com.example.word.data.database.DatabaseInitializer  String 2com.example.word.data.database.DatabaseInitializer  WordDatabase 2com.example.word.data.database.DatabaseInitializer  Context 3com.example.word.data.database.VocabularyJsonLoader  
EssayTemplate 3com.example.word.data.database.VocabularyJsonLoader  List 3com.example.word.data.database.VocabularyJsonLoader  Phrase 3com.example.word.data.database.VocabularyJsonLoader  String 3com.example.word.data.database.VocabularyJsonLoader  VocabularyMetadata 3com.example.word.data.database.VocabularyJsonLoader  Word 3com.example.word.data.database.VocabularyJsonLoader  Int 1com.example.word.data.database.VocabularyMetadata  String 1com.example.word.data.database.VocabularyMetadata  IOException java.io  withContext kotlinx.coroutines  
JSONObject org.json  AudioAttributes 
android.media  MediaPlayer 
android.media  Log android.util  
TTSManager androidx.fragment.app.Fragment  
TTSManager 1com.example.word.ui.vocabulary.VocabularyFragment  	ByteArray com.example.word.utils  CoroutineScope com.example.word.utils  Dispatchers com.example.word.utils  OkHttpClient com.example.word.utils  OnlineTTSHelper com.example.word.utils  
SupervisorJob com.example.word.utils  
TTSManager com.example.word.utils  Unit com.example.word.utils  invoke com.example.word.utils  java com.example.word.utils  Boolean &com.example.word.utils.OnlineTTSHelper  	ByteArray &com.example.word.utils.OnlineTTSHelper  Context &com.example.word.utils.OnlineTTSHelper  OkHttpClient &com.example.word.utils.OnlineTTSHelper  String &com.example.word.utils.OnlineTTSHelper  Unit &com.example.word.utils.OnlineTTSHelper  getJAVA &com.example.word.utils.OnlineTTSHelper  getJava &com.example.word.utils.OnlineTTSHelper  java &com.example.word.utils.OnlineTTSHelper  Boolean !com.example.word.utils.TTSManager  	ByteArray !com.example.word.utils.TTSManager  Context !com.example.word.utils.TTSManager  CoroutineScope !com.example.word.utils.TTSManager  Dispatchers !com.example.word.utils.TTSManager  Int !com.example.word.utils.TTSManager  MediaPlayer !com.example.word.utils.TTSManager  OkHttpClient !com.example.word.utils.TTSManager  String !com.example.word.utils.TTSManager  
SupervisorJob !com.example.word.utils.TTSManager  TextToSpeech !com.example.word.utils.TTSManager  Unit !com.example.word.utils.TTSManager  invoke !com.example.word.utils.TTSManager  Boolean +com.example.word.utils.TTSManager.Companion  	ByteArray +com.example.word.utils.TTSManager.Companion  Context +com.example.word.utils.TTSManager.Companion  CoroutineScope +com.example.word.utils.TTSManager.Companion  Dispatchers +com.example.word.utils.TTSManager.Companion  Int +com.example.word.utils.TTSManager.Companion  MediaPlayer +com.example.word.utils.TTSManager.Companion  OkHttpClient +com.example.word.utils.TTSManager.Companion  String +com.example.word.utils.TTSManager.Companion  
SupervisorJob +com.example.word.utils.TTSManager.Companion  TextToSpeech +com.example.word.utils.TTSManager.Companion  Unit +com.example.word.utils.TTSManager.Companion  invoke +com.example.word.utils.TTSManager.Companion  CoroutineScope 	java.lang  Dispatchers 	java.lang  OkHttpClient 	java.lang  
SupervisorJob 	java.lang  java 	java.lang  CoroutineScope 	java.util  Dispatchers 	java.util  OkHttpClient 	java.util  
SupervisorJob 	java.util  invoke 	java.util  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  	ByteArray kotlin  CoroutineScope kotlin  Dispatchers kotlin  OkHttpClient kotlin  
SupervisorJob kotlin  java kotlin  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  OkHttpClient kotlin.annotation  
SupervisorJob kotlin.annotation  java kotlin.annotation  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  OkHttpClient kotlin.collections  
SupervisorJob kotlin.collections  java kotlin.collections  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  OkHttpClient kotlin.comparisons  
SupervisorJob kotlin.comparisons  java kotlin.comparisons  CoroutineContext kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  OkHttpClient 	kotlin.io  
SupervisorJob 	kotlin.io  java 	kotlin.io  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  OkHttpClient 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  java 
kotlin.jvm  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  OkHttpClient 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  java 
kotlin.ranges  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  OkHttpClient kotlin.sequences  
SupervisorJob kotlin.sequences  java kotlin.sequences  CoroutineScope kotlin.text  Dispatchers kotlin.text  OkHttpClient kotlin.text  
SupervisorJob kotlin.text  java kotlin.text  CompletableJob kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  OkHttpClient kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  invoke kotlinx.coroutines  java kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  CoroutineScope okhttp3  Dispatchers okhttp3  OkHttpClient okhttp3  
SupervisorJob okhttp3  invoke okhttp3  java okhttp3  Builder okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  invoke okhttp3.OkHttpClient.Companion  HttpUrl okhttp3  	Companion okhttp3.HttpUrl  	toHttpUrl okhttp3.HttpUrl.Companion  FragmentHomeBinding androidx.fragment.app.Fragment  GameificationManager androidx.fragment.app.Fragment  findNavController androidx.navigation.fragment  
addExperience com.example.word.data.dao  addStudyTime com.example.word.data.dao  Boolean )com.example.word.data.dao.UserProgressDao  String )com.example.word.data.dao.UserProgressDao  
addExperience )com.example.word.data.dao.UserProgressDao  addStudyTime )com.example.word.data.dao.UserProgressDao  getADDExperience )com.example.word.data.dao.UserProgressDao  getADDStudyTime )com.example.word.data.dao.UserProgressDao  getAddExperience )com.example.word.data.dao.UserProgressDao  getAddStudyTime )com.example.word.data.dao.UserProgressDao  
addExperience com.example.word.data.entities  addStudyTime com.example.word.data.entities  Boolean +com.example.word.data.entities.UserProgress  String +com.example.word.data.entities.UserProgress  
addExperience  com.example.word.data.repository  addStudyTime  com.example.word.data.repository  
addExperience /com.example.word.data.repository.WordRepository  addStudyTime /com.example.word.data.repository.WordRepository  getADDExperience /com.example.word.data.repository.WordRepository  getADDStudyTime /com.example.word.data.repository.WordRepository  getAddExperience /com.example.word.data.repository.WordRepository  getAddStudyTime /com.example.word.data.repository.WordRepository  FragmentHomeBinding com.example.word.databinding  HomeFragment com.example.word.ui.home  Int com.example.word.ui.home  String com.example.word.ui.home  getValue com.example.word.ui.home  provideDelegate com.example.word.ui.home  
viewModels com.example.word.ui.home  Bundle %com.example.word.ui.home.HomeFragment  FragmentHomeBinding %com.example.word.ui.home.HomeFragment  GameificationManager %com.example.word.ui.home.HomeFragment  Int %com.example.word.ui.home.HomeFragment  LayoutInflater %com.example.word.ui.home.HomeFragment  String %com.example.word.ui.home.HomeFragment  View %com.example.word.ui.home.HomeFragment  	ViewGroup %com.example.word.ui.home.HomeFragment  
WordViewModel %com.example.word.ui.home.HomeFragment  _binding %com.example.word.ui.home.HomeFragment  getGETValue %com.example.word.ui.home.HomeFragment  getGetValue %com.example.word.ui.home.HomeFragment  getPROVIDEDelegate %com.example.word.ui.home.HomeFragment  getProvideDelegate %com.example.word.ui.home.HomeFragment  
getVIEWModels %com.example.word.ui.home.HomeFragment  getValue %com.example.word.ui.home.HomeFragment  
getViewModels %com.example.word.ui.home.HomeFragment  provideDelegate %com.example.word.ui.home.HomeFragment  
viewModels %com.example.word.ui.home.HomeFragment  
addExperience 	java.lang  addStudyTime 	java.lang  
addExperience kotlin  addStudyTime kotlin  
addExperience kotlin.annotation  addStudyTime kotlin.annotation  
addExperience kotlin.collections  addStudyTime kotlin.collections  
addExperience kotlin.comparisons  addStudyTime kotlin.comparisons  
addExperience 	kotlin.io  addStudyTime 	kotlin.io  
addExperience 
kotlin.jvm  addStudyTime 
kotlin.jvm  
addExperience 
kotlin.ranges  addStudyTime 
kotlin.ranges  
addExperience kotlin.sequences  addStudyTime kotlin.sequences  
addExperience kotlin.text  addStudyTime kotlin.text  GameificationManager #androidx.lifecycle.AndroidViewModel  GameificationManager androidx.lifecycle.ViewModel  GameificationManager .com.example.word.ui.progress.ProgressViewModel  AchievementCategory +com.example.word.utils.GameificationManager  Boolean +com.example.word.utils.GameificationManager  AchievementCategory 7com.example.word.utils.GameificationManager.Achievement  Boolean 7com.example.word.utils.GameificationManager.Achievement  Int 7com.example.word.utils.GameificationManager.Achievement  String 7com.example.word.utils.GameificationManager.Achievement  ExperienceType :com.example.word.utils.GameificationManager.ExperienceType  Int :com.example.word.utils.GameificationManager.ExperienceType  Int 6com.example.word.utils.GameificationManager.StudyStats  Int 5com.example.word.utils.GameificationManager.UserLevel  String 5com.example.word.utils.GameificationManager.UserLevel  Achievement 	java.lang  Achievement kotlin  Achievement kotlin.annotation  Achievement kotlin.collections  Achievement kotlin.comparisons  Achievement 	kotlin.io  Achievement 
kotlin.jvm  Achievement 
kotlin.ranges  Achievement kotlin.sequences  Achievement kotlin.text  GameificationManager 2androidx.recyclerview.widget.DiffUtil.ItemCallback  GameificationManager (androidx.recyclerview.widget.ListAdapter  GameificationManager 1androidx.recyclerview.widget.RecyclerView.Adapter  GameificationManager 4androidx.recyclerview.widget.RecyclerView.ViewHolder  GameificationManager /com.example.word.ui.progress.AchievementAdapter  GameificationManager Gcom.example.word.ui.progress.AchievementAdapter.AchievementDiffCallback  GameificationManager Ecom.example.word.ui.progress.AchievementAdapter.AchievementViewHolder  Achievement 5com.example.word.utils.GameificationManager.Companion  Int 5com.example.word.utils.GameificationManager.Companion  List 5com.example.word.utils.GameificationManager.Companion  	Throwable android.app.Application  	Throwable android.content.Context  	Throwable android.content.ContextWrapper  PackageManager android.content.pm  	Throwable com.example.word  WordApplication com.example.word  	Throwable  com.example.word.WordApplication  	Throwable *com.example.word.WordApplication.Companion  Bundle /com.example.word.ui.home.HomeFragment.Companion  FragmentHomeBinding /com.example.word.ui.home.HomeFragment.Companion  GameificationManager /com.example.word.ui.home.HomeFragment.Companion  Int /com.example.word.ui.home.HomeFragment.Companion  LayoutInflater /com.example.word.ui.home.HomeFragment.Companion  String /com.example.word.ui.home.HomeFragment.Companion  View /com.example.word.ui.home.HomeFragment.Companion  	ViewGroup /com.example.word.ui.home.HomeFragment.Companion  
WordViewModel /com.example.word.ui.home.HomeFragment.Companion  getGETValue /com.example.word.ui.home.HomeFragment.Companion  getGetValue /com.example.word.ui.home.HomeFragment.Companion  getPROVIDEDelegate /com.example.word.ui.home.HomeFragment.Companion  getProvideDelegate /com.example.word.ui.home.HomeFragment.Companion  getValue /com.example.word.ui.home.HomeFragment.Companion  provideDelegate /com.example.word.ui.home.HomeFragment.Companion  
viewModels /com.example.word.ui.home.HomeFragment.Companion  Context 6com.example.word.utils.StudyReminderReceiver.Companion  Intent 6com.example.word.utils.StudyReminderReceiver.Companion  	Throwable kotlin  
NavController androidx.navigation  Boolean ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  Bundle ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  FragmentVocabularyBinding ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  LayoutInflater ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  String ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
TTSManager ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  View ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  	ViewGroup ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  WordAdapter ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
WordViewModel ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getGETValue ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getGetValue ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getPROVIDEDelegate ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getProvideDelegate ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  getValue ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  provideDelegate ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  
viewModels ;com.example.word.ui.vocabulary.VocabularyFragment.Companion  Any com.example.word.utils  CrashPrevention com.example.word.utils  ErrorHandler com.example.word.utils  	Exception com.example.word.utils  Thread com.example.word.utils  	Throwable com.example.word.utils  Any &com.example.word.utils.CrashPrevention  Boolean &com.example.word.utils.CrashPrevention  Context &com.example.word.utils.CrashPrevention  	Exception &com.example.word.utils.CrashPrevention  Fragment &com.example.word.utils.CrashPrevention  Int &com.example.word.utils.CrashPrevention  
NavController &com.example.word.utils.CrashPrevention  String &com.example.word.utils.CrashPrevention  Unit &com.example.word.utils.CrashPrevention  safeUIOperation &com.example.word.utils.CrashPrevention  withSafeContext &com.example.word.utils.CrashPrevention  Context #com.example.word.utils.ErrorHandler  
CrashStats #com.example.word.utils.ErrorHandler  Int #com.example.word.utils.ErrorHandler  Long #com.example.word.utils.ErrorHandler  String #com.example.word.utils.ErrorHandler  Thread #com.example.word.utils.ErrorHandler  	Throwable #com.example.word.utils.ErrorHandler  Int .com.example.word.utils.ErrorHandler.CrashStats  Long .com.example.word.utils.ErrorHandler.CrashStats  File java.io  
FileWriter java.io  Thread 	java.lang  Thread 	java.util  Any kotlin  	Exception kotlin  Thread kotlin  	Exception kotlin.annotation  Thread kotlin.annotation  	Exception kotlin.collections  Thread kotlin.collections  	Exception kotlin.comparisons  Thread kotlin.comparisons  	Exception 	kotlin.io  Thread 	kotlin.io  	Exception 
kotlin.jvm  Thread 
kotlin.jvm  	Exception 
kotlin.ranges  Thread 
kotlin.ranges  	Exception kotlin.sequences  Thread kotlin.sequences  	Exception kotlin.text  Thread kotlin.text  
JSONException org.json                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              