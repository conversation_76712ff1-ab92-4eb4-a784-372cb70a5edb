package com.example.word.utils;

/**
 * TTS调试助手
 * 用于诊断TTS问题
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00062\u0006\u0010\u0007\u001a\u00020\bJ(\u0010\t\u001a\u00020\n2\u0006\u0010\u0007\u001a\u00020\b2\u0018\u0010\u000b\u001a\u0014\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\n0\fJ\u000e\u0010\u000e\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/example/word/utils/TTSDebugHelper;", "", "()V", "TAG", "", "checkSystemTTSSettings", "", "context", "Landroid/content/Context;", "checkTTSAvailability", "", "callback", "Lkotlin/Function2;", "", "getTTSEngineInfo", "app_debug"})
public final class TTSDebugHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "TTSDebugHelper";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.TTSDebugHelper INSTANCE = null;
    
    private TTSDebugHelper() {
        super();
    }
    
    /**
     * 检查TTS可用性
     */
    public final void checkTTSAvailability(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 获取TTS引擎信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTTSEngineInfo(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 检查系统TTS设置
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> checkSystemTTSSettings(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
}