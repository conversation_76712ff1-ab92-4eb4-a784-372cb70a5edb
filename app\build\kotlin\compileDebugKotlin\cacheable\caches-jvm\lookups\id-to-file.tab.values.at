/ Header Record For PersistentHashMapValueStorage3 2app/src/main/java/com/example/word/MainActivity.kt@ ?app/src/main/java/com/example/word/data/dao/EssayTemplateDao.kt9 8app/src/main/java/com/example/word/data/dao/PhraseDao.kt? >app/src/main/java/com/example/word/data/dao/StudySessionDao.kt? >app/src/main/java/com/example/word/data/dao/UserProgressDao.kt7 6app/src/main/java/com/example/word/data/dao/WordDao.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktK Japp/src/main/java/com/example/word/data/database/VocabularyDataProvider.ktI Happ/src/main/java/com/example/word/data/database/VocabularyJsonLoader.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.kt@ ?app/src/main/java/com/example/word/data/entities/Achievement.ktB Aapp/src/main/java/com/example/word/data/entities/EssayTemplate.kt; :app/src/main/java/com/example/word/data/entities/Phrase.ktA @app/src/main/java/com/example/word/data/entities/StudySession.ktA @app/src/main/java/com/example/word/data/entities/UserProgress.kt9 8app/src/main/java/com/example/word/data/entities/Word.ktE Dapp/src/main/java/com/example/word/data/repository/WordRepository.kt= <app/src/main/java/com/example/word/ui/essay/EssayFragment.ktD Capp/src/main/java/com/example/word/ui/essay/EssayTemplateAdapter.kt> =app/src/main/java/com/example/word/ui/essay/EssayViewModel.kt; :app/src/main/java/com/example/word/ui/home/<USER>/src/main/java/com/example/word/ui/phrases/PhraseAdapter.ktA @app/src/main/java/com/example/word/ui/phrases/PhrasesFragment.ktE Dapp/src/main/java/com/example/word/ui/progress/AchievementAdapter.ktC Bapp/src/main/java/com/example/word/ui/progress/ProgressFragment.ktD Capp/src/main/java/com/example/word/ui/progress/ProgressViewModel.kt; :app/src/main/java/com/example/word/ui/quiz/QuizFragment.kt< ;app/src/main/java/com/example/word/ui/quiz/QuizViewModel.ktC Bapp/src/main/java/com/example/word/ui/settings/SettingsFragment.ktC Bapp/src/main/java/com/example/word/ui/viewmodel/PhraseViewModel.ktA @app/src/main/java/com/example/word/ui/viewmodel/WordViewModel.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt@ ?app/src/main/java/com/example/word/ui/vocabulary/WordAdapter.kt8 7app/src/main/java/com/example/word/utils/ChartHelper.ktA @app/src/main/java/com/example/word/utils/GameificationManager.kt@ ?app/src/main/java/com/example/word/utils/NotificationManager.kt< ;app/src/main/java/com/example/word/utils/OnlineTTSHelper.ktF Eapp/src/main/java/com/example/word/utils/SpacedRepetitionAlgorithm.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt3 2app/src/main/java/com/example/word/MainActivity.kt6 5app/src/main/java/com/example/word/WordApplication.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.kt; :app/src/main/java/com/example/word/ui/home/<USER>/src/main/java/com/example/word/utils/NotificationManager.ktB Aapp/src/main/java/com/example/word/utils/StudyReminderReceiver.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt6 5app/src/main/java/com/example/word/WordApplication.ktI Happ/src/main/java/com/example/word/data/database/VocabularyJsonLoader.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt< ;app/src/main/java/com/example/word/utils/CrashPrevention.kt9 8app/src/main/java/com/example/word/utils/ErrorHandler.ktB Aapp/src/main/java/com/example/word/utils/StudyReminderReceiver.ktD Capp/src/main/java/com/example/word/ui/essay/EssayTemplateAdapter.kt@ ?app/src/main/java/com/example/word/utils/NotificationManager.kt3 2app/src/main/java/com/example/word/MainActivity.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktA @app/src/main/java/com/example/word/data/database/WordDatabase.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktA @app/src/main/java/com/example/word/data/parser/TextDataParser.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt; :app/src/main/java/com/example/word/utils/TTSDebugHelper.kt7 6app/src/main/java/com/example/word/utils/TTSManager.kt@ ?app/src/main/java/com/example/word/data/dao/EssayTemplateDao.kt9 8app/src/main/java/com/example/word/data/dao/PhraseDao.kt7 6app/src/main/java/com/example/word/data/dao/WordDao.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt@ ?app/src/main/java/com/example/word/utils/DatabaseDebugHelper.kt= <app/src/main/java/com/example/word/utils/SimpleTTSManager.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktC Bapp/src/main/java/com/example/word/data/organizer/DataOrganizer.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt@ ?app/src/main/java/com/example/word/utils/DatabaseDebugHelper.kt? >app/src/main/java/com/example/word/utils/TTSFallbackManager.kt3 2app/src/main/java/com/example/word/MainActivity.ktH Gapp/src/main/java/com/example/word/data/database/DatabaseInitializer.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt= <app/src/main/java/com/example/word/utils/AppHealthChecker.kt3 2app/src/main/java/com/example/word/MainActivity.ktG Fapp/src/main/java/com/example/word/ui/vocabulary/VocabularyFragment.kt9 8app/src/main/java/com/example/word/utils/AppOptimizer.kt< ;app/src/main/java/com/example/word/utils/OnlineTTSHelper.kt