package com.example.word.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.word.data.entities.Phrase

/**
 * 短语数据访问对象
 */
@Dao
interface PhraseDao {
    
    // 获取所有短语
    @Query("SELECT * FROM phrases ORDER BY frequency DESC")
    fun getAllPhrases(): LiveData<List<Phrase>>
    
    // 根据ID获取短语
    @Query("SELECT * FROM phrases WHERE id = :id")
    suspend fun getPhraseById(id: Long): Phrase?
    
    // 搜索短语
    @Query("SELECT * FROM phrases WHERE phrase LIKE '%' || :query || '%' OR translation LIKE '%' || :query || '%' ORDER BY frequency DESC")
    fun searchPhrases(query: String): LiveData<List<Phrase>>
    
    // 根据类型获取短语
    @Query("SELECT * FROM phrases WHERE type = :type ORDER BY frequency DESC")
    fun getPhrasesByType(type: String): LiveData<List<Phrase>>
    
    // 根据分类获取短语
    @Query("SELECT * FROM phrases WHERE category = :category ORDER BY frequency DESC")
    fun getPhrasesByCategory(category: String): LiveData<List<Phrase>>
    
    // 根据难度获取短语
    @Query("SELECT * FROM phrases WHERE difficultyLevel = :level ORDER BY frequency DESC")
    fun getPhrasesByDifficulty(level: Int): LiveData<List<Phrase>>
    
    // 获取收藏的短语
    @Query("SELECT * FROM phrases WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC")
    fun getBookmarkedPhrases(): LiveData<List<Phrase>>
    
    // 获取随机短语
    @Query("SELECT * FROM phrases ORDER BY RANDOM() LIMIT :count")
    suspend fun getRandomPhrases(count: Int): List<Phrase>
    
    // 获取所有短语类型
    @Query("SELECT DISTINCT type FROM phrases")
    suspend fun getAllPhraseTypes(): List<String>
    
    // 获取所有分类
    @Query("SELECT DISTINCT category FROM phrases")
    suspend fun getAllCategories(): List<String>

    // 获取总短语数
    @Query("SELECT COUNT(*) FROM phrases")
    suspend fun getPhraseCount(): Int
    
    // 插入短语
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhrase(phrase: Phrase): Long
    
    // 批量插入短语
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhrases(phrases: List<Phrase>)
    
    // 更新短语
    @Update
    suspend fun updatePhrase(phrase: Phrase)
    
    // 更新收藏状态
    @Query("UPDATE phrases SET isBookmarked = :isBookmarked WHERE id = :id")
    suspend fun updateBookmarkStatus(id: Long, isBookmarked: Boolean)
    
    // 更新学习统计
    @Query("UPDATE phrases SET studyCount = studyCount + 1, lastStudiedTime = :time WHERE id = :id")
    suspend fun updateStudyStats(id: Long, time: Long)
    
    // 删除短语
    @Delete
    suspend fun deletePhrase(phrase: Phrase)
    
    // 清空所有短语
    @Query("DELETE FROM phrases")
    suspend fun deleteAllPhrases()
}
