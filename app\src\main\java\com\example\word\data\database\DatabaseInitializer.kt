package com.example.word.data.database

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.example.word.data.parser.TextDataParser
import com.example.word.data.organizer.DataOrganizer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 数据库初始化器
 * 负责在应用首次启动时初始化数据库数据
 */
object DatabaseInitializer {

    private const val TAG = "DatabaseInitializer"
    private const val PREFS_NAME = "database_init"
    private const val KEY_INITIALIZED = "is_initialized"
    private const val KEY_VERSION = "data_version"
    
    /**
     * 初始化数据库
     */
    suspend fun initializeDatabase(context: Context) {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Starting database initialization")

                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

                // 检查数据库schema版本
                val dbSchemaVersion = prefs.getInt("db_schema_version", 1)
                val currentSchemaVersion = 2 // 当前数据库版本

                // 如果schema版本不匹配，重置数据库
                if (dbSchemaVersion != currentSchemaVersion) {
                    Log.d(TAG, "Database schema version mismatch. Resetting database.")
                    WordDatabase.resetDatabase(context)

                    // 清除初始化状态
                    prefs.edit()
                        .putBoolean(KEY_INITIALIZED, false)
                        .putInt("db_schema_version", currentSchemaVersion)
                        .apply()
                }

                val database = WordDatabase.getDatabase(context)

                // 检查是否需要初始化或更新数据
                val isInitialized = prefs.getBoolean(KEY_INITIALIZED, false)
                val currentVersion = prefs.getString(KEY_VERSION, "0.0")
                val metadata = VocabularyJsonLoader.getVocabularyMetadata(context)
                val latestVersion = "2.0" // 强制更新到新版本以使用DataOrganizer

                Log.d(TAG, "Current version: $currentVersion, Latest version: $latestVersion")

                if (!isInitialized || currentVersion != latestVersion) {
                    Log.d(TAG, "Database needs initialization or update")

                    // 清除旧数据（如果存在）
                    if (isInitialized) {
                        Log.d(TAG, "Clearing old data")
                        clearOldData(database)
                    }

                    // 加载新数据
                    Log.d(TAG, "Loading vocabulary data")
                    loadVocabularyData(context, database)

                    // 更新初始化状态
                    prefs.edit()
                        .putBoolean(KEY_INITIALIZED, true)
                        .putString(KEY_VERSION, latestVersion)
                        .putInt("db_schema_version", currentSchemaVersion)
                        .apply()

                    Log.d(TAG, "Database initialization completed successfully")
                } else {
                    Log.d(TAG, "Database already initialized with latest version")
                }

            } catch (e: Exception) {
                Log.e(TAG, "Database initialization failed", e)

                // 如果初始化失败，尝试重置数据库
                try {
                    Log.w(TAG, "Attempting database reset due to initialization failure")
                    WordDatabase.resetDatabase(context)

                    // 清除初始化状态，下次启动时重新初始化
                    val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                    prefs.edit()
                        .putBoolean(KEY_INITIALIZED, false)
                        .apply()

                } catch (resetException: Exception) {
                    Log.e(TAG, "Database reset also failed", resetException)
                }

                throw e // 重新抛出原始异常
            }
        }
    }
    
    /**
     * 加载词汇数据
     */
    private suspend fun loadVocabularyData(context: Context, database: WordDatabase) {
        try {
            Log.d(TAG, "Loading vocabulary data using DataOrganizer")

            // 使用DataOrganizer整理1.txt数据
            val organizedData = DataOrganizer.organizeAllData(context)
            val wordsFromText = organizedData.words
            val phrasesFromText = organizedData.phrases
            val essayTemplatesFromText = organizedData.essayTemplates

            // 确定最终使用的数据
            val finalWords = when {
                wordsFromText.isNotEmpty() -> {
                    Log.d(TAG, "Using ${wordsFromText.size} words from text file")
                    wordsFromText
                }
                else -> {
                    Log.d(TAG, "Falling back to JSON/default data for words")
                    val jsonWords = VocabularyJsonLoader.loadWordsFromJson(context)
                    if (jsonWords.isNotEmpty()) jsonWords else VocabularyDataProvider.getCET4Words()
                }
            }

            val finalPhrases = when {
                phrasesFromText.isNotEmpty() -> {
                    Log.d(TAG, "Using ${phrasesFromText.size} phrases from text file")
                    phrasesFromText
                }
                else -> {
                    Log.d(TAG, "Falling back to JSON/default data for phrases")
                    val jsonPhrases = VocabularyJsonLoader.loadPhrasesFromJson(context)
                    if (jsonPhrases.isNotEmpty()) jsonPhrases else VocabularyDataProvider.getCET4Phrases()
                }
            }

            val finalTemplates = when {
                essayTemplatesFromText.isNotEmpty() -> {
                    Log.d(TAG, "Using ${essayTemplatesFromText.size} essay templates from text file")
                    essayTemplatesFromText
                }
                else -> {
                    Log.d(TAG, "Falling back to JSON/default data for essay templates")
                    val jsonTemplates = VocabularyJsonLoader.loadEssayTemplatesFromJson(context)
                    if (jsonTemplates.isNotEmpty()) jsonTemplates else VocabularyDataProvider.getEssayTemplates()
                }
            }

            // 插入数据到数据库
            database.wordDao().insertWords(finalWords)
            database.phraseDao().insertPhrases(finalPhrases)
            database.essayTemplateDao().insertTemplates(finalTemplates)

            // 创建默认用户进度记录
            val existingProgress = database.userProgressDao().getUserProgressSync()
            if (existingProgress == null) {
                val defaultProgress = com.example.word.data.entities.UserProgress()
                database.userProgressDao().insertOrUpdateUserProgress(defaultProgress)
            }

            Log.d(TAG, "Vocabulary data loading completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Error loading vocabulary data", e)
            // 如果出错，使用默认数据
            database.wordDao().insertWords(VocabularyDataProvider.getCET4Words())
            database.phraseDao().insertPhrases(VocabularyDataProvider.getCET4Phrases())
            database.essayTemplateDao().insertTemplates(VocabularyDataProvider.getEssayTemplates())
        }
    }
    
    /**
     * 清除旧数据
     */
    private suspend fun clearOldData(database: WordDatabase) {
        database.wordDao().deleteAllWords()
        database.phraseDao().deleteAllPhrases()
        database.essayTemplateDao().deleteAllTemplates()
    }
    
    /**
     * 检查数据库是否已初始化
     */
    fun isDatabaseInitialized(context: Context): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(KEY_INITIALIZED, false)
    }
    
    /**
     * 获取当前数据版本
     */
    fun getCurrentDataVersion(context: Context): String {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getString(KEY_VERSION, "0.0") ?: "0.0"
    }
    
    /**
     * 强制重新加载数据（用于调试）
     */
    suspend fun forceReloadData(context: Context) {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "Force reloading data from 1.txt")

                // 重置数据库
                WordDatabase.resetDatabase(context)

                // 清除初始化状态
                val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                prefs.edit().clear().apply()

                // 重新初始化
                initializeDatabase(context)

                Log.d(TAG, "Force reload completed")

            } catch (e: Exception) {
                Log.e(TAG, "Error in force reload", e)
                throw e
            }
        }
    }

    /**
     * 强制重新初始化数据库
     */
    suspend fun forceReinitialize(context: Context) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit()
            .putBoolean(KEY_INITIALIZED, false)
            .putString(KEY_VERSION, "0.0")
            .apply()

        initializeDatabase(context)
    }
}
