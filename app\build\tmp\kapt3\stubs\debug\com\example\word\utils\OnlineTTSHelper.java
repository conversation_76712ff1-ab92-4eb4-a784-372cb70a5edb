package com.example.word.utils;

/**
 * 在线TTS助手
 * 专门用于测试和调用在线TTS API
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002JD\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u00042\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\r\u001a\u00020\u00042 \u0010\u000e\u001a\u001c\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0006\u0012\u0004\u0018\u00010\u0011\u0012\u0004\u0012\u00020\n0\u000fJ\u001a\u0010\u0012\u001a\u00020\n2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\n0\u0013J(\u0010\u0014\u001a\u00020\n2\u0006\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\n0\u0017R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/word/utils/OnlineTTSHelper;", "", "()V", "API_KEY", "", "TAG", "TTS_API_URL", "httpClient", "Lokhttp3/OkHttpClient;", "getTTSAudio", "", "text", "language", "speed", "callback", "Lkotlin/Function3;", "", "", "simpleAPITest", "Lkotlin/Function1;", "testAPIConnection", "context", "Landroid/content/Context;", "Lkotlin/Function2;", "app_debug"})
public final class OnlineTTSHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "OnlineTTSHelper";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7";
    @org.jetbrains.annotations.NotNull()
    private static final okhttp3.OkHttpClient httpClient = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.OnlineTTSHelper INSTANCE = null;
    
    private OnlineTTSHelper() {
        super();
    }
    
    /**
     * 测试API连接
     */
    public final void testAPIConnection(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 获取TTS音频
     */
    public final void getTTSAudio(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    java.lang.String speed, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.lang.String, ? super byte[], kotlin.Unit> callback) {
    }
    
    /**
     * 简单的API测试方法
     */
    public final void simpleAPITest(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
}