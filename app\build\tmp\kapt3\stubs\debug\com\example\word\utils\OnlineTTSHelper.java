package com.example.word.utils;

/**
 * 在线TTS助手
 * 专门用于测试和调用在线TTS API
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\'B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0010\u001a\u00020\u0011J>\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00042\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00110\u0018JB\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00042\b\u0010\u0014\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0015\u001a\u00020\u00162\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00110\u0018H\u0082@\u00a2\u0006\u0002\u0010\u001bJ\u0006\u0010\u001c\u001a\u00020\u0004J,\u0010\u001d\u001a\u00020\u00112$\u0010\u0017\u001a \u0012\u0004\u0012\u00020\u0019\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00110\u001eJ\b\u0010\u001f\u001a\u0004\u0018\u00010\nJ\u0016\u0010 \u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010!\u001a\u00020\u0004H\u0002J\u001a\u0010\"\u001a\u00020\u00112\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00110#J(\u0010$\u001a\u00020\u00112\u0006\u0010%\u001a\u00020&2\u0018\u0010\u0017\u001a\u0014\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00110\u0018R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R.\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t2\u000e\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/example/word/utils/OnlineTTSHelper;", "", "()V", "API_KEY", "", "TAG", "TTS_API_URL", "VOICES_API_URL", "<set-?>", "", "Lcom/example/word/utils/OnlineTTSHelper$TTSVoice;", "cachedVoices", "getCachedVoices", "()Ljava/util/List;", "httpClient", "Lokhttp3/OkHttpClient;", "clearCache", "", "generateTTSAudio", "text", "voice", "speed", "", "callback", "Lkotlin/Function2;", "", "generateTTSAudioInternal", "(Ljava/lang/String;Lcom/example/word/utils/OnlineTTSHelper$TTSVoice;FLkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAPIStatusSummary", "getAvailableVoices", "Lkotlin/Function3;", "getRecommendedEnglishVoice", "parseVoicesResponse", "responseBody", "simpleAPITest", "Lkotlin/Function1;", "testAPIConnection", "context", "Landroid/content/Context;", "TTSVoice", "app_debug"})
public final class OnlineTTSHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "OnlineTTSHelper";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String VOICES_API_URL = "https://api.hewoyi.com/api/ai/audio/speech";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TTS_API_URL = "https://api.hewoyi.com/api/ai/audio/speech";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_KEY = "QrK0eMRKAkaq7XIrovZSR3A5i7";
    @org.jetbrains.annotations.NotNull()
    private static final okhttp3.OkHttpClient httpClient = null;
    @org.jetbrains.annotations.Nullable()
    private static java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> cachedVoices;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.word.utils.OnlineTTSHelper INSTANCE = null;
    
    private OnlineTTSHelper() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> getCachedVoices() {
        return null;
    }
    
    /**
     * 获取可用的语音列表
     */
    public final void getAvailableVoices(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Boolean, ? super java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice>, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 解析语音列表响应
     */
    private final java.util.List<com.example.word.utils.OnlineTTSHelper.TTSVoice> parseVoicesResponse(java.lang.String responseBody) {
        return null;
    }
    
    /**
     * 获取推荐的英语语音
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.word.utils.OnlineTTSHelper.TTSVoice getRecommendedEnglishVoice() {
        return null;
    }
    
    /**
     * 测试API连接
     */
    public final void testAPIConnection(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 生成TTS音频（使用推荐的语音）
     */
    public final void generateTTSAudio(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.Nullable()
    com.example.word.utils.OnlineTTSHelper.TTSVoice voice, float speed, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 内部TTS音频生成方法
     */
    private final java.lang.Object generateTTSAudioInternal(java.lang.String text, com.example.word.utils.OnlineTTSHelper.TTSVoice voice, float speed, kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 简单的API测试方法
     */
    public final void simpleAPITest(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * 获取API状态摘要
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAPIStatusSummary() {
        return null;
    }
    
    /**
     * 清除缓存的语音列表
     */
    public final void clearCache() {
    }
    
    /**
     * TTS语音数据类
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u000b\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\'\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\f2\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u00038F\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\tR\u0011\u0010\u000b\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\u000b\u0010\rR\u0011\u0010\u000e\u001a\u00020\f8F\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\t\u00a8\u0006\u001a"}, d2 = {"Lcom/example/word/utils/OnlineTTSHelper$TTSVoice;", "", "name", "", "language", "gender", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "displayName", "getDisplayName", "()Ljava/lang/String;", "getGender", "isChinese", "", "()Z", "isEnglish", "getLanguage", "getName", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class TTSVoice {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String name = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String language = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String gender = null;
        
        public TTSVoice(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String language, @org.jetbrains.annotations.NotNull()
        java.lang.String gender) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getLanguage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getGender() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        public final boolean isEnglish() {
            return false;
        }
        
        public final boolean isChinese() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.word.utils.OnlineTTSHelper.TTSVoice copy(@org.jetbrains.annotations.NotNull()
        java.lang.String name, @org.jetbrains.annotations.NotNull()
        java.lang.String language, @org.jetbrains.annotations.NotNull()
        java.lang.String gender) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}