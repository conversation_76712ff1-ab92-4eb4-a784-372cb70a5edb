(androidx.appcompat.app.AppCompatActivityandroidx.room.RoomDatabase#androidx.room.RoomDatabase.Callbackandroidx.fragment.app.Fragment(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback#androidx.lifecycle.AndroidViewModel androidx.viewbinding.ViewBindingkotlin.Enum.android.speech.tts.TextToSpeech.OnInitListenerandroid.view.View!android.content.BroadcastReceiverandroid.app.Application                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            