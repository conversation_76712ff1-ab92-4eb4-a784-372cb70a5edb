/ Header Record For PersistentHashMapValueStorage) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback$ #androidx.lifecycle.AndroidViewModel androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment$ #androidx.lifecycle.AndroidViewModel androidx.fragment.app.Fragment$ #androidx.lifecycle.AndroidViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment$ #androidx.lifecycle.AndroidViewModel$ #androidx.lifecycle.AndroidViewModel androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.view.View android.view.View kotlin.Enum kotlin.Enum" !android.content.BroadcastReceiver kotlin.Enum/ .android.speech.tts.TextToSpeech.OnInitListener android.app.Application) (androidx.appcompat.app.AppCompatActivity androidx.fragment.app.Fragment" !android.content.BroadcastReceiver/ .android.speech.tts.TextToSpeech.OnInitListener android.app.Application androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback androidx.fragment.app.Fragment" !android.content.BroadcastReceiver) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity androidx.room.RoomDatabase$ #androidx.room.RoomDatabase.Callback androidx.fragment.app.Fragment/ .android.speech.tts.TextToSpeech.OnInitListener androidx.fragment.app.Fragment