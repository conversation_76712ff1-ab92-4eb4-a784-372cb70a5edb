package com.example.word

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.example.word.databinding.ActivityMainBinding
import com.example.word.data.database.DatabaseInitializer
import com.example.word.utils.AppHealthChecker
import com.example.word.utils.AppOptimizer
import com.google.android.material.bottomnavigation.BottomNavigationView
import kotlinx.coroutines.launch
import android.widget.Toast

/**
 * CET-4词汇学习应用主界面
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupNavigation()
        performHealthCheckAndInit()
    }

    /**
     * 设置导航
     */
    private fun setupNavigation() {
        try {
            val navHostFragment = supportFragmentManager
                .findFragmentById(R.id.nav_host_fragment) as? NavHostFragment

            if (navHostFragment == null) {
                throw IllegalStateException("NavHostFragment not found")
            }

            val navController = navHostFragment.navController

            // 设置底部导航
            val bottomNav = findViewById<BottomNavigationView>(R.id.bottom_navigation)
            bottomNav?.setupWithNavController(navController)
                ?: throw IllegalStateException("BottomNavigationView not found")

        } catch (e: Exception) {
            e.printStackTrace()
            // 如果导航设置失败，关闭应用避免崩溃
            finish()
        }
    }

    /**
     * 执行健康检查并初始化
     */
    private fun performHealthCheckAndInit() {
        lifecycleScope.launch {
            try {
                // 执行启动优化检查
                val optimizationResult = AppOptimizer.optimizeAppStartup(this@MainActivity)

                if (optimizationResult.hasOptimizations) {
                    Toast.makeText(this@MainActivity, optimizationResult.summary, Toast.LENGTH_SHORT).show()
                }

                // 只在需要时执行健康检查
                if (optimizationResult.needsHealthCheck) {
                    val healthResult = AppHealthChecker.performHealthCheck(this@MainActivity)
                    Toast.makeText(this@MainActivity, healthResult.summary, Toast.LENGTH_LONG).show()

                    if (healthResult.isHealthy || healthResult.fixes.isNotEmpty()) {
                        AppOptimizer.markHealthCheckDone(this@MainActivity)
                    }
                }

                // 确保数据库已初始化
                initializeDatabase()

            } catch (e: Exception) {
                e.printStackTrace()
                Toast.makeText(this@MainActivity, "初始化失败: ${e.message}", Toast.LENGTH_LONG).show()
                // 即使出错，也尝试初始化数据库
                initializeDatabase()
            }
        }
    }

    /**
     * 初始化数据库
     */
    private fun initializeDatabase() {
        lifecycleScope.launch {
            var retryCount = 0
            val maxRetries = 3

            while (retryCount < maxRetries) {
                try {
                    DatabaseInitializer.initializeDatabase(this@MainActivity)
                    break // 成功初始化，退出循环

                } catch (e: Exception) {
                    retryCount++
                    e.printStackTrace()

                    if (retryCount >= maxRetries) {
                        // 最后一次尝试失败，显示错误信息
                        runOnUiThread {
                            try {
                                androidx.appcompat.app.AlertDialog.Builder(this@MainActivity)
                                    .setTitle("数据库初始化失败")
                                    .setMessage("应用数据库初始化失败，可能是由于数据结构变更。是否重置应用数据？")
                                    .setPositiveButton("重置数据") { _, _ ->
                                        resetAppData()
                                    }
                                    .setNegativeButton("退出应用") { _, _ ->
                                        finish()
                                    }
                                    .setCancelable(false)
                                    .show()
                            } catch (dialogException: Exception) {
                                // 如果连对话框都无法显示，显示Toast
                                try {
                                    android.widget.Toast.makeText(
                                        this@MainActivity,
                                        "数据库初始化失败，应用将退出",
                                        android.widget.Toast.LENGTH_LONG
                                    ).show()

                                    // 延迟退出，让用户看到Toast
                                    lifecycleScope.launch {
                                        kotlinx.coroutines.delay(3000)
                                        finish()
                                    }
                                } catch (toastException: Exception) {
                                    // 如果连Toast都无法显示，直接退出
                                    finish()
                                }
                            }
                        }
                    } else {
                        // 还有重试机会，等待一段时间后重试
                        kotlinx.coroutines.delay(1000)
                    }
                }
            }
        }
    }

    /**
     * 重置应用数据
     */
    private fun resetAppData() {
        lifecycleScope.launch {
            try {
                // 重置数据库
                com.example.word.data.database.WordDatabase.resetDatabase(this@MainActivity)

                // 清除SharedPreferences
                val prefs = getSharedPreferences("database_init", MODE_PRIVATE)
                prefs.edit().clear().apply()

                val appPrefs = getSharedPreferences("app_settings", MODE_PRIVATE)
                appPrefs.edit().clear().apply()

                runOnUiThread {
                    android.widget.Toast.makeText(
                        this@MainActivity,
                        "应用数据已重置，正在重新启动...",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()

                    // 重新启动应用
                    val intent = intent
                    finish()
                    startActivity(intent)
                }

            } catch (e: Exception) {
                e.printStackTrace()
                runOnUiThread {
                    android.widget.Toast.makeText(
                        this@MainActivity,
                        "重置失败，请手动清除应用数据",
                        android.widget.Toast.LENGTH_LONG
                    ).show()
                    finish()
                }
            }
        }
    }
}