package com.example.word

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.example.word.databinding.ActivityMainBinding
import com.example.word.data.database.DatabaseInitializer
import com.google.android.material.bottomnavigation.BottomNavigationView
import kotlinx.coroutines.launch

/**
 * CET-4词汇学习应用主界面
 */
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupNavigation()
        initializeDatabase()
    }

    /**
     * 设置导航
     */
    private fun setupNavigation() {
        try {
            val navHostFragment = supportFragmentManager
                .findFragmentById(R.id.nav_host_fragment) as? NavHostFragment

            if (navHostFragment == null) {
                throw IllegalStateException("NavHostFragment not found")
            }

            val navController = navHostFragment.navController

            // 设置底部导航
            val bottomNav = findViewById<BottomNavigationView>(R.id.bottom_navigation)
            bottomNav?.setupWithNavController(navController)
                ?: throw IllegalStateException("BottomNavigationView not found")

        } catch (e: Exception) {
            e.printStackTrace()
            // 如果导航设置失败，关闭应用避免崩溃
            finish()
        }
    }

    /**
     * 初始化数据库
     */
    private fun initializeDatabase() {
        lifecycleScope.launch {
            try {
                DatabaseInitializer.initializeDatabase(this@MainActivity)
            } catch (e: Exception) {
                e.printStackTrace()
                // 数据库初始化失败时的处理
                runOnUiThread {
                    try {
                        android.widget.Toast.makeText(
                            this@MainActivity,
                            "数据库初始化失败，应用可能无法正常工作",
                            android.widget.Toast.LENGTH_LONG
                        ).show()
                    } catch (toastException: Exception) {
                        // 如果连Toast都无法显示，记录错误但不崩溃
                        toastException.printStackTrace()
                    }
                }
            }
        }
    }
}