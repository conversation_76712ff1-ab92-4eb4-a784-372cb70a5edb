package com.example.word.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.word.data.dao.WordDao
import com.example.word.data.dao.PhraseDao
import com.example.word.data.dao.EssayTemplateDao
import com.example.word.data.dao.StudySessionDao
import com.example.word.data.dao.UserProgressDao
import com.example.word.data.entities.Word
import com.example.word.data.entities.Phrase
import com.example.word.data.entities.EssayTemplate
import com.example.word.data.entities.StudySession
import com.example.word.data.entities.UserProgress
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * CET-4词汇学习应用数据库
 */
@Database(
    entities = [
        Word::class,
        Phrase::class,
        EssayTemplate::class,
        StudySession::class,
        UserProgress::class
    ],
    version = 1,
    exportSchema = false
)
abstract class WordDatabase : RoomDatabase() {
    
    abstract fun wordDao(): WordDao
    abstract fun phraseDao(): PhraseDao
    abstract fun essayTemplateDao(): EssayTemplateDao
    abstract fun studySessionDao(): StudySessionDao
    abstract fun userProgressDao(): UserProgressDao
    
    companion object {
        @Volatile
        private var INSTANCE: WordDatabase? = null
        
        fun getDatabase(context: Context): WordDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    WordDatabase::class.java,
                    "word_database"
                )
                .addCallback(WordDatabaseCallback())
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
    
    /**
     * 数据库回调，用于预填充数据
     */
    private class WordDatabaseCallback : RoomDatabase.Callback() {
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            // 数据库创建后的回调，暂时留空
            // 数据初始化将在DatabaseInitializer中处理
        }
    }
}
