package com.example.word.data.database

import android.content.Context
import android.util.Log
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.word.data.dao.WordDao
import com.example.word.data.dao.PhraseDao
import com.example.word.data.dao.EssayTemplateDao
import com.example.word.data.dao.StudySessionDao
import com.example.word.data.dao.UserProgressDao
import com.example.word.data.entities.Word
import com.example.word.data.entities.Phrase
import com.example.word.data.entities.EssayTemplate
import com.example.word.data.entities.StudySession
import com.example.word.data.entities.UserProgress
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * CET-4词汇学习应用数据库
 */
@Database(
    entities = [
        Word::class,
        Phrase::class,
        EssayTemplate::class,
        StudySession::class,
        UserProgress::class
    ],
    version = 1,
    exportSchema = false
)
abstract class WordDatabase : RoomDatabase() {
    
    abstract fun wordDao(): WordDao
    abstract fun phraseDao(): PhraseDao
    abstract fun essayTemplateDao(): EssayTemplateDao
    abstract fun studySessionDao(): StudySessionDao
    abstract fun userProgressDao(): UserProgressDao
    
    companion object {
        private const val TAG = "WordDatabase"

        @Volatile
        private var INSTANCE: WordDatabase? = null

        fun getDatabase(context: Context): WordDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = try {
                    Log.d(TAG, "Creating database instance")

                    Room.databaseBuilder(
                        context.applicationContext,
                        WordDatabase::class.java,
                        "word_database"
                    )
                    .addCallback(WordDatabaseCallback())
                    .fallbackToDestructiveMigration() // 在开发阶段允许破坏性迁移
                    .build()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to create database", e)
                    throw e
                }

                INSTANCE = instance
                Log.d(TAG, "Database instance created successfully")
                instance
            }
        }

        /**
         * 清除数据库实例（用于测试或重置）
         */
        fun clearInstance() {
            synchronized(this) {
                INSTANCE?.close()
                INSTANCE = null
                Log.d(TAG, "Database instance cleared")
            }
        }
    }
    
    /**
     * 数据库回调，用于预填充数据
     */
    private class WordDatabaseCallback : RoomDatabase.Callback() {
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            try {
                Log.d(TAG, "Database created, version: ${db.version}")
                // 数据库创建后的回调，暂时留空
                // 数据初始化将在DatabaseInitializer中处理
            } catch (e: Exception) {
                Log.e(TAG, "Error in database onCreate callback", e)
            }
        }

        override fun onOpen(db: SupportSQLiteDatabase) {
            super.onOpen(db)
            try {
                Log.d(TAG, "Database opened, version: ${db.version}")
            } catch (e: Exception) {
                Log.e(TAG, "Error in database onOpen callback", e)
            }
        }
    }
}
