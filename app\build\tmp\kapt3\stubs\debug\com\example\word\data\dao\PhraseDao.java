package com.example.word.data.dao;

/**
 * 短语数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000eH\'J\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000eH\'J\u0018\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u000e\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u0017\u001a\u00020\u000bH\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u0019\u001a\u00020\u0015H\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010\u001b\u001a\u00020\u000bH\'J\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\n2\u0006\u0010\u001d\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00122\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010 \u001a\u00020\u00032\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\nH\u00a7@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\n0\u000e2\u0006\u0010$\u001a\u00020\u000bH\'J\u001e\u0010%\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010&\u001a\u00020\'H\u00a7@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001e\u0010*\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010+\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010,\u00a8\u0006-"}, d2 = {"Lcom/example/word/data/dao/PhraseDao;", "", "deleteAllPhrases", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePhrase", "phrase", "Lcom/example/word/data/entities/Phrase;", "(Lcom/example/word/data/entities/Phrase;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCategories", "", "", "getAllPhraseTypes", "getAllPhrases", "Landroidx/lifecycle/LiveData;", "getBookmarkedPhrases", "getPhraseById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPhraseCount", "", "getPhrasesByCategory", "category", "getPhrasesByDifficulty", "level", "getPhrasesByType", "type", "getRandomPhrases", "count", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertPhrase", "insertPhrases", "phrases", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchPhrases", "query", "updateBookmarkStatus", "isBookmarked", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePhrase", "updateStudyStats", "time", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface PhraseDao {
    
    @androidx.room.Query(value = "SELECT * FROM phrases ORDER BY frequency DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> getAllPhrases();
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhraseById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.word.data.entities.Phrase> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE phrase LIKE \'%\' || :query || \'%\' OR translation LIKE \'%\' || :query || \'%\' ORDER BY frequency DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> searchPhrases(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE type = :type ORDER BY frequency DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> getPhrasesByType(@org.jetbrains.annotations.NotNull()
    java.lang.String type);
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE category = :category ORDER BY frequency DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> getPhrasesByCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE difficultyLevel = :level ORDER BY frequency DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> getPhrasesByDifficulty(int level);
    
    @androidx.room.Query(value = "SELECT * FROM phrases WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.example.word.data.entities.Phrase>> getBookmarkedPhrases();
    
    @androidx.room.Query(value = "SELECT * FROM phrases ORDER BY RANDOM() LIMIT :count")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRandomPhrases(int count, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.word.data.entities.Phrase>> $completion);
    
    @androidx.room.Query(value = "SELECT DISTINCT type FROM phrases")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllPhraseTypes(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @androidx.room.Query(value = "SELECT DISTINCT category FROM phrases")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllCategories(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM phrases")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhraseCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPhrase(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Phrase phrase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPhrases(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.word.data.entities.Phrase> phrases, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePhrase(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Phrase phrase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE phrases SET isBookmarked = :isBookmarked WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBookmarkStatus(long id, boolean isBookmarked, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE phrases SET studyCount = studyCount + 1, lastStudiedTime = :time WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateStudyStats(long id, long time, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePhrase(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.Phrase phrase, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM phrases")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllPhrases(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}