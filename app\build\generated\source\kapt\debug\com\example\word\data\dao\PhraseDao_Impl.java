package com.example.word.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.word.data.entities.Phrase;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PhraseDao_Impl implements PhraseDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Phrase> __insertionAdapterOfPhrase;

  private final EntityDeletionOrUpdateAdapter<Phrase> __deletionAdapterOfPhrase;

  private final EntityDeletionOrUpdateAdapter<Phrase> __updateAdapterOfPhrase;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBookmarkStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStudyStats;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllPhrases;

  public PhraseDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPhrase = new EntityInsertionAdapter<Phrase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `phrases` (`id`,`phrase`,`translation`,`type`,`exampleSentence`,`exampleTranslation`,`frequency`,`difficultyLevel`,`category`,`isBookmarked`,`studyCount`,`lastStudiedTime`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Phrase entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPhrase() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPhrase());
        }
        if (entity.getTranslation() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTranslation());
        }
        if (entity.getType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getType());
        }
        if (entity.getExampleSentence() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getExampleSentence());
        }
        if (entity.getExampleTranslation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getExampleTranslation());
        }
        statement.bindLong(7, entity.getFrequency());
        statement.bindLong(8, entity.getDifficultyLevel());
        if (entity.getCategory() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCategory());
        }
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(10, _tmp);
        statement.bindLong(11, entity.getStudyCount());
        statement.bindLong(12, entity.getLastStudiedTime());
      }
    };
    this.__deletionAdapterOfPhrase = new EntityDeletionOrUpdateAdapter<Phrase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `phrases` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Phrase entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPhrase = new EntityDeletionOrUpdateAdapter<Phrase>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `phrases` SET `id` = ?,`phrase` = ?,`translation` = ?,`type` = ?,`exampleSentence` = ?,`exampleTranslation` = ?,`frequency` = ?,`difficultyLevel` = ?,`category` = ?,`isBookmarked` = ?,`studyCount` = ?,`lastStudiedTime` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Phrase entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPhrase() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPhrase());
        }
        if (entity.getTranslation() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTranslation());
        }
        if (entity.getType() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getType());
        }
        if (entity.getExampleSentence() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getExampleSentence());
        }
        if (entity.getExampleTranslation() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getExampleTranslation());
        }
        statement.bindLong(7, entity.getFrequency());
        statement.bindLong(8, entity.getDifficultyLevel());
        if (entity.getCategory() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCategory());
        }
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(10, _tmp);
        statement.bindLong(11, entity.getStudyCount());
        statement.bindLong(12, entity.getLastStudiedTime());
        statement.bindLong(13, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateBookmarkStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE phrases SET isBookmarked = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateStudyStats = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE phrases SET studyCount = studyCount + 1, lastStudiedTime = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllPhrases = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM phrases";
        return _query;
      }
    };
  }

  @Override
  public Object insertPhrase(final Phrase phrase, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfPhrase.insertAndReturnId(phrase);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPhrases(final List<Phrase> phrases,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPhrase.insert(phrases);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePhrase(final Phrase phrase, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPhrase.handle(phrase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePhrase(final Phrase phrase, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPhrase.handle(phrase);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBookmarkStatus(final long id, final boolean isBookmarked,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBookmarkStatus.acquire();
        int _argIndex = 1;
        final int _tmp = isBookmarked ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBookmarkStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateStudyStats(final long id, final long time,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStudyStats.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, time);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateStudyStats.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllPhrases(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllPhrases.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllPhrases.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<Phrase>> getAllPhrases() {
    final String _sql = "SELECT * FROM phrases ORDER BY frequency DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPhraseById(final long id, final Continuation<? super Phrase> $completion) {
    final String _sql = "SELECT * FROM phrases WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Phrase>() {
      @Override
      @Nullable
      public Phrase call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final Phrase _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _result = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<Phrase>> searchPhrases(final String query) {
    final String _sql = "SELECT * FROM phrases WHERE phrase LIKE '%' || ? || '%' OR translation LIKE '%' || ? || '%' ORDER BY frequency DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Phrase>> getPhrasesByType(final String type) {
    final String _sql = "SELECT * FROM phrases WHERE type = ? ORDER BY frequency DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Phrase>> getPhrasesByCategory(final String category) {
    final String _sql = "SELECT * FROM phrases WHERE category = ? ORDER BY frequency DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Phrase>> getPhrasesByDifficulty(final int level) {
    final String _sql = "SELECT * FROM phrases WHERE difficultyLevel = ? ORDER BY frequency DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, level);
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Phrase>> getBookmarkedPhrases() {
    final String _sql = "SELECT * FROM phrases WHERE isBookmarked = 1 ORDER BY lastStudiedTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"phrases"}, false, new Callable<List<Phrase>>() {
      @Override
      @Nullable
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getRandomPhrases(final int count,
      final Continuation<? super List<Phrase>> $completion) {
    final String _sql = "SELECT * FROM phrases ORDER BY RANDOM() LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, count);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Phrase>>() {
      @Override
      @NonNull
      public List<Phrase> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPhrase = CursorUtil.getColumnIndexOrThrow(_cursor, "phrase");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfExampleSentence = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleSentence");
          final int _cursorIndexOfExampleTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "exampleTranslation");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfDifficultyLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "difficultyLevel");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final int _cursorIndexOfStudyCount = CursorUtil.getColumnIndexOrThrow(_cursor, "studyCount");
          final int _cursorIndexOfLastStudiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastStudiedTime");
          final List<Phrase> _result = new ArrayList<Phrase>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Phrase _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPhrase;
            if (_cursor.isNull(_cursorIndexOfPhrase)) {
              _tmpPhrase = null;
            } else {
              _tmpPhrase = _cursor.getString(_cursorIndexOfPhrase);
            }
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpExampleSentence;
            if (_cursor.isNull(_cursorIndexOfExampleSentence)) {
              _tmpExampleSentence = null;
            } else {
              _tmpExampleSentence = _cursor.getString(_cursorIndexOfExampleSentence);
            }
            final String _tmpExampleTranslation;
            if (_cursor.isNull(_cursorIndexOfExampleTranslation)) {
              _tmpExampleTranslation = null;
            } else {
              _tmpExampleTranslation = _cursor.getString(_cursorIndexOfExampleTranslation);
            }
            final int _tmpFrequency;
            _tmpFrequency = _cursor.getInt(_cursorIndexOfFrequency);
            final int _tmpDifficultyLevel;
            _tmpDifficultyLevel = _cursor.getInt(_cursorIndexOfDifficultyLevel);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            final int _tmpStudyCount;
            _tmpStudyCount = _cursor.getInt(_cursorIndexOfStudyCount);
            final long _tmpLastStudiedTime;
            _tmpLastStudiedTime = _cursor.getLong(_cursorIndexOfLastStudiedTime);
            _item = new Phrase(_tmpId,_tmpPhrase,_tmpTranslation,_tmpType,_tmpExampleSentence,_tmpExampleTranslation,_tmpFrequency,_tmpDifficultyLevel,_tmpCategory,_tmpIsBookmarked,_tmpStudyCount,_tmpLastStudiedTime);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllPhraseTypes(final Continuation<? super List<String>> $completion) {
    final String _sql = "SELECT DISTINCT type FROM phrases";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<String>>() {
      @Override
      @NonNull
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            if (_cursor.isNull(0)) {
              _item = null;
            } else {
              _item = _cursor.getString(0);
            }
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllCategories(final Continuation<? super List<String>> $completion) {
    final String _sql = "SELECT DISTINCT category FROM phrases";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<String>>() {
      @Override
      @NonNull
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            if (_cursor.isNull(0)) {
              _item = null;
            } else {
              _item = _cursor.getString(0);
            }
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPhraseCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM phrases";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
