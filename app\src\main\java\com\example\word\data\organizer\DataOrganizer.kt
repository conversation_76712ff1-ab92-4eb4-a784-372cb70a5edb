package com.example.word.data.organizer

import android.content.Context
import android.util.Log
import com.example.word.data.entities.Word
import com.example.word.data.entities.Phrase
import com.example.word.data.entities.EssayTemplate
import org.json.JSONArray
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 数据整理器
 * 将1.txt的内容整理成结构化的数据
 */
object DataOrganizer {
    
    private const val TAG = "DataOrganizer"
    
    /**
     * 整理1.txt中的所有数据
     */
    fun organizeAllData(context: Context): OrganizedData {
        Log.d(TAG, "Starting to organize data from 1.txt")
        
        val words = mutableListOf<Word>()
        val phrases = mutableListOf<Phrase>()
        val essayTemplates = mutableListOf<EssayTemplate>()
        
        try {
            context.assets.open("1.txt").use { inputStream ->
                BufferedReader(InputStreamReader(inputStream, "UTF-8")).use { reader ->
                    var line: String?
                    var currentSection = ""
                    var wordIndex = 1L
                    var phraseIndex = 1L
                    var templateIndex = 1L

                    while (reader.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    val trimmedLine = currentLine.trim()
                    
                    // 检测章节
                    when {
                        trimmedLine.contains("**第一部分：高频词汇列表**") -> {
                            currentSection = "high_freq_words"
                            Log.d(TAG, "Found high frequency words section")
                        }
                        trimmedLine.contains("**第二部分：短语搭配列表**") -> {
                            currentSection = "phrases"
                            Log.d(TAG, "Found phrases section")
                        }
                        trimmedLine.contains("**第四部分：四级作文黄金句式**") -> {
                            currentSection = "essay_sentences"
                            Log.d(TAG, "Found essay sentences section")
                        }
                        trimmedLine.contains("**第五部分：四六级作文首段模板**") -> {
                            currentSection = "essay_templates"
                            Log.d(TAG, "Found essay templates section")
                        }
                        trimmedLine.contains("**第六部分：英语四级 1000 高频词**") -> {
                            currentSection = "cet4_words"
                            Log.d(TAG, "Found CET-4 words section")
                        }
                        trimmedLine.contains("**第三部分：") || 
                        trimmedLine.contains("**第七部分：") -> {
                            currentSection = "other"
                        }
                        
                        // 解析数据
                        currentSection == "high_freq_words" -> {
                            parseHighFreqWord(trimmedLine)?.let { word ->
                                words.add(word.copy(id = wordIndex++))
                            }
                        }
                        currentSection == "cet4_words" -> {
                            parseCET4Word(trimmedLine)?.let { word ->
                                words.add(word.copy(id = wordIndex++))
                            }
                        }
                        currentSection == "phrases" -> {
                            parsePhrase(trimmedLine)?.let { phrase ->
                                phrases.add(phrase.copy(id = phraseIndex++))
                            }
                        }
                        currentSection == "essay_sentences" || currentSection == "essay_templates" -> {
                            parseEssayTemplate(trimmedLine, currentSection)?.let { template ->
                                essayTemplates.add(template.copy(id = templateIndex++))
                            }
                            }
                        }
                    }
                }

                Log.d(TAG, "Data organization completed:")
                Log.d(TAG, "  Words: ${words.size}")
                Log.d(TAG, "  Phrases: ${phrases.size}")
                Log.d(TAG, "  Essay Templates: ${essayTemplates.size}")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error organizing data from 1.txt", e)
        }
        
        return OrganizedData(words, phrases, essayTemplates)
    }
    
    /**
     * 解析高频词汇
     * 格式: "1. available (414 次)a.可利用的，可得到"
     */
    private fun parseHighFreqWord(line: String): Word? {
        return try {
            val regex = """(\d+)\.\s*([a-zA-Z]+)\s*\((\d+)\s*次\)\s*([a-z\.]+)\s*(.+)""".toRegex()
            val matchResult = regex.find(line)
            
            matchResult?.let { match ->
                val (_, word, frequency, partOfSpeech, translation) = match.destructured
                
                Word(
                    id = 0L,
                    word = word.trim(),
                    translation = translation.trim(),
                    phonetic = "",
                    partOfSpeech = partOfSpeech.trim(),
                    exampleSentence = "",
                    exampleTranslation = "",
                    difficultyLevel = calculateDifficultyFromFrequency(frequency.toInt()),
                    frequencyRank = frequency.toInt(),
                    rootWord = "",
                    prefix = "",
                    suffix = "",
                    etymology = "",
                    memoryTip = "",
                    relatedWords = "",
                    synonyms = "",
                    antonyms = ""
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 解析CET-4词汇
     * 格式: "1. evaluate [ɪˈvæljueɪt]（考频 8 次）v.估值；评价"
     */
    private fun parseCET4Word(line: String): Word? {
        return try {
            val regex = """(\d+)\.\s*([a-zA-Z]+)\s*\[([^\]]+)\]\s*（考频\s*(\d+)\s*次）\s*([a-z\.]+)\s*(.+)""".toRegex()
            val matchResult = regex.find(line)
            
            matchResult?.let { match ->
                val (_, word, phonetic, frequency, partOfSpeech, translation) = match.destructured
                
                Word(
                    id = 0L,
                    word = word.trim(),
                    translation = translation.trim(),
                    phonetic = phonetic.trim(),
                    partOfSpeech = partOfSpeech.trim(),
                    exampleSentence = "",
                    exampleTranslation = "",
                    difficultyLevel = calculateDifficultyFromFrequency(frequency.toInt()),
                    frequencyRank = frequency.toInt(),
                    rootWord = "",
                    prefix = "",
                    suffix = "",
                    etymology = "",
                    memoryTip = "",
                    relatedWords = "",
                    synonyms = "",
                    antonyms = ""
                )
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 解析短语
     * 格式: "a series of 一系列,一连串"
     */
    private fun parsePhrase(line: String): Phrase? {
        return try {
            if (line.isEmpty() || line.startsWith("**") || line.startsWith("---")) {
                return null
            }
            
            val chineseIndex = line.indexOfFirst { it.toString().matches(Regex("[\\u4e00-\\u9fa5]")) }
            
            if (chineseIndex > 0) {
                val englishPart = line.substring(0, chineseIndex).trim()
                val chinesePart = line.substring(chineseIndex).trim()
                
                if (englishPart.isNotEmpty() && chinesePart.isNotEmpty()) {
                    return Phrase(
                        id = 0L,
                        phrase = englishPart,
                        translation = chinesePart,
                        type = "collocation",
                        exampleSentence = "",
                        exampleTranslation = "",
                        frequency = 3,
                        difficultyLevel = 2,
                        category = "常用短语"
                    )
                }
            }
            
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 解析作文模板
     */
    private fun parseEssayTemplate(line: String, section: String): EssayTemplate? {
        return try {
            if (line.isEmpty() || line.startsWith("**") || line.startsWith("---")) {
                return null
            }
            
            val numberRegex = """(\d+)\.\s*(.+)""".toRegex()
            val matchResult = numberRegex.find(line)
            
            matchResult?.let { match ->
                val (_, content) = match.destructured
                
                val chineseIndex = content.indexOfFirst { it.toString().matches(Regex("[\\u4e00-\\u9fa5]")) }
                
                if (chineseIndex > 0) {
                    val englishPart = content.substring(0, chineseIndex).trim()
                    val chinesePart = content.substring(chineseIndex).trim()
                    
                    if (englishPart.isNotEmpty() && chinesePart.isNotEmpty()) {
                        val category = if (section == "essay_sentences") "黄金句式" else "首段模板"
                        
                        return EssayTemplate(
                            id = 0L,
                            title = "四级作文$category",
                            type = "sentence_pattern",
                            content = englishPart,
                            description = chinesePart,
                            usage = "用于四级作文写作",
                            difficultyLevel = 3,
                            category = category
                        )
                    }
                }
            }
            
            null
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 根据频率计算难度等级
     */
    private fun calculateDifficultyFromFrequency(frequency: Int): Int {
        return when {
            frequency >= 100 -> 1
            frequency >= 50 -> 2
            frequency >= 20 -> 3
            frequency >= 10 -> 4
            else -> 5
        }
    }
    
    /**
     * 整理后的数据
     */
    data class OrganizedData(
        val words: List<Word>,
        val phrases: List<Phrase>,
        val essayTemplates: List<EssayTemplate>
    )
}
