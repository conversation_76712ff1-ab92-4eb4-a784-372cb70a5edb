package com.example.word.utils

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import java.util.*

/**
 * 简化的TTS管理器
 * 专注于基本的语音播放功能
 */
class SimpleTTSManager(private val context: Context) {
    
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false
    private var isInitializing = false
    
    companion object {
        private const val TAG = "SimpleTTSManager"
    }
    
    /**
     * 初始化TTS
     */
    fun initialize(onInitComplete: ((Boolean) -> Unit)? = null) {
        if (isInitialized || isInitializing) {
            Log.d(TAG, "TTS already initialized or initializing")
            onInitComplete?.invoke(isInitialized)
            return
        }
        
        isInitializing = true
        Log.d(TAG, "Initializing TTS...")
        
        try {
            textToSpeech = TextToSpeech(context) { status ->
                isInitializing = false
                
                when (status) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "TTS initialization successful")
                        
                        textToSpeech?.let { tts ->
                            // 设置语言为英语
                            val langResult = tts.setLanguage(Locale.US)
                            
                            when (langResult) {
                                TextToSpeech.LANG_MISSING_DATA,
                                TextToSpeech.LANG_NOT_SUPPORTED -> {
                                    Log.w(TAG, "English language not supported, trying default")
                                    tts.setLanguage(Locale.getDefault())
                                }
                            }
                            
                            // 设置语音参数
                            tts.setSpeechRate(1.0f)
                            tts.setPitch(1.0f)
                            
                            // 设置进度监听器
                            tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
                                override fun onStart(utteranceId: String?) {
                                    Log.d(TAG, "TTS started: $utteranceId")
                                }
                                
                                override fun onDone(utteranceId: String?) {
                                    Log.d(TAG, "TTS completed: $utteranceId")
                                }
                                
                                override fun onError(utteranceId: String?) {
                                    Log.e(TAG, "TTS error: $utteranceId")
                                }
                            })
                            
                            isInitialized = true
                            Log.d(TAG, "TTS setup completed successfully")
                            onInitComplete?.invoke(true)
                        }
                    }
                    else -> {
                        Log.e(TAG, "TTS initialization failed with status: $status")

                        // 使用错误处理器分析错误
                        val errorInfo = TTSErrorHandler.analyzeTTSError(status, context)
                        Log.w(TAG, "TTS Error Analysis: ${errorInfo.message}")

                        if (errorInfo.suggestedAction != null) {
                            Log.i(TAG, "Suggested action: ${errorInfo.suggestedAction}")
                        }

                        isInitialized = false
                        onInitComplete?.invoke(false)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing TTS", e)
            isInitializing = false
            isInitialized = false
            onInitComplete?.invoke(false)
        }
    }
    
    /**
     * 播放文本
     */
    fun speak(text: String, slowSpeed: Boolean = false): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "TTS not initialized, attempting to initialize...")
            initialize { success ->
                if (success) {
                    speak(text, slowSpeed)
                }
            }
            return false
        }
        
        return try {
            textToSpeech?.let { tts ->
                Log.d(TAG, "Speaking: '$text', slow: $slowSpeed")
                
                // 设置语速
                val speechRate = if (slowSpeed) 0.7f else 1.0f
                tts.setSpeechRate(speechRate)
                
                // 播放文本
                val result = tts.speak(text, TextToSpeech.QUEUE_FLUSH, null, "TTS_$text")
                
                when (result) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "TTS speak command successful")
                        true
                    }
                    TextToSpeech.ERROR -> {
                        Log.e(TAG, "TTS speak command failed")
                        false
                    }
                    else -> {
                        Log.w(TAG, "TTS speak command returned: $result")
                        false
                    }
                }
            } ?: run {
                Log.e(TAG, "TTS instance is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text: $text", e)
            false
        }
    }
    
    /**
     * 停止播放
     */
    fun stop() {
        try {
            textToSpeech?.stop()
            Log.d(TAG, "TTS stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping TTS", e)
        }
    }
    
    /**
     * 检查是否正在播放
     */
    fun isSpeaking(): Boolean {
        return try {
            textToSpeech?.isSpeaking ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if TTS is speaking", e)
            false
        }
    }
    
    /**
     * 检查是否已初始化
     */
    fun isReady(): Boolean = isInitialized
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            textToSpeech?.stop()
            textToSpeech?.shutdown()
            textToSpeech = null
            isInitialized = false
            isInitializing = false
            Log.d(TAG, "TTS resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing TTS resources", e)
        }
    }
    
    /**
     * 获取状态信息
     */
    fun getStatus(): String {
        return "Initialized: $isInitialized, Initializing: $isInitializing, Speaking: ${isSpeaking()}"
    }
}
