package com.example.word.data.dao;

/**
 * 用户进度数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\u0007\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J \u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ \u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u00072\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\f\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\u000fH\'J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0010H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0014J(\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0019J@\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u001d2\u0006\u0010 \u001a\u00020\u001d2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010!J@\u0010\"\u001a\u00020\u00032\u0006\u0010#\u001a\u00020\u00052\u0006\u0010$\u001a\u00020\u001d2\u0006\u0010%\u001a\u00020\u00172\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001d2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010(J0\u0010)\u001a\u00020\u00032\u0006\u0010*\u001a\u00020\u00052\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010+\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010,J8\u0010-\u001a\u00020\u00032\u0006\u0010.\u001a\u00020\u00052\u0006\u0010/\u001a\u00020\u00052\u0006\u00100\u001a\u00020\u00052\u0006\u00101\u001a\u0002022\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u00103JH\u00104\u001a\u00020\u00032\u0006\u00105\u001a\u00020\u00052\u0006\u00106\u001a\u00020\u00052\u0006\u00107\u001a\u00020\u00072\u0006\u00108\u001a\u00020\u00052\u0006\u00109\u001a\u00020\u00052\u0006\u0010:\u001a\u00020\u00072\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010;\u00a8\u0006<"}, d2 = {"Lcom/example/word/data/dao/UserProgressDao;", "", "addExperience", "", "experience", "", "updatedAt", "", "(IJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addStudyTime", "additionalTime", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearUserProgress", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserProgress", "Landroidx/lifecycle/LiveData;", "Lcom/example/word/data/entities/UserProgress;", "getUserProgressSync", "insertOrUpdateUserProgress", "userProgress", "(Lcom/example/word/data/entities/UserProgress;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAchievements", "achievements", "", "totalAchievements", "(Ljava/lang/String;IJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAppSettings", "themeMode", "showDifficulty", "", "showFrequency", "showStreak", "autoBackup", "(Ljava/lang/String;ZZZZJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLearningPreferences", "dailyGoal", "reminderEnabled", "reminderTime", "autoPlay", "slowPronunciation", "(IZLjava/lang/String;ZZJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLevelAndExperience", "level", "totalExperience", "(IIIJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateQuizStats", "totalQuizzes", "correctAnswers", "totalAnswers", "averageAccuracy", "", "(IIIFJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateStudyStats", "wordsLearned", "phrasesLearned", "studyTime", "studyDays", "consecutiveDays", "lastStudyDate", "(IIJIIJJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface UserProgressDao {
    
    @androidx.room.Query(value = "SELECT * FROM user_progress WHERE id = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.example.word.data.entities.UserProgress> getUserProgress();
    
    @androidx.room.Query(value = "SELECT * FROM user_progress WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserProgressSync(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.word.data.entities.UserProgress> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertOrUpdateUserProgress(@org.jetbrains.annotations.NotNull()
    com.example.word.data.entities.UserProgress userProgress, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress\n        SET totalWordsLearned = :wordsLearned,\n            totalPhrasesLearned = :phrasesLearned,\n            totalStudyTime = :studyTime,\n            studyDays = :studyDays,\n            consecutiveDays = :consecutiveDays,\n            lastStudyDate = :lastStudyDate,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateStudyStats(int wordsLearned, int phrasesLearned, long studyTime, int studyDays, int consecutiveDays, long lastStudyDate, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress\n        SET totalStudyTime = totalStudyTime + :additionalTime,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addStudyTime(long additionalTime, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress\n        SET experiencePoints = experiencePoints + :experience,\n            totalExperience = totalExperience + :experience,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addExperience(int experience, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress \n        SET currentLevel = :level,\n            experiencePoints = :experience,\n            totalExperience = :totalExperience,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLevelAndExperience(int level, int experience, int totalExperience, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress \n        SET totalQuizzes = :totalQuizzes,\n            correctAnswers = :correctAnswers,\n            totalAnswers = :totalAnswers,\n            averageAccuracy = :averageAccuracy,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateQuizStats(int totalQuizzes, int correctAnswers, int totalAnswers, float averageAccuracy, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress \n        SET unlockedAchievements = :achievements,\n            totalAchievements = :totalAchievements,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAchievements(@org.jetbrains.annotations.NotNull()
    java.lang.String achievements, int totalAchievements, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress \n        SET dailyGoal = :dailyGoal,\n            reminderEnabled = :reminderEnabled,\n            reminderTime = :reminderTime,\n            autoPlayPronunciation = :autoPlay,\n            slowPronunciation = :slowPronunciation,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLearningPreferences(int dailyGoal, boolean reminderEnabled, @org.jetbrains.annotations.NotNull()
    java.lang.String reminderTime, boolean autoPlay, boolean slowPronunciation, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE user_progress \n        SET themeMode = :themeMode,\n            showDifficulty = :showDifficulty,\n            showFrequency = :showFrequency,\n            showStreak = :showStreak,\n            autoBackup = :autoBackup,\n            updatedAt = :updatedAt\n        WHERE id = 1\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAppSettings(@org.jetbrains.annotations.NotNull()
    java.lang.String themeMode, boolean showDifficulty, boolean showFrequency, boolean showStreak, boolean autoBackup, long updatedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM user_progress")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearUserProgress(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 用户进度数据访问对象
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}