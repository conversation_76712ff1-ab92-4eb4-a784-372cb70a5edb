package com.example.word.ui.progress

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.word.databinding.FragmentProgressBinding
import com.example.word.utils.StudyDataPoint
import com.example.word.utils.GameificationManager
import java.text.SimpleDateFormat
import java.util.*

/**
 * 学习进度Fragment
 */
class ProgressFragment : Fragment() {

    private var _binding: FragmentProgressBinding? = null
    private val binding get() = _binding!!

    private val progressViewModel: ProgressViewModel by viewModels()
    private lateinit var achievementAdapter: AchievementAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProgressBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()
        setupObservers()
        setupCharts()
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        achievementAdapter = AchievementAdapter()
        binding.recyclerViewAchievements.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = achievementAdapter
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察用户进度
        progressViewModel.userProgress.observe(viewLifecycleOwner) { progress ->
            updateUserProgress(progress)
        }

        // 观察学习统计
        progressViewModel.studyStatistics.observe(viewLifecycleOwner) { statistics ->
            updateStudyStatistics(statistics)
        }

        // 观察词汇掌握情况
        progressViewModel.vocabularyMastery.observe(viewLifecycleOwner) { mastery ->
            updateVocabularyMastery(mastery)
        }

        // 观察学习成就
        progressViewModel.achievements.observe(viewLifecycleOwner) { achievements ->
            achievementAdapter.submitList(achievements)
        }

        // 观察加载状态
        progressViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        // 观察错误信息
        progressViewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
                progressViewModel.clearErrorMessage()
            }
        }
    }

    /**
     * 更新用户进度显示
     */
    private fun updateUserProgress(progress: com.example.word.data.entities.UserProgress?) {
        if (progress == null) {
            // 如果没有用户进度数据，显示默认值
            binding.apply {
                textViewCurrentLevel.text = "等级 1"
                textViewExperiencePoints.text = "0 XP"
                progressBarLevel.max = 1000
                progressBarLevel.progress = 0
                textViewExpProgress.text = "0 / 1000"
            }
            return
        }

        binding.apply {
            textViewCurrentLevel.text = "等级 ${progress.currentLevel}"
            textViewExperiencePoints.text = "${progress.experiencePoints} XP"

            // 更新经验值进度条
            val nextLevelExp = progress.currentLevel * 1000
            val currentLevelExp = (progress.currentLevel - 1) * 1000
            val progressInLevel = progress.experiencePoints - currentLevelExp
            val expNeededForLevel = nextLevelExp - currentLevelExp

            progressBarLevel.max = expNeededForLevel
            progressBarLevel.progress = progressInLevel

            textViewExpProgress.text = "$progressInLevel / $expNeededForLevel"
        }
    }

    /**
     * 更新学习统计显示
     */
    private fun updateStudyStatistics(statistics: StudyStatistics) {
        binding.apply {
            // 基本统计
            textViewTotalStudyDays.text = "${statistics.totalStudyDays}天"
            textViewConsecutiveDays.text = "${statistics.consecutiveStudyDays}天"
            textViewTotalStudyTime.text = formatStudyTime(statistics.totalStudyTime)
            textViewStudiedWords.text = "${statistics.studiedWordsCount}个"
            textViewBookmarkedWords.text = "${statistics.bookmarkedWordsCount}个"
            textViewAverageAccuracy.text = "${statistics.averageAccuracy.toInt()}%"

            // 今日统计
            textViewTodayStudyCount.text = "${statistics.todayStudyCount}次"
            textViewTodayAccuracy.text = "${statistics.todayAccuracy}%"

            // 本周统计
            textViewWeekStudyCount.text = "${statistics.weekStudyCount}次"

            // 更新准确率进度条
            progressBarAccuracy.progress = statistics.averageAccuracy.toInt()
        }
    }

    /**
     * 更新词汇掌握情况显示
     */
    private fun updateVocabularyMastery(mastery: VocabularyMastery) {
        binding.apply {
            textViewTotalVocabulary.text = "${mastery.totalWords}个"
            textViewStudiedVocabulary.text = "${mastery.studiedWords}个"
            textViewMasteredVocabulary.text = "${mastery.masteredWords}个"
            textViewMasteryRate.text = "${mastery.masteryRate}%"

            // 更新掌握率进度条
            progressBarMastery.max = mastery.totalWords
            progressBarMastery.progress = mastery.masteredWords

            // 显示各难度等级统计
            updateDifficultyStats(mastery.difficultyStats)
        }
    }

    /**
     * 更新难度等级统计
     */
    private fun updateDifficultyStats(difficultyStats: Map<Int, DifficultyStats>) {
        // 这里可以添加更详细的难度等级统计显示
        // 由于布局限制，暂时简化处理
    }

    /**
     * 设置图表
     */
    private fun setupCharts() {
        // 设置学习进度图表
        val studyData = generateStudyProgressData()
        binding.chartStudyProgress.setData(studyData)

        // 设置词汇掌握圆形图表
        binding.chartVocabularyMastery.setProgress(
            current = 95f,
            max = 4500f,
            titleText = "词汇掌握",
            subtitleText = "95/4500"
        )

        // 设置等级进度圆形图表
        binding.chartLevelProgress.setProgress(
            current = 750f,
            max = 1000f,
            titleText = "等级进度",
            subtitleText = "750/1000 XP"
        )
    }

    /**
     * 生成学习进度数据
     */
    private fun generateStudyProgressData(): List<StudyDataPoint> {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("MM/dd", Locale.getDefault())
        val data = mutableListOf<StudyDataPoint>()

        // 生成最近7天的学习数据
        for (i in 6 downTo 0) {
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            val date = dateFormat.format(calendar.time)
            val studyCount = (10..50).random().toFloat() // 模拟数据
            data.add(StudyDataPoint(date, studyCount))
        }

        return data.reversed()
    }

    /**
     * 格式化学习时间
     */
    private fun formatStudyTime(timeMinutes: Long): String {
        val hours = timeMinutes / 60
        val minutes = timeMinutes % 60

        return when {
            hours > 0 -> "${hours}小时${minutes}分钟"
            minutes > 0 -> "${minutes}分钟"
            else -> "0分钟"
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
