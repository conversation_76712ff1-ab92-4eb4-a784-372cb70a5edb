package com.example.word.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.word.data.entities.EssayTemplate

/**
 * 作文模板数据访问对象
 */
@Dao
interface EssayTemplateDao {
    
    // 获取所有模板
    @Query("SELECT * FROM essay_templates ORDER BY type, difficultyLevel")
    fun getAllTemplates(): LiveData<List<EssayTemplate>>
    
    // 根据ID获取模板
    @Query("SELECT * FROM essay_templates WHERE id = :id")
    suspend fun getTemplateById(id: Long): EssayTemplate?
    
    // 根据类型获取模板
    @Query("SELECT * FROM essay_templates WHERE type = :type ORDER BY difficultyLevel")
    fun getTemplatesByType(type: String): LiveData<List<EssayTemplate>>
    
    // 根据分类获取模板
    @Query("SELECT * FROM essay_templates WHERE category = :category ORDER BY difficultyLevel")
    fun getTemplatesByCategory(category: String): LiveData<List<EssayTemplate>>
    
    // 根据难度获取模板
    @Query("SELECT * FROM essay_templates WHERE difficultyLevel = :level ORDER BY type")
    fun getTemplatesByDifficulty(level: Int): LiveData<List<EssayTemplate>>
    
    // 搜索模板
    @Query("SELECT * FROM essay_templates WHERE title LIKE '%' || :query || '%' OR content LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%'")
    fun searchTemplates(query: String): LiveData<List<EssayTemplate>>
    
    // 获取收藏的模板
    @Query("SELECT * FROM essay_templates WHERE isBookmarked = 1 ORDER BY usageCount DESC")
    fun getBookmarkedTemplates(): LiveData<List<EssayTemplate>>
    
    // 获取最常用的模板
    @Query("SELECT * FROM essay_templates ORDER BY usageCount DESC LIMIT :count")
    fun getMostUsedTemplates(count: Int): LiveData<List<EssayTemplate>>
    
    // 获取所有模板类型
    @Query("SELECT DISTINCT type FROM essay_templates")
    suspend fun getAllTemplateTypes(): List<String>
    
    // 获取所有分类
    @Query("SELECT DISTINCT category FROM essay_templates")
    suspend fun getAllCategories(): List<String>

    // 获取总模板数
    @Query("SELECT COUNT(*) FROM essay_templates")
    suspend fun getTemplateCount(): Int
    
    // 插入模板
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplate(template: EssayTemplate): Long
    
    // 批量插入模板
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTemplates(templates: List<EssayTemplate>)
    
    // 更新模板
    @Update
    suspend fun updateTemplate(template: EssayTemplate)
    
    // 更新收藏状态
    @Query("UPDATE essay_templates SET isBookmarked = :isBookmarked WHERE id = :id")
    suspend fun updateBookmarkStatus(id: Long, isBookmarked: Boolean)
    
    // 增加使用次数
    @Query("UPDATE essay_templates SET usageCount = usageCount + 1 WHERE id = :id")
    suspend fun incrementUsageCount(id: Long)
    
    // 删除模板
    @Delete
    suspend fun deleteTemplate(template: EssayTemplate)
    
    // 清空所有模板
    @Query("DELETE FROM essay_templates")
    suspend fun deleteAllTemplates()
}
