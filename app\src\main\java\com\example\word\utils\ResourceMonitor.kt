package com.example.word.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.io.Closeable
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 资源监控器
 * 监控和管理应用中的资源使用
 */
object ResourceMonitor {
    
    private const val TAG = "ResourceMonitor"
    
    // 资源追踪
    private val activeResources = ConcurrentHashMap<String, ResourceInfo>()
    private val resourceCounter = AtomicLong(0)
    
    /**
     * 资源信息
     */
    data class ResourceInfo(
        val id: String,
        val type: String,
        val name: String,
        val createdAt: Long,
        val stackTrace: String
    )
    
    /**
     * 注册资源
     */
    fun registerResource(type: String, name: String, resource: Any): String {
        val id = "resource_${resourceCounter.incrementAndGet()}"
        val stackTrace = Thread.currentThread().stackTrace.joinToString("\n") { it.toString() }
        
        val resourceInfo = ResourceInfo(
            id = id,
            type = type,
            name = name,
            createdAt = System.currentTimeMillis(),
            stackTrace = stackTrace
        )
        
        activeResources[id] = resourceInfo
        Log.d(TAG, "Registered resource: $type - $name (ID: $id)")
        
        return id
    }
    
    /**
     * 注销资源
     */
    fun unregisterResource(id: String) {
        activeResources.remove(id)?.let { resource ->
            Log.d(TAG, "Unregistered resource: ${resource.type} - ${resource.name} (ID: $id)")
        }
    }
    
    /**
     * 安全关闭资源
     */
    fun safeClose(resource: Closeable?, resourceName: String = "Unknown") {
        try {
            resource?.close()
            Log.d(TAG, "Successfully closed resource: $resourceName")
        } catch (e: Exception) {
            Log.e(TAG, "Error closing resource: $resourceName", e)
        }
    }
    
    /**
     * 安全关闭多个资源
     */
    fun safeCloseAll(vararg resources: Pair<Closeable?, String>) {
        resources.forEach { (resource, name) ->
            safeClose(resource, name)
        }
    }
    
    /**
     * 检查资源泄漏
     */
    fun checkResourceLeaks(): List<ResourceInfo> {
        val currentTime = System.currentTimeMillis()
        val leakedResources = mutableListOf<ResourceInfo>()
        
        activeResources.values.forEach { resource ->
            val age = currentTime - resource.createdAt
            
            // 如果资源存在超过5分钟，认为可能泄漏
            if (age > 5 * 60 * 1000) {
                leakedResources.add(resource)
                Log.w(TAG, "Potential resource leak detected: ${resource.type} - ${resource.name} (Age: ${age}ms)")
            }
        }
        
        return leakedResources
    }
    
    /**
     * 获取资源报告
     */
    fun getResourceReport(): String {
        return buildString {
            appendLine("=== 资源监控报告 ===")
            appendLine("活跃资源数量: ${activeResources.size}")
            appendLine("总创建资源数: ${resourceCounter.get()}")
            appendLine()
            
            if (activeResources.isNotEmpty()) {
                appendLine("活跃资源列表:")
                activeResources.values.forEach { resource ->
                    val age = System.currentTimeMillis() - resource.createdAt
                    appendLine("- ${resource.type}: ${resource.name} (Age: ${age}ms)")
                }
                appendLine()
            }
            
            val leakedResources = checkResourceLeaks()
            if (leakedResources.isNotEmpty()) {
                appendLine("⚠️ 可能的资源泄漏:")
                leakedResources.forEach { resource ->
                    val age = System.currentTimeMillis() - resource.createdAt
                    appendLine("- ${resource.type}: ${resource.name} (Age: ${age}ms)")
                }
            } else {
                appendLine("✅ 未检测到资源泄漏")
            }
        }
    }
    
    /**
     * 清理过期资源
     */
    fun cleanupExpiredResources() {
        val currentTime = System.currentTimeMillis()
        val expiredResources = mutableListOf<String>()
        
        activeResources.forEach { (id, resource) ->
            val age = currentTime - resource.createdAt
            
            // 清理超过10分钟的资源记录
            if (age > 10 * 60 * 1000) {
                expiredResources.add(id)
            }
        }
        
        expiredResources.forEach { id ->
            activeResources.remove(id)
            Log.d(TAG, "Cleaned up expired resource record: $id")
        }
        
        if (expiredResources.isNotEmpty()) {
            Log.d(TAG, "Cleaned up ${expiredResources.size} expired resource records")
        }
    }
    
    /**
     * 启动资源监控
     */
    fun startMonitoring(context: Context) {
        Log.d(TAG, "Starting resource monitoring...")
        
        CoroutineScope(Dispatchers.Default).launch {
            while (true) {
                try {
                    // 每分钟检查一次
                    delay(60 * 1000)
                    
                    // 检查资源泄漏
                    val leakedResources = checkResourceLeaks()
                    if (leakedResources.isNotEmpty()) {
                        Log.w(TAG, "Detected ${leakedResources.size} potential resource leaks")
                    }
                    
                    // 清理过期资源记录
                    cleanupExpiredResources()
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Error in resource monitoring", e)
                }
            }
        }
    }
    
    /**
     * 使用资源的安全包装器
     */
    inline fun <T : Closeable, R> useResource(
        resource: T,
        resourceName: String = "Unknown",
        block: (T) -> R
    ): R {
        val id = registerResource("Closeable", resourceName, resource)
        return try {
            resource.use { block(it) }
        } finally {
            unregisterResource(id)
        }
    }
    
    /**
     * 监控协程资源
     */
    fun monitorCoroutine(name: String, job: Job): String {
        val id = registerResource("Coroutine", name, job)
        
        job.invokeOnCompletion { exception ->
            unregisterResource(id)
            if (exception != null) {
                Log.w(TAG, "Coroutine $name completed with exception", exception)
            } else {
                Log.d(TAG, "Coroutine $name completed successfully")
            }
        }
        
        return id
    }
    
    /**
     * 获取内存使用情况
     */
    fun getMemoryInfo(): String {
        val runtime = Runtime.getRuntime()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory()
        
        return buildString {
            appendLine("=== 内存使用情况 ===")
            appendLine("已使用: ${usedMemory / 1024 / 1024}MB")
            appendLine("总分配: ${totalMemory / 1024 / 1024}MB")
            appendLine("最大可用: ${maxMemory / 1024 / 1024}MB")
            appendLine("使用率: ${(usedMemory * 100) / maxMemory}%")
        }
    }
    
    /**
     * 强制垃圾回收
     */
    fun forceGarbageCollection() {
        Log.d(TAG, "Forcing garbage collection...")
        System.gc()
        
        // 等待一下让GC完成
        try {
            Thread.sleep(100)
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt()
        }
        
        Log.d(TAG, "Garbage collection completed")
    }
    
    /**
     * 获取完整的系统报告
     */
    fun getSystemReport(): String {
        return buildString {
            append(getResourceReport())
            appendLine()
            append(getMemoryInfo())
        }
    }
}
