// Generated by view binder compiler. Do not edit!
package com.example.word.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.word.R;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final Button buttonAbout;

  @NonNull
  public final Button buttonClearData;

  @NonNull
  public final Button buttonExportData;

  @NonNull
  public final Button buttonImportData;

  @NonNull
  public final Button buttonTestTTS;

  @NonNull
  public final LinearLayout layoutReminderTime;

  @NonNull
  public final RadioButton radioButtonDark;

  @NonNull
  public final RadioButton radioButtonLight;

  @NonNull
  public final RadioButton radioButtonSystem;

  @NonNull
  public final RadioGroup radioGroupTheme;

  @NonNull
  public final Spinner spinnerDailyGoal;

  @NonNull
  public final Spinner spinnerReviewMode;

  @NonNull
  public final SwitchMaterial switchAutoBackup;

  @NonNull
  public final SwitchMaterial switchAutoPlay;

  @NonNull
  public final SwitchMaterial switchReminder;

  @NonNull
  public final SwitchMaterial switchShowProgress;

  @NonNull
  public final SwitchMaterial switchShowStreak;

  @NonNull
  public final SwitchMaterial switchSlowSpeed;

  @NonNull
  public final TextView textViewReminderTime;

  private FragmentSettingsBinding(@NonNull NestedScrollView rootView, @NonNull Button buttonAbout,
      @NonNull Button buttonClearData, @NonNull Button buttonExportData,
      @NonNull Button buttonImportData, @NonNull Button buttonTestTTS,
      @NonNull LinearLayout layoutReminderTime, @NonNull RadioButton radioButtonDark,
      @NonNull RadioButton radioButtonLight, @NonNull RadioButton radioButtonSystem,
      @NonNull RadioGroup radioGroupTheme, @NonNull Spinner spinnerDailyGoal,
      @NonNull Spinner spinnerReviewMode, @NonNull SwitchMaterial switchAutoBackup,
      @NonNull SwitchMaterial switchAutoPlay, @NonNull SwitchMaterial switchReminder,
      @NonNull SwitchMaterial switchShowProgress, @NonNull SwitchMaterial switchShowStreak,
      @NonNull SwitchMaterial switchSlowSpeed, @NonNull TextView textViewReminderTime) {
    this.rootView = rootView;
    this.buttonAbout = buttonAbout;
    this.buttonClearData = buttonClearData;
    this.buttonExportData = buttonExportData;
    this.buttonImportData = buttonImportData;
    this.buttonTestTTS = buttonTestTTS;
    this.layoutReminderTime = layoutReminderTime;
    this.radioButtonDark = radioButtonDark;
    this.radioButtonLight = radioButtonLight;
    this.radioButtonSystem = radioButtonSystem;
    this.radioGroupTheme = radioGroupTheme;
    this.spinnerDailyGoal = spinnerDailyGoal;
    this.spinnerReviewMode = spinnerReviewMode;
    this.switchAutoBackup = switchAutoBackup;
    this.switchAutoPlay = switchAutoPlay;
    this.switchReminder = switchReminder;
    this.switchShowProgress = switchShowProgress;
    this.switchShowStreak = switchShowStreak;
    this.switchSlowSpeed = switchSlowSpeed;
    this.textViewReminderTime = textViewReminderTime;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_about;
      Button buttonAbout = ViewBindings.findChildViewById(rootView, id);
      if (buttonAbout == null) {
        break missingId;
      }

      id = R.id.button_clear_data;
      Button buttonClearData = ViewBindings.findChildViewById(rootView, id);
      if (buttonClearData == null) {
        break missingId;
      }

      id = R.id.button_export_data;
      Button buttonExportData = ViewBindings.findChildViewById(rootView, id);
      if (buttonExportData == null) {
        break missingId;
      }

      id = R.id.button_import_data;
      Button buttonImportData = ViewBindings.findChildViewById(rootView, id);
      if (buttonImportData == null) {
        break missingId;
      }

      id = R.id.buttonTestTTS;
      Button buttonTestTTS = ViewBindings.findChildViewById(rootView, id);
      if (buttonTestTTS == null) {
        break missingId;
      }

      id = R.id.layout_reminder_time;
      LinearLayout layoutReminderTime = ViewBindings.findChildViewById(rootView, id);
      if (layoutReminderTime == null) {
        break missingId;
      }

      id = R.id.radio_button_dark;
      RadioButton radioButtonDark = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonDark == null) {
        break missingId;
      }

      id = R.id.radio_button_light;
      RadioButton radioButtonLight = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonLight == null) {
        break missingId;
      }

      id = R.id.radio_button_system;
      RadioButton radioButtonSystem = ViewBindings.findChildViewById(rootView, id);
      if (radioButtonSystem == null) {
        break missingId;
      }

      id = R.id.radio_group_theme;
      RadioGroup radioGroupTheme = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupTheme == null) {
        break missingId;
      }

      id = R.id.spinner_daily_goal;
      Spinner spinnerDailyGoal = ViewBindings.findChildViewById(rootView, id);
      if (spinnerDailyGoal == null) {
        break missingId;
      }

      id = R.id.spinner_review_mode;
      Spinner spinnerReviewMode = ViewBindings.findChildViewById(rootView, id);
      if (spinnerReviewMode == null) {
        break missingId;
      }

      id = R.id.switch_auto_backup;
      SwitchMaterial switchAutoBackup = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoBackup == null) {
        break missingId;
      }

      id = R.id.switch_auto_play;
      SwitchMaterial switchAutoPlay = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoPlay == null) {
        break missingId;
      }

      id = R.id.switch_reminder;
      SwitchMaterial switchReminder = ViewBindings.findChildViewById(rootView, id);
      if (switchReminder == null) {
        break missingId;
      }

      id = R.id.switch_show_progress;
      SwitchMaterial switchShowProgress = ViewBindings.findChildViewById(rootView, id);
      if (switchShowProgress == null) {
        break missingId;
      }

      id = R.id.switch_show_streak;
      SwitchMaterial switchShowStreak = ViewBindings.findChildViewById(rootView, id);
      if (switchShowStreak == null) {
        break missingId;
      }

      id = R.id.switch_slow_speed;
      SwitchMaterial switchSlowSpeed = ViewBindings.findChildViewById(rootView, id);
      if (switchSlowSpeed == null) {
        break missingId;
      }

      id = R.id.text_view_reminder_time;
      TextView textViewReminderTime = ViewBindings.findChildViewById(rootView, id);
      if (textViewReminderTime == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((NestedScrollView) rootView, buttonAbout, buttonClearData,
          buttonExportData, buttonImportData, buttonTestTTS, layoutReminderTime, radioButtonDark,
          radioButtonLight, radioButtonSystem, radioGroupTheme, spinnerDailyGoal, spinnerReviewMode,
          switchAutoBackup, switchAutoPlay, switchReminder, switchShowProgress, switchShowStreak,
          switchSlowSpeed, textViewReminderTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
