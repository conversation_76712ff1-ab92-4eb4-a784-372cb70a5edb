package com.example.word.utils

import android.content.Context
import android.speech.tts.TextToSpeech
import android.util.Log
import java.util.*

/**
 * TTS调试助手
 * 用于诊断TTS问题
 */
object TTSDebugHelper {
    
    private const val TAG = "TTSDebugHelper"
    
    /**
     * 检查TTS可用性
     */
    fun checkTTSAvailability(context: Context, callback: (<PERSON><PERSON><PERSON>, String) -> Unit) {
        Log.d(TAG, "Checking TTS availability...")
        
        try {
            val tts = TextToSpeech(context) { status ->
                when (status) {
                    TextToSpeech.SUCCESS -> {
                        Log.d(TAG, "TTS initialization successful")
                        
                        // 检查语言支持
                        val langResult = tts.setLanguage(Locale.US)
                        when (langResult) {
                            TextToSpeech.LANG_MISSING_DATA -> {
                                Log.w(TAG, "TTS language data missing")
                                callback(false, "TTS语言数据缺失")
                            }
                            TextToSpeech.LANG_NOT_SUPPORTED -> {
                                Log.w(TAG, "TTS language not supported")
                                callback(false, "TTS不支持英语")
                            }
                            else -> {
                                Log.i(TAG, "TTS language support OK")
                                
                                // 测试发音
                                val speakResult = tts.speak("test", TextToSpeech.QUEUE_FLUSH, null, "test_id")
                                when (speakResult) {
                                    TextToSpeech.SUCCESS -> {
                                        Log.i(TAG, "TTS speak test successful")
                                        callback(true, "TTS功能正常")
                                    }
                                    TextToSpeech.ERROR -> {
                                        Log.e(TAG, "TTS speak test failed")
                                        callback(false, "TTS播放测试失败")
                                    }
                                    else -> {
                                        Log.w(TAG, "TTS speak test returned: $speakResult")
                                        callback(false, "TTS播放状态异常: $speakResult")
                                    }
                                }
                            }
                        }
                        
                        // 清理资源
                        tts.stop()
                        tts.shutdown()
                        
                    }
                    else -> {
                        Log.e(TAG, "TTS initialization failed with status: $status")
                        callback(false, "TTS初始化失败: $status")
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "TTS check failed", e)
            callback(false, "TTS检查异常: ${e.message}")
        }
    }
    
    /**
     * 获取TTS引擎信息
     */
    fun getTTSEngineInfo(context: Context): String {
        return try {
            val tts = TextToSpeech(context, null)
            val defaultEngine = tts.defaultEngine
            tts.shutdown()
            "默认TTS引擎: $defaultEngine"
        } catch (e: Exception) {
            "无法获取TTS引擎信息: ${e.message}"
        }
    }
    
    /**
     * 检查系统TTS设置
     */
    fun checkSystemTTSSettings(context: Context): List<String> {
        val info = mutableListOf<String>()
        
        try {
            // 检查TTS设置
            val intent = android.content.Intent("com.android.settings.TTS_SETTINGS")
            val activities = context.packageManager.queryIntentActivities(intent, 0)
            info.add("TTS设置可用: ${activities.isNotEmpty()}")
            
            // 检查语言设置
            val locale = Locale.getDefault()
            info.add("系统语言: ${locale.language}_${locale.country}")
            
            // 检查可用的TTS引擎
            val tts = TextToSpeech(context, null)
            val engines = tts.engines
            info.add("可用TTS引擎数量: ${engines.size}")
            engines.forEach { engine ->
                info.add("  - ${engine.name}: ${engine.label}")
            }
            tts.shutdown()
            
        } catch (e: Exception) {
            info.add("检查系统TTS设置时出错: ${e.message}")
        }
        
        return info
    }
}
