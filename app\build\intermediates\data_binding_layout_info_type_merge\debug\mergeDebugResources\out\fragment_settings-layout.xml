<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="com.example.word" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_settings_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="499" endOffset="39"/></Target><Target id="@+id/spinner_daily_goal" view="Spinner"><Expressions/><location startLine="52" startOffset="20" endLine="56" endOffset="50"/></Target><Target id="@+id/spinner_review_mode" view="Spinner"><Expressions/><location startLine="75" startOffset="20" endLine="79" endOffset="50"/></Target><Target id="@+id/switch_reminder" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="126" startOffset="20" endLine="129" endOffset="62"/></Target><Target id="@+id/layout_reminder_time" view="LinearLayout"><Expressions/><location startLine="134" startOffset="16" endLine="160" endOffset="30"/></Target><Target id="@+id/text_view_reminder_time" view="TextView"><Expressions/><location startLine="151" startOffset="20" endLine="158" endOffset="44"/></Target><Target id="@+id/switch_auto_play" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="205" startOffset="20" endLine="208" endOffset="62"/></Target><Target id="@+id/switch_slow_speed" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="227" startOffset="20" endLine="230" endOffset="62"/></Target><Target id="@+id/radio_group_theme" view="RadioGroup"><Expressions/><location startLine="261" startOffset="16" endLine="289" endOffset="28"/></Target><Target id="@+id/radio_button_system" view="RadioButton"><Expressions/><location startLine="267" startOffset="20" endLine="273" endOffset="48"/></Target><Target id="@+id/radio_button_light" view="RadioButton"><Expressions/><location startLine="275" startOffset="20" endLine="280" endOffset="49"/></Target><Target id="@+id/radio_button_dark" view="RadioButton"><Expressions/><location startLine="282" startOffset="20" endLine="287" endOffset="49"/></Target><Target id="@+id/switch_show_progress" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="334" startOffset="20" endLine="337" endOffset="62"/></Target><Target id="@+id/switch_show_streak" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="356" startOffset="20" endLine="359" endOffset="62"/></Target><Target id="@+id/switch_auto_backup" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="406" startOffset="20" endLine="409" endOffset="62"/></Target><Target id="@+id/button_export_data" view="Button"><Expressions/><location startLine="419" startOffset="20" endLine="427" endOffset="49"/></Target><Target id="@+id/button_import_data" view="Button"><Expressions/><location startLine="429" startOffset="20" endLine="436" endOffset="49"/></Target><Target id="@+id/button_clear_data" view="Button"><Expressions/><location startLine="440" startOffset="16" endLine="448" endOffset="45"/></Target><Target id="@+id/button_about" view="Button"><Expressions/><location startLine="476" startOffset="16" endLine="482" endOffset="45"/></Target><Target id="@+id/buttonTestTTS" view="Button"><Expressions/><location startLine="484" startOffset="16" endLine="491" endOffset="45"/></Target></Targets></Layout>