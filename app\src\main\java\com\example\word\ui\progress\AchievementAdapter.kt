package com.example.word.ui.progress

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.word.R
import com.example.word.databinding.ItemAchievementBinding
import com.example.word.utils.GameificationManager

/**
 * 成就列表适配器
 */
class AchievementAdapter : ListAdapter<GameificationManager.Achievement, AchievementAdapter.AchievementViewHolder>(AchievementDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AchievementViewHolder {
        val binding = ItemAchievementBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AchievementViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: AchievementViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class AchievementViewHolder(
        private val binding: ItemAchievementBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(achievement: GameificationManager.Achievement) {
            binding.apply {
                textViewTitle.text = achievement.title
                textViewDescription.text = achievement.description

                // 设置进度
                progressBarAchievement.max = achievement.targetValue
                progressBarAchievement.progress = achievement.currentValue
                textViewProgress.text = "${achievement.currentValue}/${achievement.targetValue}"

                // 设置解锁状态
                if (achievement.isUnlocked) {
                    imageViewIcon.setImageResource(R.drawable.ic_achievement_unlocked)
                    imageViewIcon.alpha = 1.0f
                    textViewTitle.alpha = 1.0f
                    textViewDescription.alpha = 1.0f
                    root.alpha = 1.0f
                } else {
                    imageViewIcon.setImageResource(R.drawable.ic_achievement_locked)
                    imageViewIcon.alpha = 0.5f
                    textViewTitle.alpha = 0.5f
                    textViewDescription.alpha = 0.5f
                    root.alpha = 0.7f
                }
            }
        }
    }
    
    private class AchievementDiffCallback : DiffUtil.ItemCallback<GameificationManager.Achievement>() {
        override fun areItemsTheSame(oldItem: GameificationManager.Achievement, newItem: GameificationManager.Achievement): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: GameificationManager.Achievement, newItem: GameificationManager.Achievement): Boolean {
            return oldItem == newItem
        }
    }
}
