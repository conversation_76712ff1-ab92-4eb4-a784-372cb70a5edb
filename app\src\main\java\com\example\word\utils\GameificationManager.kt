package com.example.word.utils

import com.example.word.R
import com.example.word.data.entities.*

/**
 * 游戏化系统管理器
 * 负责处理积分、等级、成就等游戏化元素
 */
object GameificationManager {
    
    /**
     * 计算用户等级
     */
    fun calculateUserLevel(totalExperience: Int): UserLevel {
        val level = when {
            totalExperience < 100 -> 1
            totalExperience < 300 -> 2
            totalExperience < 600 -> 3
            totalExperience < 1000 -> 4
            totalExperience < 1500 -> 5
            totalExperience < 2100 -> 6
            totalExperience < 2800 -> 7
            totalExperience < 3600 -> 8
            totalExperience < 4500 -> 9
            totalExperience < 5500 -> 10
            else -> 10 + (totalExperience - 5500) / 1000
        }
        
        val experienceRequired = getExperienceRequiredForLevel(level + 1)
        val currentExperience = totalExperience - getExperienceRequiredForLevel(level)
        
        return UserLevel(
            level = level,
            title = getLevelTitle(level),
            experienceRequired = experienceRequired - getExperienceRequiredForLevel(level),
            currentExperience = currentExperience,
            iconResId = getLevelIcon(level)
        )
    }
    
    /**
     * 获取等级所需经验值
     */
    private fun getExperienceRequiredForLevel(level: Int): Int {
        return when {
            level <= 1 -> 0
            level == 2 -> 100
            level == 3 -> 300
            level == 4 -> 600
            level == 5 -> 1000
            level == 6 -> 1500
            level == 7 -> 2100
            level == 8 -> 2800
            level == 9 -> 3600
            level == 10 -> 4500
            level == 11 -> 5500
            else -> 5500 + (level - 11) * 1000
        }
    }
    
    /**
     * 获取等级称号
     */
    private fun getLevelTitle(level: Int): String {
        return when {
            level <= 1 -> "初学者"
            level <= 3 -> "学习者"
            level <= 5 -> "进步者"
            level <= 7 -> "熟练者"
            level <= 10 -> "专家"
            level <= 15 -> "大师"
            else -> "传奇"
        }
    }
    
    /**
     * 获取等级图标
     */
    private fun getLevelIcon(level: Int): Int {
        return when {
            level <= 1 -> R.drawable.ic_level_1
            level <= 3 -> R.drawable.ic_level_2
            level <= 5 -> R.drawable.ic_level_3
            level <= 7 -> R.drawable.ic_level_4
            level <= 10 -> R.drawable.ic_level_5
            else -> R.drawable.ic_level_master
        }
    }
    

    

    

    

    
    /**
     * 计算经验值奖励
     */
    fun calculateExperienceReward(type: ExperienceType, multiplier: Float = 1f): Int {
        return (type.points * multiplier).toInt()
    }
    
    /**
     * 检查是否有新成就解锁
     */
    fun checkNewAchievements(
        oldStats: StudyStats,
        newStats: StudyStats
    ): List<Achievement> {
        val newAchievements = mutableListOf<Achievement>()
        
        // 检查学习天数成就
        if (newStats.totalStudyDays > oldStats.totalStudyDays) {
            val achievements = createStudyDaysAchievements(newStats.totalStudyDays)
            newAchievements.addAll(achievements.filter { it.isUnlocked && it.currentValue == it.targetValue })
        }
        
        // 检查词汇量成就
        if (newStats.totalWordsLearned > oldStats.totalWordsLearned) {
            val achievements = createVocabularyAchievements(newStats.totalWordsLearned)
            newAchievements.addAll(achievements.filter { it.isUnlocked && it.currentValue == it.targetValue })
        }
        
        // 检查连续学习成就
        if (newStats.consecutiveStudyDays > oldStats.consecutiveStudyDays) {
            val achievements = createConsecutiveAchievements(newStats.consecutiveStudyDays)
            newAchievements.addAll(achievements.filter { it.isUnlocked && it.currentValue == it.targetValue })
        }
        
        return newAchievements
    }

    /**
     * 用户等级数据类
     */
    data class UserLevel(
        val level: Int,
        val title: String,
        val experienceRequired: Int,
        val currentExperience: Int,
        val iconResId: Int
    )

    /**
     * 成就数据类
     */
    data class Achievement(
        val id: String,
        val title: String,
        val description: String,
        val iconResId: Int,
        val targetValue: Int,
        val currentValue: Int,
        val isUnlocked: Boolean,
        val experienceReward: Int,
        val category: AchievementCategory
    )

    /**
     * 学习统计数据类
     */
    data class StudyStats(
        val totalStudyDays: Int = 0,
        val consecutiveStudyDays: Int = 0,
        val totalWordsLearned: Int = 0,
        val totalPhrasesLearned: Int = 0,
        val totalExperience: Int = 0
    )

    /**
     * 成就分类枚举
     */
    enum class AchievementCategory {
        STUDY_DAYS,
        VOCABULARY,
        CONSECUTIVE,
        EXPERIENCE
    }

    /**
     * 经验值类型枚举
     */
    enum class ExperienceType(val points: Int) {
        WORD_LEARNED(10),
        PHRASE_LEARNED(15),
        QUIZ_CORRECT(5),
        QUIZ_PERFECT(50),
        DAILY_GOAL_COMPLETED(25),
        STREAK_BONUS(20)
    }

    /**
     * 创建学习天数成就
     */
    fun createStudyDaysAchievements(studyDays: Int): List<Achievement> {
            val milestones = listOf(1, 7, 30, 100, 365)
            return milestones.map { milestone ->
                Achievement(
                    id = "study_days_$milestone",
                    title = "学习${milestone}天",
                    description = "累计学习${milestone}天",
                    iconResId = R.drawable.ic_achievement_unlocked,
                    targetValue = milestone,
                    currentValue = minOf(studyDays, milestone),
                    isUnlocked = studyDays >= milestone,
                    experienceReward = milestone * 10,
                    category = AchievementCategory.STUDY_DAYS
                )
            }
        }

        /**
         * 创建词汇量成就
         */
        fun createVocabularyAchievements(studiedWords: Int): List<Achievement> {
            val milestones = listOf(10, 50, 100, 500, 1000, 2000)
            return milestones.map { milestone ->
                Achievement(
                    id = "vocabulary_$milestone",
                    title = "词汇达人${milestone}",
                    description = "学习${milestone}个单词",
                    iconResId = R.drawable.ic_achievement_unlocked,
                    targetValue = milestone,
                    currentValue = minOf(studiedWords, milestone),
                    isUnlocked = studiedWords >= milestone,
                    experienceReward = milestone * 5,
                    category = AchievementCategory.VOCABULARY
                )
            }
        }

        /**
         * 创建连续学习成就
         */
        fun createConsecutiveAchievements(consecutiveDays: Int): List<Achievement> {
            val milestones = listOf(3, 7, 14, 30, 60)
            return milestones.map { milestone ->
                Achievement(
                    id = "consecutive_$milestone",
                    title = "坚持${milestone}天",
                    description = "连续学习${milestone}天",
                    iconResId = R.drawable.ic_achievement_unlocked,
                    targetValue = milestone,
                    currentValue = minOf(consecutiveDays, milestone),
                    isUnlocked = consecutiveDays >= milestone,
                    experienceReward = milestone * 15,
                    category = AchievementCategory.CONSECUTIVE
                )
            }
        }

        /**
         * 创建经验值成就
         */
        fun createExperienceAchievements(exp: Int): List<Achievement> {
            val milestones = listOf(100, 500, 1000, 5000, 10000)
            return milestones.map { milestone ->
                Achievement(
                    id = "experience_$milestone",
                    title = "经验${milestone}",
                    description = "获得${milestone}经验值",
                    iconResId = R.drawable.ic_achievement_unlocked,
                    targetValue = milestone,
                    currentValue = minOf(exp, milestone),
                    isUnlocked = exp >= milestone,
                    experienceReward = milestone / 10,
                    category = AchievementCategory.EXPERIENCE
                )
            }
        }
}
